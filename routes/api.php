<?php

use App\Http\Controllers\API\AuthController;
use App\Http\Controllers\API\FacilitiesController;
use App\Http\Controllers\API\FavoriteController;
use App\Http\Controllers\API\FriendsController;
use App\Http\Controllers\API\CoHostController;
use App\Http\Controllers\API\EarlyAccessController;
use App\Http\Controllers\API\SupportController;
use App\Http\Controllers\API\LegalController;
use App\Http\Controllers\API\AccountController;
use App\Http\Controllers\API\NotificationController;
use App\Http\Controllers\API\HostingResourceController;
use App\Http\Controllers\API\WalletController;
use App\Http\Controllers\API\SettingsController;
use App\Http\Controllers\API\GeneralController;
use App\Http\Controllers\API\ItemsController;
use App\Http\Controllers\API\PropertyTypesController;
use App\Http\Controllers\API\ReservationController;
use App\Http\Controllers\API\ReviewController;
use App\Http\Controllers\API\ServiceCategoryController;
use App\Http\Controllers\API\CancellationPolicyController;
use App\Http\Controllers\API\HostListingsController;
use App\Http\Controllers\HostController;
use Illuminate\Support\Facades\Route;

/*
  |--------------------------------------------------------------------------
  | API Routes
  |--------------------------------------------------------------------------
  |
  | Here is where you can register API routes for your application. These
  | routes are loaded by the RouteServiceProvider within a group which
  | is assigned the "api" middleware group. Enjoy building your API!
  |
 */

// api routes
Route::name('api.')->group(function () {

    // Auth
    Route::prefix('client')->group(function () {
        Route::post('/validate_otp', [AuthController::class, 'validate_otp']);
        Route::post('/resend_otp', [AuthController::class, 'resend_otp']);
        Route::post('/login', [AuthController::class, 'login']);
        Route::post('/login_token', [AuthController::class, 'login_token']);
        Route::post('/login_guest', [AuthController::class, 'login_guest']);
        Route::post('/test_sms', [AuthController::class, 'test_sms']); // Test SMS endpoint
    });
    Route::group(['middleware' => ['auth:api']], function () {
        // Protected client routes
        Route::prefix('client')->group(function () {
            Route::delete('/delete_account', [AuthController::class, 'delete_account']);
            Route::post('/logout', [AuthController::class, 'logout']);
            Route::post('/validate', [AuthController::class, 'validate_token']);
            Route::post('/edit_profile', [AuthController::class, 'edit_profile']);
            Route::post('/toggle_hoster_mode', [AuthController::class, 'toggle_hoster_mode']);
            Route::post('/follow', [AuthController::class, 'follow']);
            Route::delete('/unfollow', [AuthController::class, 'unfollow']);
            Route::get('/follow_list', [AuthController::class, 'follow_list']);
            Route::get('/info', [AuthController::class, 'client_info']);
            Route::post('/notification', [AuthController::class, 'notification']);
        });
        // Settings
        Route::get('/settings', [GeneralController::class, 'settings']);

        // Favorite
        Route::prefix('favorite')->group(function () {
            Route::get('/list', [FavoriteController::class, 'list']);
            Route::post('/set', [FavoriteController::class, 'set']);
        });
        // Reservation
        Route::prefix('reservations')->group(function () {
            Route::get('/list', [ReservationController::class, 'list']);
            Route::post('/check', [ReservationController::class, 'check']);
            Route::post('/create', [ReservationController::class, 'create']);
            Route::get('/{id}', [ReservationController::class, 'show']);
            Route::delete('/cancel/{id}', [ReservationController::class, 'cancel']);
            Route::post('/confirm/{id}', [ReservationController::class, 'confirm'])->middleware('user.host');
        });

        // General
        Route::prefix('general')->group(function () {
            Route::post('/cities', [GeneralController::class, 'cities_geo']);
        });
        // Facilities
        Route::prefix('facilities')->group(function () {
            Route::get('/list', [FacilitiesController::class, 'get_list']);
        });
        // Cancellation Policies
        Route::prefix('cancellation-policies')->group(function () {
            Route::get('/', [CancellationPolicyController::class, 'index']);
            Route::get('/{id}', [CancellationPolicyController::class, 'show']);
            // Admin routes (add middleware as needed)
            Route::post('/', [CancellationPolicyController::class, 'store']);
            Route::put('/{id}', [CancellationPolicyController::class, 'update']);
            Route::delete('/{id}', [CancellationPolicyController::class, 'destroy']);
        });
        // Property Types
        Route::prefix('property_types')->group(function () {
            Route::get('/list', [PropertyTypesController::class, 'get_list']);
        });
        // Service Category
        Route::prefix('service_categories')->group(function () {
            Route::get('/list', [ServiceCategoryController::class, 'get_list']);
        });
        // Items
        Route::prefix('items')->group(function () {
            Route::get('/list', [ItemsController::class, 'get_list']);
            Route::get('/search', [ItemsController::class, 'search']);
            Route::get('/{id}', [ItemsController::class, 'show']);
            Route::post('/create', [ItemsController::class, 'create'])->middleware('user.host');
            Route::put('/update/{id}', [ItemsController::class, 'update'])->middleware(['user.host', 'item.owned']);
            Route::post('/upload-gallery/{id}', [ItemsController::class, 'uploadGallery'])->middleware(['user.host', 'item.owned']);
        });

        // Host Dashboard
        Route::prefix('host')->middleware('user.host')->group(function () {
            Route::get('/dashboard', [HostController::class, 'dashboard']);
            Route::get('/financial-summary', [HostController::class, 'financialSummary']);
            Route::get('/reservations', [HostController::class, 'reservations']);
            Route::get('/reviews', [HostController::class, 'reviews']);

            // Host Listings Management
            Route::prefix('listings')->group(function () {
                Route::get('/', [HostListingsController::class, 'index']);
                Route::get('/stats', [HostListingsController::class, 'stats']);
                Route::post('/toggle-status/{id}', [HostListingsController::class, 'toggleStatus']);
                Route::delete('/delete/{id}', [HostListingsController::class, 'delete']);
                Route::post('/duplicate/{id}', [HostListingsController::class, 'duplicate']);
                Route::post('/bulk-status', [HostListingsController::class, 'bulkUpdateStatus']);
                Route::post('/bulk-delete', [HostListingsController::class, 'bulkDelete']);
            });

            // Property Creation and Management
            Route::prefix('properties')->group(function () {
                Route::post('/', [ItemsController::class, 'create']); // Create property
                Route::get('/metadata', [HostListingsController::class, 'getCreationMetadata']);
            });

            // Property Drafts
            Route::prefix('property-drafts')->group(function () {
                Route::post('/', [HostListingsController::class, 'saveDraft']);
                Route::get('/{id}', [HostListingsController::class, 'loadDraft']);
                Route::put('/{id}', [HostListingsController::class, 'updateDraft']);
                Route::delete('/{id}', [HostListingsController::class, 'deleteDraft']);
            });

            // Withdrawal Management
            Route::prefix('withdrawal')->group(function () {
                Route::post('/request', [HostController::class, 'requestWithdrawal']);
                Route::get('/history', [HostController::class, 'withdrawalHistory']);
                Route::get('/methods', [HostController::class, 'withdrawalMethods']);
                Route::delete('/cancel/{id}', [HostController::class, 'cancelWithdrawal']);
            });
        });

        // Reviews
        Route::prefix('reviews')->group(function () {
            Route::get('/', [ReviewController::class, 'index']);
            Route::get('/stats/{propertyId}', [ReviewController::class, 'stats']);
            Route::post('/', [ReviewController::class, 'store']);
            Route::put('/{reviewId}', [ReviewController::class, 'update']);
            Route::delete('/{reviewId}', [ReviewController::class, 'destroy']);
            Route::get('/user', [ReviewController::class, 'userReviews']);
            Route::get('/host', [ReviewController::class, 'hostReviews'])->middleware('user.host');
            Route::post('/{reviewId}/like', [ReviewController::class, 'toggleLike']);
            Route::post('/{reviewId}/report', [ReviewController::class, 'report']);
            Route::get('/can-review', [ReviewController::class, 'canReview']);
        });

        // Friends/Knowledge System
        Route::prefix('friends')->group(function () {
            Route::get('/', [FriendsController::class, 'index']);
            Route::get('/pending-requests', [FriendsController::class, 'pendingRequests']);
            Route::post('/send-request', [FriendsController::class, 'sendRequest']);
            Route::post('/accept-request', [FriendsController::class, 'acceptRequest']);
            Route::post('/decline-request', [FriendsController::class, 'declineRequest']);
            Route::delete('/remove', [FriendsController::class, 'removeFriend']);
            Route::get('/search', [FriendsController::class, 'searchUsers']);
        });

        // Co-Host System
        Route::prefix('co-hosts')->middleware('user.host')->group(function () {
            Route::get('/', [CoHostController::class, 'index']);
            Route::get('/pending-invitations', [CoHostController::class, 'pendingInvitations']);
            Route::post('/send-invitation', [CoHostController::class, 'sendInvitation']);
            Route::post('/accept-invitation', [CoHostController::class, 'acceptInvitation']);
            Route::post('/decline-invitation', [CoHostController::class, 'declineInvitation']);
            Route::delete('/remove', [CoHostController::class, 'removeCoHost']);
            Route::get('/search', [CoHostController::class, 'searchCoHosts']);
            Route::get('/permissions', [CoHostController::class, 'getPermissions']);
        });

        // Early Access Features System
        Route::prefix('early-access')->group(function () {
            Route::get('/', [EarlyAccessController::class, 'index']);
            Route::get('/my-features', [EarlyAccessController::class, 'myFeatures']);
            Route::post('/enroll', [EarlyAccessController::class, 'enroll']);
            Route::post('/unenroll', [EarlyAccessController::class, 'unenroll']);
            Route::post('/feedback', [EarlyAccessController::class, 'submitFeedback']);
            Route::post('/mark-used', [EarlyAccessController::class, 'markAsUsed']);
            Route::get('/stats', [EarlyAccessController::class, 'getFeatureStats']);
        });

        // Support Center
        Route::prefix('support')->group(function () {
            Route::get('/faqs', [SupportController::class, 'getFaqs']);
            Route::get('/tickets', [SupportController::class, 'getTickets']);
            Route::post('/tickets', [SupportController::class, 'createTicket']);
            Route::get('/categories', [SupportController::class, 'getCategories']);
        });

        // Legal Documents
        Route::prefix('legal')->group(function () {
            Route::get('/', [LegalController::class, 'index']);
            Route::get('/terms', [LegalController::class, 'getTerms']);
            Route::get('/privacy', [LegalController::class, 'getPrivacyPolicy']);
            Route::get('/cookies', [LegalController::class, 'getCookiePolicy']);
            Route::get('/user-agreement', [LegalController::class, 'getUserAgreement']);
            Route::get('/host-agreement', [LegalController::class, 'getHostAgreement']);
            Route::get('/{type}', [LegalController::class, 'getByType']);
        });

        // Account Management
        Route::prefix('account')->group(function () {
            Route::get('/info', [AccountController::class, 'getAccountInfo']);
            Route::put('/info', [AccountController::class, 'updateAccountInfo']);
            Route::post('/change-password', [AccountController::class, 'changePassword']);
            Route::get('/security', [AccountController::class, 'getSecuritySettings']);
            Route::post('/deactivate', [AccountController::class, 'deactivateAccount']);
            Route::get('/export-data', [AccountController::class, 'exportAccountData']);
        });

        // Notifications
        Route::prefix('notifications')->group(function () {
            Route::get('/', [NotificationController::class, 'index']);
            Route::post('/{id}/read', [NotificationController::class, 'markAsRead']);
            Route::post('/mark-all-read', [NotificationController::class, 'markAllAsRead']);
            Route::delete('/{id}', [NotificationController::class, 'destroy']);
            Route::get('/preferences', [NotificationController::class, 'getPreferences']);
            Route::put('/preferences', [NotificationController::class, 'updatePreferences']);
            Route::get('/stats', [NotificationController::class, 'getStats']);
            Route::post('/test', [NotificationController::class, 'createTestNotification']);
        });

        // Hosting Resources
        Route::prefix('hosting-resources')->group(function () {
            Route::get('/', [HostingResourceController::class, 'index']);
            Route::get('/categories', [HostingResourceController::class, 'getCategories']);
            Route::get('/featured', [HostingResourceController::class, 'getFeatured']);
            Route::get('/search', [HostingResourceController::class, 'search']);
            Route::get('/{id}', [HostingResourceController::class, 'show']);
        });

        // Wallet & Financial
        Route::prefix('wallet')->group(function () {
            Route::get('/balance', [WalletController::class, 'getBalance']);
            Route::get('/transactions', [WalletController::class, 'getTransactions']);
            Route::post('/withdraw', [WalletController::class, 'requestWithdrawal']);
            Route::get('/withdrawals', [WalletController::class, 'getWithdrawalRequests']);
            Route::get('/analytics', [WalletController::class, 'getEarningsAnalytics']);
        });

        // Settings & Preferences
        Route::prefix('settings')->group(function () {
            Route::get('/', [SettingsController::class, 'getSettings']);
            Route::put('/', [SettingsController::class, 'updateSettings']);
            Route::get('/{category}', [SettingsController::class, 'getCategorySettings']);
            Route::post('/reset', [SettingsController::class, 'resetSettings']);
            Route::get('/export', [SettingsController::class, 'exportSettings']);
            Route::post('/import', [SettingsController::class, 'importSettings']);
        });

        // File Upload
        Route::prefix('upload')->group(function () {
            Route::post('/image', [HostListingsController::class, 'uploadImage']);
            Route::post('/video', [HostListingsController::class, 'uploadVideo']);
        });

        // Cancellation Policies
        Route::prefix('cancellation-policies')->group(function () {
            Route::get('/', [CancellationPolicyController::class, 'index']);
            Route::get('/{id}', [CancellationPolicyController::class, 'show']);
        });
    });
});

Route::fallback(function () {
    abort(404, 'API resource not found');
});
