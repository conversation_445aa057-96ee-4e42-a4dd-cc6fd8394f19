# Cancellation Policy CRUD Implementation

## Overview
This implementation provides a complete CRUD system for cancellation policies that handles the specific requirements for different booking durations and policy types.

## Policy Types Implemented

### Short-term Bookings (≤28 days)
- **مرنة (Flexible)**: استرداد كامل قبل يوم واحد من الوصول
- **معتدلة (Moderate)**: استرداد كامل قبل يومين من الوصول  
- **مشددة (Strict)**: استرداد كامل قبل 5 أيام من الوصول

### Long-term Bookings (>28 days)
- **معتدلة (Moderate)**: استرداد 50% من المبلغ في خلال 7 أيام من تاريخ الوصول
- **مشددة (Strict)**: المبلغ المدفوع يتم رده في حالة إلغاء الحجز في خلال ثلاثة أيام من تاريخ الحجز على أن يكون الإلغاء قبل موعد الحجز بمدة لا تقل عن 14 يوم

## Backend Files Created

### 1. Model
- `app/Models/Admin/CancellationPolicy.php`
- Includes calculation methods for refunds
- Supports both short-term and long-term policies

### 2. Migrations
- `database/migrations/2024_01_20_000000_create_cancellation_policies_table.php`
- `database/migrations/2024_01_20_000001_add_cancellation_policy_id_to_service_category_items.php`

### 3. Controller & Resource
- `app/Http/Controllers/API/CancellationPolicyController.php`
- `app/Http/Resources/CancellationPolicyResource.php`

### 4. Seeder
- `database/seeders/CancellationPolicySeeder.php`
- Pre-populates the 5 policy types

### 5. Routes
- Added to `routes/api.php`:
  ```php
  Route::prefix('cancellation-policies')->group(function () {
      Route::get('/', [CancellationPolicyController::class, 'index']);
      Route::get('/{id}', [CancellationPolicyController::class, 'show']);
      Route::post('/', [CancellationPolicyController::class, 'store']);
      Route::put('/{id}', [CancellationPolicyController::class, 'update']);
      Route::delete('/{id}', [CancellationPolicyController::class, 'destroy']);
  });
  ```

## Frontend Files Created

### 1. Model
- `lib/feature/cancellation_policies/data/models/cancellation_policy_model.dart`
- Includes helper methods for formatting and validation

### 2. API Service
- `lib/feature/cancellation_policies/data/services/cancellation_policy_api_service.dart`
- Full CRUD operations

### 3. Widgets
- `lib/feature/cancellation_policies/presentation/widgets/cancellation_policy_selector.dart`
- `lib/feature/cancellation_policies/presentation/widgets/cancellation_policy_form_field.dart`

### 4. Updated Files
- `lib/feature/home/<USER>/models/place_detail_model.dart` - Added cancellation policy field
- `lib/feature/home/<USER>/views/place_details_screen.dart` - Enhanced display
- `lib/core/databases/api/end_points.dart` - Added new endpoints
- Localization files with new strings

## API Endpoints

### Public Endpoints
- `GET /api/cancellation-policies` - List all active policies
- `GET /api/cancellation-policies/{id}` - Get specific policy

### Admin Endpoints
- `POST /api/cancellation-policies` - Create new policy
- `PUT /api/cancellation-policies/{id}` - Update policy
- `DELETE /api/cancellation-policies/{id}` - Delete policy

## Database Schema

### cancellation_policies table
```sql
- id (bigint, primary key)
- name_en (string)
- name_ar (string) 
- description_en (text)
- description_ar (text)
- policy_type (enum: flexible_short, moderate_short, strict_short, moderate_long, strict_long, custom)
- duration_type (enum: short, long, both)
- cancellation_window_hours (integer)
- refund_percentage (decimal)
- booking_window_hours (integer, nullable)
- minimum_notice_hours (integer, nullable)
- service_fee_refundable (boolean)
- cleaning_fee_refundable (boolean)
- is_active (boolean)
- order (integer)
- timestamps
- soft deletes
```

### service_category_items table (updated)
```sql
- cancelation_policy_id (foreign key to cancellation_policies.id)
- cancelation_rules (text, kept for backward compatibility)
```

## Deployment Steps

1. **Upload backend files** to production server
2. **Run migrations**:
   ```bash
   php artisan migrate
   ```
3. **Seed policies**:
   ```bash
   php artisan db:seed --class=CancellationPolicySeeder
   ```
4. **Clear cache**:
   ```bash
   php artisan config:clear
   php artisan route:clear
   php artisan cache:clear
   ```

## Usage Examples

### In Property Creation Form
```dart
CancellationPolicyFormField(
  onChanged: (policy) {
    setState(() {
      selectedCancellationPolicy = policy;
    });
  },
  apiService: getIt<CancellationPolicyApiService>(),
  isRequired: true,
)
```

### In Place Details Display
The place details screen automatically displays the cancellation policy with:
- Policy type badge
- Formatted description
- Refund percentage
- Time windows
- Informational notes

## Features

✅ **Multi-language Support** - Arabic/English with proper RTL  
✅ **Duration-based Policies** - Different rules for short/long-term bookings  
✅ **Smart Calculation** - Backend methods for refund calculations  
✅ **Backward Compatibility** - Supports legacy cancelation_rules field  
✅ **Admin Management** - Full CRUD for policy management  
✅ **User-friendly Display** - Enhanced UI with badges and formatting  
✅ **Validation** - Proper form validation and error handling  

## Error Fixes Applied

- Fixed `CancellationPolicyResource::toArray()` method signature compatibility
- Updated import paths for DioConsumer
- Added proper endpoint constants
- Ensured all model fields are properly initialized

The system is now ready for production deployment and provides a comprehensive solution for managing cancellation policies according to your specific requirements.
