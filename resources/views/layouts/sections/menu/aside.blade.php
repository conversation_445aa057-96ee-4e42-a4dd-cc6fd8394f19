@php
    $configData = Helper::appClasses();
@endphp

<aside id="layout-menu" class="layout-menu menu-vertical menu bg-menu-theme">

    <!-- ! Hide app brand if navbar-full -->
    @if (!isset($navbarFull))
        <div class="app-brand demo">
            <a href="{{ url('/') }}" class="app-brand-link">
                <span class="app-brand-logo demo">
                    @include('_partials.macros', ['height' => 20])
                </span>
                <span class="app-brand-text demo menu-text fw-bold">{{ config('variables.templateName') }}</span>
            </a>

            <a href="javascript:void(0);" class="layout-menu-toggle menu-link text-large ms-auto">
                <i class="ti menu-toggle-icon d-none d-xl-block ti-sm align-middle"></i>
                <i class="ti ti-x d-block d-xl-none ti-sm align-middle"></i>
            </a>
        </div>
    @endif

    <div class="menu-inner-shadow"></div>

    <ul class="menu-inner py-1">

        {{-- Main Menu --}}

        <!-- Dashboard Analytics -->
        <li class="menu-item {{ isActiveRoute('dashboard') }}">
            <a href="{{ url('/') }}" class="menu-link">
                <i class="menu-icon tf-icons ti ti-smart-home"></i>
                <div>{{ __('dashboard') }}</div>
            </a>
        </li>

        <!-- Roles & Users -->
        @canany(['read users', 'create users', 'update users', 'read roles', 'create roles', 'update roles'])
            <li
                class="menu-item {{ isActiveRoute(['roles.index', 'roles.create', 'roles.edit', 'users.index', 'users.create', 'users.edit'], 'active open') }}">
                <a href="javascript:void(0);" class="menu-link menu-toggle">
                    <i class="menu-icon tf-icons ti ti-users"></i>
                    <div>{{ __('User Management') }}</div>
                </a>

                <ul class="menu-sub">
                    @can('read roles')
                        <li class="menu-item {{ isActiveRoute(['roles.index', 'roles.create', 'roles.edit']) }}">
                            <a href="{{ route('roles.index') }}" class="menu-link">
                                <div>{{ __('roles') }}</div>
                            </a>
                        </li>
                    @endcan

                    @can('read users')
                        <li class="menu-item {{ isActiveRoute(['users.index', 'users.create', 'users.edit']) }}">
                            <a href="{{ route('users.index') }}" class="menu-link">
                                <div>{{ __('users') }}</div>
                            </a>
                        </li>
                    @endcan
                </ul>
            </li>
        @endcanany

        <!-- Attendance -->
        @can('read attendances')
            <li class="menu-item {{ isActiveRoute(['attendances.index', 'attendances.create', 'attendances.edit']) }}">
                <a href="{{ route('attendances.index') }}" class="menu-link">
                    <i class="menu-icon tf-icons ti ti-clock"></i>
                    <div>{{ __('attendances') }}</div>
                </a>
            </li>
        @endcan
        <!-- Attendance -->
        @can('read service_category_items')
            <li
                class="menu-item {{ isActiveRoute(['service_category_items.index', 'service_category_items.create', 'service_category_items.edit']) }}">
                <a href="{{ route('service_category_items.index') }}" class="menu-link">
                    <i class="menu-icon tf-icons ti ti-leaf"></i>
                    <div>{{ __('service_category_items') }}</div>
                </a>
            </li>
        @endcan

        <!-- Transactions -->
        @can('read transactions')
            <li class="menu-item {{ isActiveRoute(['transactions.index', 'transactions.create', 'transactions.edit']) }}">
                <a href="{{ route('transactions.index') }}" class="menu-link">
                    <i class="menu-icon tf-icons ti ti-exchange"></i>
                    <div>{{ __('transactions') }}</div>
                </a>
            </li>
        @endcan

        <!-- Invoice -->
        @can('read invoices')
            <li class="menu-item {{ isActiveRoute(['invoices.index', 'invoices.create']) }}">
                <a href="{{ route('invoices.index') }}" class="menu-link">
                    <i class="menu-icon tf-icons ti ti-receipt"></i>
                    <div>{{ __('invoices') }}</div>
                </a>
            </li>
        @endcan

        <!-- Reports -->
        @canany(['read report_cashier', 'read report_cashier_in_out', 'read audit_log'])
            <li
                class="menu-item {{ isActiveRoute(['reports.cashier.index', 'reports.cashier_in_out.index', 'reports.audit_log.index'], 'active open') }}">
                <a href="javascript:void(0);" class="menu-link menu-toggle">
                    <i class="menu-icon tf-icons ti ti-file-analytics"></i>
                    <div>{{ __('reports') }}</div>
                </a>

                <ul class="menu-sub">
                    @can('read report_cashier')
                        <li class="menu-item {{ isActiveRoute('reports.cashier.index') }}">
                            <a href="{{ route('reports.cashier.index') }}" class="menu-link">
                                <div>{{ __('report_cashier') }}</div>
                            </a>
                        </li>
                    @endcan

                    @can('read report_cashier_in_out')
                        <li class="menu-item {{ isActiveRoute('reports.cashier_in_out.index') }}">
                            <a href="{{ route('reports.cashier_in_out.index') }}" class="menu-link">
                                <div>{{ __('report_cashier_in_out') }}</div>
                            </a>
                        </li>
                    @endcan

                    @can('read audit_log')
                        <li class="menu-item {{ isActiveRoute('reports.audit_log.index') }}">
                            <a href="{{ route('reports.audit_log.index') }}" class="menu-link">
                                <div>{{ __('audit_log') }}</div>
                            </a>
                        </li>
                    @endcan
                </ul>
            </li>
        @endcanany
        <!-- Settings -->
        @canany(['read email_setting', 'read credential_setting', 'read sms_setting', 'read payment_gateway_settings',
            'read settings', 'read financial_categories', 'read payment_methods', 'read countries', 'read cities', 'read
            service_categories', 'read service_categories_documents', 'read service_categories_fields', 'read
            facilities_categories', 'read facilities', 'read property_types'])
            <li
                class="menu-item {{ isActiveRoute(['settings.email.edit', 'settings.credential.edit', 'settings.sms.edit', 'settings.payment_gateway.edit', 'settings.edit', 'financial_categories.index', 'financial_categories.create', 'financial_categories.edit', 'payment_methods.index', 'payment_methods.create', 'payment_methods.edit', 'countries.index', 'countries.create', 'countries.edit', 'cities.index', 'cities.create', 'cities.edit', 'service_categories.index', 'service_categories.create', 'service_categories.edit', 'service_categories_forms.index', 'service_categories_forms.create', 'service_categories_forms.edit', 'service_categories_documents.index', 'service_categories_documents.create', 'service_categories_documents.edit', 'service_categories_fields.index', 'service_categories_fields.create', 'service_categories_fields.edit', 'facilities_categories.index', 'facilities_categories.create', 'facilities_categories.edit', 'facilities.index', 'facilities.create', 'facilities.edit','property_types.index', 'property_types.create', 'property_types.edit'], 'active open') }}">
                <a href="javascript:void(0);" class="menu-link menu-toggle">
                    <i class="menu-icon tf-icons ti ti-settings"></i>
                    <div>{{ __('settings') }}</div>
                </a>

                <ul class="menu-sub">
                    @can('read settings')
                        <li class="menu-item {{ isActiveRoute('settings.edit') }}">
                            <a href="{{ route('settings.edit') }}" class="menu-link">
                                <div>{{ __('settings') }}</div>
                            </a>
                        </li>
                    @endcan

                    @can('read email_setting')
                        <li class="menu-item {{ isActiveRoute('settings.email.edit') }}">
                            <a href="{{ route('settings.email.edit') }}" class="menu-link">
                                <div>{{ __('email_setting') }}</div>
                            </a>
                        </li>
                    @endcan

                    @can('read credential_setting')
                        <li class="menu-item {{ isActiveRoute('settings.credential.edit') }}">
                            <a href="{{ route('settings.credential.edit') }}" class="menu-link">
                                <div>{{ __('credential_setting') }}</div>
                            </a>
                        </li>
                    @endcan

                    @can('read sms_setting')
                        <li class="menu-item {{ isActiveRoute('settings.sms.edit') }}">
                            <a href="{{ route('settings.sms.edit') }}" class="menu-link">
                                <div>{{ __('sms_setting') }}</div>
                            </a>
                        </li>
                    @endcan

                    @can('read payment_gateway_settings')
                        <li class="menu-item {{ isActiveRoute('settings.payment_gateway.edit') }}">
                            <a href="{{ route('settings.payment_gateway.edit') }}" class="menu-link">
                                <div>{{ __('payment_gateway_setting') }}</div>
                            </a>
                        </li>
                    @endcan

                    @can('read financial_categories')
                        <li
                            class="menu-item {{ isActiveRoute(['financial_categories.index', 'financial_categories.create', 'financial_categories.edit']) }}">
                            <a href="{{ route('financial_categories.index') }}" class="menu-link">
                                <div>{{ __('financial_categories') }}</div>
                            </a>
                        </li>
                    @endcan

                    @can('read payment_methods')
                        <li
                            class="menu-item {{ isActiveRoute(['payment_methods.index', 'payment_methods.create', 'payment_methods.edit']) }}">
                            <a href="{{ route('payment_methods.index') }}" class="menu-link">
                                <div>{{ __('payment_methods') }}</div>
                            </a>
                        </li>
                    @endcan

                    @can('read countries')
                        <li class="menu-item {{ isActiveRoute(['countries.index', 'countries.create', 'countries.edit']) }}">
                            <a href="{{ route('countries.index') }}" class="menu-link">
                                <div>{{ __('countries') }}</div>
                            </a>
                        </li>
                    @endcan

                    @can('read cities')
                        <li class="menu-item {{ isActiveRoute(['cities.index', 'cities.create', 'cities.edit']) }}">
                            <a href="{{ route('cities.index') }}" class="menu-link">
                                <div>{{ __('cities') }}</div>
                            </a>
                        </li>
                    @endcan

                    @can('read service_categories')
                        <li
                            class="menu-item {{ isActiveRoute(['service_categories.index', 'service_categories.create', 'service_categories.edit', 'service_categories_forms.index', 'service_categories_forms.create', 'service_categories_forms.edit']) }}">
                            <a href="{{ route('service_categories.index') }}" class="menu-link">
                                <div>{{ __('service_categories') }}</div>
                            </a>
                        </li>
                    @endcan

                    @can('read service_categories_documents')
                        <li
                            class="menu-item {{ isActiveRoute(['service_categories_documents.index', 'service_categories_documents.create', 'service_categories_documents.edit']) }}">
                            <a href="{{ route('service_categories_documents.index') }}" class="menu-link">
                                <div>{{ __('service_categories_documents') }}</div>
                            </a>
                        </li>
                    @endcan

                    @can('read service_categories_fields')
                        <li
                            class="menu-item {{ isActiveRoute(['service_categories_fields.index', 'service_categories_fields.create', 'service_categories_fields.edit']) }}">
                            <a href="{{ route('service_categories_fields.index') }}" class="menu-link">
                                <div>{{ __('service_categories_fields') }}</div>
                            </a>
                        </li>
                    @endcan

                    @can('read facilities_categories')
                        <li
                            class="menu-item {{ isActiveRoute(['facilities_categories.index', 'facilities_categories.create', 'facilities_categories.edit']) }}">
                            <a href="{{ route('facilities_categories.index') }}" class="menu-link">
                                <i class="menu-icon tf-icons ti ti-category"></i>
                                <div>{{ __('facilities_categories') }}</div>
                            </a>
                        </li>
                    @endcan

                    @can('read facilities')
                        <li
                            class="menu-item {{ isActiveRoute(['facilities.index', 'facilities.create', 'facilities.edit']) }}">
                            <a href="{{ route('facilities.index') }}" class="menu-link">
                                <div>{{ __('facilities') }}</div>
                            </a>
                        </li>
                    @endcan

                    @can('read property_types')
                        <li class="menu-item {{ isActiveRoute(['property_types.index', 'property_types.create', 'property_types.edit']) }}">
                            <a href="{{ route('property_types.index') }}" class="menu-link">
                                <div>{{ __('property_type') }}</div>
                            </a>
                        </li>
                    @endcan
                </ul>
            </li>
        @endcanany

    </ul>

</aside>

@php
    function isActiveRoute($routes, $class = 'active')
    {
        $currentRoute = Route::currentRouteName();
        if (is_array($routes)) {
            return in_array($currentRoute, $routes) ? $class : '';
        }
        return $currentRoute === $routes ? $class : '';
    }
@endphp
