@php
    $configData = Helper::appClasses();
@endphp

@extends('layouts/layoutMaster')

@section('title', __('add') . ' ' . __($nameS))

@section('content')
    <div class="row g-4">
        <div class="col-12">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="card-title mt-2"> {{ __('add') . ' ' . __($nameS) }}</h4>
                    <a href="{{ route($name . '.index') }}" class="btn btn-outline-primary float-right">
                        <i class="fas fa-arrow-left mr-1"></i> {{ __('back') }}
                    </a>
                </div>
            </div>
            <div class="card">
                <div class="card-body">
                    @include('_partials._errors')
                    <form action="{{ route($name . '.store') }}" method="POST">
                        @csrf
                        <div class="row">
                            <div class="col-4 mb-4">
                                <label class="form-label required" for="title_en">{{ __('title_en') }} <i class="required ml-sm">*</i></label>
                                <input type="text" id="title_en" name="title_en" class="form-control" placeholder="{{ __('title_en') }}" value="{{ old('title_en') }}" />
                            </div>
                            <div class="col-4 mb-4">
                                <label class="form-label required" for="title_ar">{{ __('title_ar') }} <i class="required ml-sm">*</i></label>
                                <input type="text" id="title_ar" name="title_ar" class="form-control" placeholder="{{ __('title_ar') }}" value="{{ old('title_ar') }}" />
                            </div>
                            <div class="col-4 mb-4">
                                <label class="form-label" for="order">{{ __('order') }}</label>
                                <input type="number" id="order" name="order" class="form-control" placeholder="{{ __('order') }}" value="{{ old('order') }}" />
                            </div>
                        </div>
                        <div class="col-12 text-center mt-4">
                            <button type="submit" class="btn btn-primary me-sm-3 me-1">{{ __('submit') }}</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
