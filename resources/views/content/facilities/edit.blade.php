@php
    $configData = Helper::appClasses();
@endphp

@extends('layouts/layoutMaster')

@section('title', __('edit') . ' ' . __($nameS))

@section('vendor-style')
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/datatables-bs5/datatables.bootstrap5.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/datatables-buttons-bs5/buttons.bootstrap5.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/select2/select2.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/formvalidation/dist/css/formValidation.min.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/animate-css/animate.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.css') }}" />
@endsection

@section('vendor-script')
    <script src="{{ asset('assets/vendor/libs/moment/moment.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/select2/select2.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/FormValidation.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/plugins/Bootstrap5.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/plugins/AutoFocus.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave-phone.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.js') }}"></script>
@endsection

@section('content')
    <div class="row g-4">
        <div class="col-12">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="card-title mt-2"> {{ __('edit') . ' ' . __($nameS) }}</h4>
                    <a href="{{ route($name . '.index') }}" class="btn btn-outline-primary float-right">
                        <i class="fas fa-arrow-left mr-1"></i> {{ __('back') }}
                    </a>
                </div>
            </div>
            <div class="card">
                <div class="card-body">
                    @include('_partials._errors')
                    <form action="{{ route($name . '.update', $item->id) }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')
                        <div class="row">
                            <div class="col-6 mb-4">
                                <label class="form-label required" for="title_en">{{ __('title_en') }} <i
                                        class="required ml-sm">*</i></label>
                                <input type="text" id="title_en" name="title_en" class="form-control"
                                    placeholder="{{ __('title_en') }}" value="{{ old('title_en', $item->title_en) }}" />
                            </div>
                            <div class="col-6 mb-4">
                                <label class="form-label required" for="title_ar">{{ __('title_ar') }} <i
                                        class="required ml-sm">*</i></label>
                                <input type="text" id="title_ar" name="title_ar" class="form-control"
                                    placeholder="{{ __('title_ar') }}" value="{{ old('title_ar', $item->title_ar) }}" />
                            </div>
                            <div class="col-4 mb-4">
                                <label class="form-label required" for="icon">{{ __('icon') }} <i
                                        class="required ml-sm">*</i></label>
                                <input type="file" id="icon" name="icon" class="form-control" accept="image/*" />
                                @if (isset($item) && $item->icon)
                                    <div class="mt-2">
                                        <img src="{{ asset('storage/' . $item->icon) }}" alt="Icon" style="max-width: 100px;" />
                                    </div>
                                @endif
                            </div>
                            <div class="col-4 mb-4">
                                <label class="form-label" for="order">{{ __('order') }}</label>
                                <input type="number" id="order" name="order" class="form-control"
                                    placeholder="{{ __('order') }}" value="{{ old('order', $item->order) }}" />
                            </div>
                            <div class="col-4 mb-4">
                                <label class="form-label" for="have_count">{{ __('have_count') }}</label>
                                <select id="have_count" name="have_count" class="form-select">
                                    <option value="0" {{ old('have_count', $item->have_count) == 0 ? 'selected' : '' }}>@lang('no')</option>
                                    <option value="1" {{ old('have_count', $item->have_count) == 1 ? 'selected' : '' }}>@lang('yes')</option>
                                </select>
                            </div>
                            <div class="col-4 mb-4">
                                <label class="form-label" for="facilities_category_id">{{ __('facilities_category') }}</label>
                                <select id="facilities_category_id" name="facilities_category_id" class="form-select">
                                    <option value="">@lang('select')</option>
                                    @foreach($categories as $category)
                                        <option value="{{ $category->id }}" {{ old('facilities_category_id', $item->facilities_category_id) == $category->id ? 'selected' : '' }}>{{ $category->name ?? $category->title_en ?? $category->id }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-4 mb-4">
                                <label class="form-label" for="is_featured">{{ __('is_featured') }}</label>
                                <select id="is_featured" name="is_featured" class="form-select">
                                    <option value="0" {{ old('is_featured', $item->is_featured) == 0 ? 'selected' : '' }}>@lang('no')</option>
                                    <option value="1" {{ old('is_featured', $item->is_featured) == 1 ? 'selected' : '' }}>@lang('yes')</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-12 text-center mt-4">
                            <button type="submit" class="btn btn-primary me-sm-3 me-1">{{ __('submit') }}</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
