<?php
namespace App\DataTables;

use App\Models\Admin\PropertyType;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class PropertyTypeDataTable extends DataTable
{
    public function dataTable($query)
    {
        return datatables()
            ->eloquent($query)
            ->addColumn('actions', function ($row) {
                $name = 'property_types';
                $item = $row;
                return view('content.property_types.actions', compact('name', 'item'))->render();
            })
            ->editColumn('created_at', function ($row) {
                return date('Y-m-d h:i A', strtotime($row->created_at));
            })
            ->rawColumns(['actions']);
    }

    public function query(PropertyType $model)
    {
        return $model->newQuery();
    }

    public function getLang()
    {
        if (app()->getLocale() == 'en') {
            return asset('dashboard_files/plugins/dtable/English.json');
        } elseif (app()->getLocale() == 'ar') {
            return asset('dashboard_files/plugins/dtable/Arabic.json');
        }
    }

    public function html()
    {
        return $this->builder()
            ->setTableId('property-types-table')
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->dom('Bfrtip')
            ->orderBy(3, 'desc')
            ->lengthMenu([[10, 25, 50, 100, -1], [10, 25, 50, 100, __('All')]])
            ->parameters([
                'dom' => 'Blfrtip',
            ])->language(['url' => $this->getLang()]);
    }

    protected function getColumns()
    {
        return [
            Column::make('id')->title('ID'),
            Column::make('title_en')->title(__('title_en')),
            Column::make('title_ar')->title(__('title_ar')),
            Column::make('order')->title(__('order')),
            Column::make('created_at')->title(__('created_at')),
            Column::computed('actions')->exportable(false)->printable(false)->width(60)->addClass('text-center')->title(__('actions')),
        ];
    }

    protected function filename(): string
    {
        return 'PropertyTypes_' . date('YmdHis');
    }
}
