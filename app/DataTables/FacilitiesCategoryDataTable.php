<?php
namespace App\DataTables;

use App\Models\Admin\FacilitiesCategory;
use Yajra\DataTables\Html\Button;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class FacilitiesCategoryDataTable extends DataTable
{
    public function dataTable($query)
    {
        return datatables()
            ->eloquent($query)

            ->addColumn('actions', function ($row) {
                $name = 'facilities_categories';
                $item = $row;
                return view('content.facilities_categories.actions', compact('name', 'item'))->render();
            })->editColumn('created_at', function ($row) {
            return date('Y-m-d h:i A', strtotime($row->created_at));
        })
            ->rawColumns(['actions']);
    }

    public function query(FacilitiesCategory $model)
    {
        return $model->newQuery();
    }

    /**
     * Get language file for datatable.
     *
     * @return string
     */
    public function getLang()
    {
        if (app()->getLocale() == 'en') {
            return asset('dashboard_files/plugins/dtable/English.json');
        } elseif (app()->getLocale() == 'ar') {
            return asset('dashboard_files/plugins/dtable/Arabic.json');
        }
    }

    /**
     * Optional method if you want to use html builder.
     *
     * @return \Yajra\DataTables\Html\Builder
     */
    public function html()
    {
        return $this->builder()
            ->setTableId('facilities-categories-table')
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->dom('Bfrtip')
            ->orderBy(3, 'desc')
            ->lengthMenu([[10, 25, 50, 100, -1], [10, 25, 50, 100, __('All')]])
            ->parameters([
                'dom' => 'Blfrtip',
            ])
            ->responsive(true)
            ->buttons(
                Button::make('excel')->text(__('Excel')),
                Button::make('print')->text(__('Print')),
            )->language(['url' => $this->getLang()]);
    }

    protected function getColumns()
    {
        return [
            Column::make('id')->addClass('text-center')->title('#'),
            Column::make('title_en')->addClass('text-center')->title(__('title_en')),
            Column::make('title_ar')->addClass('text-center')->title(__('title_ar')),
            Column::make('order')->addClass('text-center')->title(__('order')),
            Column::make('created_at')->addClass('text-center')->title(__('created_at')),
            Column::computed('actions')
                ->exportable(false)
                ->printable(false)
                ->addClass('text-center')->title(__('actions')),
        ];
    }

    protected function filename(): string
    {
        return 'FacilitiesCategories_' . date('YmdHis');
    }
}
