<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class NotificationPreference extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'type',
        'push_enabled',
        'email_enabled',
        'sms_enabled',
    ];

    protected $casts = [
        'push_enabled' => 'boolean',
        'email_enabled' => 'boolean',
        'sms_enabled' => 'boolean',
    ];

    /**
     * Get the user that owns the preference.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get default preferences for a user.
     */
    public static function getDefaultPreferences(): array
    {
        return [
            'booking' => [
                'push_enabled' => true,
                'email_enabled' => true,
                'sms_enabled' => false,
            ],
            'review' => [
                'push_enabled' => true,
                'email_enabled' => true,
                'sms_enabled' => false,
            ],
            'system' => [
                'push_enabled' => true,
                'email_enabled' => false,
                'sms_enabled' => false,
            ],
            'promotion' => [
                'push_enabled' => false,
                'email_enabled' => false,
                'sms_enabled' => false,
            ],
            'payment' => [
                'push_enabled' => true,
                'email_enabled' => true,
                'sms_enabled' => true,
            ],
            'host' => [
                'push_enabled' => true,
                'email_enabled' => true,
                'sms_enabled' => false,
            ],
        ];
    }

    /**
     * Create default preferences for a user.
     */
    public static function createDefaultForUser(int $userId): void
    {
        $defaults = self::getDefaultPreferences();
        
        foreach ($defaults as $type => $settings) {
            self::updateOrCreate(
                ['user_id' => $userId, 'type' => $type],
                $settings
            );
        }
    }

    /**
     * Get the type display name.
     */
    public function getTypeDisplayAttribute(): string
    {
        return match($this->type) {
            'booking' => 'الحجوزات',
            'review' => 'التقييمات',
            'system' => 'النظام',
            'promotion' => 'العروض',
            'payment' => 'المدفوعات',
            'host' => 'الاستضافة',
            default => $this->type,
        };
    }
}
