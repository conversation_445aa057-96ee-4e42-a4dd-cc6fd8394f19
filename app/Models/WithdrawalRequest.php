<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WithdrawalRequest extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'amount',
        'currency',
        'method',
        'payment_details',
        'status',
        'notes',
        'admin_notes',
        'processed_at',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'payment_details' => 'array',
        'processed_at' => 'datetime',
    ];

    /**
     * Get the user that owns the withdrawal request.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the method display name.
     */
    public function getMethodDisplayAttribute(): string
    {
        return match($this->method) {
            'bank_transfer' => 'تحويل بنكي',
            'paypal' => 'PayPal',
            'stc_pay' => 'STC Pay',
            'apple_pay' => 'Apple Pay',
            default => $this->method,
        };
    }

    /**
     * Get the status display name.
     */
    public function getStatusDisplayAttribute(): string
    {
        return match($this->status) {
            'pending' => 'قيد المراجعة',
            'approved' => 'موافق عليه',
            'processed' => 'تم التحويل',
            'rejected' => 'مرفوض',
            default => $this->status,
        };
    }

    /**
     * Get the status color.
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'pending' => 'orange',
            'approved' => 'blue',
            'processed' => 'green',
            'rejected' => 'red',
            default => 'gray',
        };
    }

    /**
     * Scope to get by status.
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get pending requests.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope to get processed requests.
     */
    public function scopeProcessed($query)
    {
        return $query->where('status', 'processed');
    }
}
