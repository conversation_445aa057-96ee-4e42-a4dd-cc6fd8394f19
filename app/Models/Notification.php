<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Notification extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'type',
        'title',
        'message',
        'data',
        'read_at',
        'is_push_sent',
    ];

    protected $casts = [
        'data' => 'array',
        'read_at' => 'datetime',
        'is_push_sent' => 'boolean',
    ];

    /**
     * Get the user that owns the notification.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Check if the notification is read.
     */
    public function isRead(): bool
    {
        return $this->read_at !== null;
    }

    /**
     * Mark the notification as read.
     */
    public function markAsRead(): void
    {
        if (!$this->isRead()) {
            $this->update(['read_at' => now()]);
        }
    }

    /**
     * Scope to get unread notifications.
     */
    public function scopeUnread($query)
    {
        return $query->whereNull('read_at');
    }

    /**
     * Scope to get read notifications.
     */
    public function scopeRead($query)
    {
        return $query->whereNotNull('read_at');
    }

    /**
     * Scope to get notifications by type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Get the type display name.
     */
    public function getTypeDisplayAttribute(): string
    {
        return match($this->type) {
            'booking' => 'حجز',
            'review' => 'تقييم',
            'system' => 'نظام',
            'promotion' => 'عرض',
            'payment' => 'دفع',
            'host' => 'استضافة',
            default => $this->type,
        };
    }

    /**
     * Get the icon for the notification type.
     */
    public function getTypeIconAttribute(): string
    {
        return match($this->type) {
            'booking' => 'book_online',
            'review' => 'star',
            'system' => 'info',
            'promotion' => 'local_offer',
            'payment' => 'payment',
            'host' => 'home',
            default => 'notifications',
        };
    }

    /**
     * Create a new notification for a user.
     */
    public static function createForUser(
        int $userId,
        string $type,
        string $title,
        string $message,
        array $data = []
    ): self {
        return self::create([
            'user_id' => $userId,
            'type' => $type,
            'title' => $title,
            'message' => $message,
            'data' => $data,
        ]);
    }
}
