<?php

namespace App\Models\Admin;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CancellationPolicy extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name_en',
        'name_ar',
        'description_en',
        'description_ar',
        'policy_type', // 'flexible_short', 'moderate_short', 'strict_short', 'moderate_long', 'strict_long', 'custom'
        'duration_type', // 'short' (≤28 days), 'long' (>28 days), 'both'
        'cancellation_window_hours', // Hours before check-in when cancellation is allowed
        'refund_percentage', // Percentage of refund (0-100)
        'booking_window_hours', // Hours after booking when special rules apply (for strict long-term)
        'minimum_notice_hours', // Minimum hours before arrival for cancellation (for strict long-term)
        'service_fee_refundable', // Boolean: whether service fees are refundable
        'cleaning_fee_refundable', // Boolean: whether cleaning fees are refundable
        'is_active',
        'order',
    ];

    protected $casts = [
        'service_fee_refundable' => 'boolean',
        'cleaning_fee_refundable' => 'boolean',
        'is_active' => 'boolean',
        'refund_percentage' => 'decimal:2',
    ];

    /**
     * Check if policy applies to given booking duration
     */
    public function appliesTo($bookingDurationDays)
    {
        switch ($this->duration_type) {
            case 'short':
                return $bookingDurationDays <= 28;
            case 'long':
                return $bookingDurationDays > 28;
            case 'both':
                return true;
            default:
                return true;
        }
    }

    /**
     * Calculate refund amount based on policy and booking details
     */
    public function calculateRefund($totalAmount, $bookingDate, $arrivalDate, $cancellationDate = null)
    {
        $cancellationDate = $cancellationDate ?? now();
        $hoursUntilArrival = $cancellationDate->diffInHours($arrivalDate);
        $hoursAfterBooking = $bookingDate->diffInHours($cancellationDate);

        // For strict long-term policy
        if ($this->policy_type === 'strict_long') {
            // Full refund if cancelled within booking window AND minimum notice is met
            if ($hoursAfterBooking <= $this->booking_window_hours &&
                $hoursUntilArrival >= $this->minimum_notice_hours) {
                return $totalAmount;
            }
            return 0; // No refund otherwise
        }

        // For other policies, check if within cancellation window
        if ($hoursUntilArrival >= $this->cancellation_window_hours) {
            return ($totalAmount * $this->refund_percentage) / 100;
        }

        return 0; // No refund if outside cancellation window
    }

    /**
     * Get the items that use this cancellation policy
     */
    public function items()
    {
        return $this->hasMany(ServiceCategoryItem::class, 'cancelation_policy_id');
    }

    /**
     * Scope to get only active policies
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to order by order field
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('order');
    }

    /**
     * Get localized name
     */
    public function getLocalizedNameAttribute()
    {
        return app()->getLocale() === 'ar' ? $this->name_ar : $this->name_en;
    }

    /**
     * Get localized description
     */
    public function getLocalizedDescriptionAttribute()
    {
        return app()->getLocale() === 'ar' ? $this->description_ar : $this->description_en;
    }
}
