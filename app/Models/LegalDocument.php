<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LegalDocument extends Model
{
    use HasFactory;

    protected $fillable = [
        'type',
        'title_en',
        'title_ar',
        'content_en',
        'content_ar',
        'version',
        'effective_date',
        'is_active',
    ];

    protected $casts = [
        'effective_date' => 'datetime',
        'is_active' => 'boolean',
    ];

    /**
     * Get the localized title.
     */
    public function getTitleAttribute(): string
    {
        return app()->getLocale() === 'ar' ? $this->title_ar : $this->title_en;
    }

    /**
     * Get the localized content.
     */
    public function getContentAttribute(): string
    {
        return app()->getLocale() === 'ar' ? $this->content_ar : $this->content_en;
    }

    /**
     * Scope to get active documents.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get by type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Get the latest version of a document type.
     */
    public function scopeLatestVersion($query, $type)
    {
        return $query->byType($type)
                    ->active()
                    ->orderBy('effective_date', 'desc')
                    ->first();
    }
}
