<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CoHost extends Model
{
    use HasFactory;

    protected $fillable = [
        'host_id',
        'co_host_id',
        'property_id',
        'status',
        'permissions',
        'commission_percentage',
        'message',
        'accepted_at',
    ];

    protected $casts = [
        'permissions' => 'array',
        'commission_percentage' => 'decimal:2',
        'accepted_at' => 'datetime',
    ];

    /**
     * Get the main host
     */
    public function host()
    {
        return $this->belongsTo(User::class, 'host_id');
    }

    /**
     * Get the co-host user
     */
    public function coHost()
    {
        return $this->belongsTo(User::class, 'co_host_id');
    }

    /**
     * Get the property (if specific to one property)
     */
    public function property()
    {
        return $this->belongsTo(\App\Models\ServiceCategoryItem::class, 'property_id');
    }

    /**
     * Scope to get pending invitations
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope to get accepted co-hosts
     */
    public function scopeAccepted($query)
    {
        return $query->where('status', 'accepted');
    }

    /**
     * Scope to get co-hosts for a specific host
     */
    public function scopeForHost($query, $hostId)
    {
        return $query->where('host_id', $hostId);
    }

    /**
     * Scope to get invitations for a specific co-host
     */
    public function scopeForCoHost($query, $coHostId)
    {
        return $query->where('co_host_id', $coHostId);
    }

    /**
     * Check if co-host relationship is accepted
     */
    public function isAccepted()
    {
        return $this->status === 'accepted';
    }

    /**
     * Check if co-host relationship is pending
     */
    public function isPending()
    {
        return $this->status === 'pending';
    }

    /**
     * Accept the co-host invitation
     */
    public function accept()
    {
        $this->update([
            'status' => 'accepted',
            'accepted_at' => now(),
        ]);
    }

    /**
     * Decline the co-host invitation
     */
    public function decline()
    {
        $this->update(['status' => 'declined']);
    }

    /**
     * Remove the co-host relationship
     */
    public function remove()
    {
        $this->update(['status' => 'removed']);
    }

    /**
     * Check if co-host has specific permission
     */
    public function hasPermission($permission)
    {
        return in_array($permission, $this->permissions ?? []);
    }

    /**
     * Get default permissions for co-hosts
     */
    public static function getDefaultPermissions()
    {
        return [
            'manage_bookings',
            'communicate_guests',
            'update_calendar',
            'view_earnings',
        ];
    }

    /**
     * Get all available permissions
     */
    public static function getAllPermissions()
    {
        return [
            'manage_bookings' => 'إدارة الحجوزات',
            'communicate_guests' => 'التواصل مع الضيوف',
            'update_calendar' => 'تحديث التقويم',
            'view_earnings' => 'عرض الأرباح',
            'edit_listing' => 'تعديل الإعلان',
            'manage_pricing' => 'إدارة الأسعار',
            'access_reviews' => 'الوصول للتقييمات',
            'manage_amenities' => 'إدارة المرافق',
        ];
    }
}
