<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserPreference extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'category',
        'key',
        'value',
        'type',
    ];

    /**
     * Get the user that owns the preference.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the typed value.
     */
    public function getTypedValueAttribute()
    {
        return match($this->type) {
            'boolean' => filter_var($this->value, FILTER_VALIDATE_BOOLEAN),
            'integer' => (int) $this->value,
            'float' => (float) $this->value,
            'json' => json_decode($this->value, true),
            default => $this->value,
        };
    }

    /**
     * Set a preference value.
     */
    public static function setPreference(int $userId, string $category, string $key, $value, string $type = 'string'): self
    {
        $stringValue = match($type) {
            'boolean' => $value ? '1' : '0',
            'json' => json_encode($value),
            default => (string) $value,
        };

        return self::updateOrCreate(
            [
                'user_id' => $userId,
                'category' => $category,
                'key' => $key,
            ],
            [
                'value' => $stringValue,
                'type' => $type,
            ]
        );
    }

    /**
     * Get a preference value.
     */
    public static function getPreference(int $userId, string $category, string $key, $default = null)
    {
        $preference = self::where('user_id', $userId)
            ->where('category', $category)
            ->where('key', $key)
            ->first();

        return $preference ? $preference->typed_value : $default;
    }

    /**
     * Get all preferences for a category.
     */
    public static function getCategoryPreferences(int $userId, string $category): array
    {
        return self::where('user_id', $userId)
            ->where('category', $category)
            ->get()
            ->pluck('typed_value', 'key')
            ->toArray();
    }

    /**
     * Get all user preferences grouped by category.
     */
    public static function getAllUserPreferences(int $userId): array
    {
        $preferences = self::where('user_id', $userId)->get();
        
        $grouped = [];
        foreach ($preferences as $preference) {
            $grouped[$preference->category][$preference->key] = $preference->typed_value;
        }
        
        return $grouped;
    }
}
