<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserEarlyAccess extends Model
{
    use HasFactory;

    protected $table = 'user_early_access';

    protected $fillable = [
        'user_id',
        'feature_id',
        'status',
        'feedback',
        'rating',
        'enrolled_at',
        'last_used_at',
    ];

    protected $casts = [
        'enrolled_at' => 'datetime',
        'last_used_at' => 'datetime',
    ];

    /**
     * Get the user
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the feature
     */
    public function feature()
    {
        return $this->belongsTo(EarlyAccessFeature::class, 'feature_id');
    }

    /**
     * Scope to get enrolled users
     */
    public function scopeEnrolled($query)
    {
        return $query->where('status', 'enrolled');
    }

    /**
     * Scope to get waitlist users
     */
    public function scopeWaitlist($query)
    {
        return $query->where('status', 'waitlist');
    }

    /**
     * Check if enrollment is active
     */
    public function isEnrolled()
    {
        return $this->status === 'enrolled';
    }

    /**
     * Check if user is on waitlist
     */
    public function isOnWaitlist()
    {
        return $this->status === 'waitlist';
    }

    /**
     * Mark as used (update last_used_at)
     */
    public function markAsUsed()
    {
        $this->update(['last_used_at' => now()]);
    }

    /**
     * Enroll user
     */
    public function enroll()
    {
        $this->update([
            'status' => 'enrolled',
            'enrolled_at' => now(),
        ]);
    }

    /**
     * Add to waitlist
     */
    public function addToWaitlist()
    {
        $this->update(['status' => 'waitlist']);
    }

    /**
     * Remove from early access
     */
    public function remove()
    {
        $this->update(['status' => 'removed']);
    }

    /**
     * Submit feedback
     */
    public function submitFeedback($feedback, $rating = null)
    {
        $this->update([
            'feedback' => $feedback,
            'rating' => $rating,
        ]);
    }
}
