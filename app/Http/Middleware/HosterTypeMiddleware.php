<?php
namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class HosterTypeMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $user = auth('api')->user();

        // Check if user exists and is in hoster mode
        if (! $user || ! $user->is_hoster_mode) {
            return response()->json([
                'message' => 'Unauthorized. Hoster mode required.',
                'error' => 'hoster_mode_required'
            ], 403);
        }

        return $next($request);
    }
}
