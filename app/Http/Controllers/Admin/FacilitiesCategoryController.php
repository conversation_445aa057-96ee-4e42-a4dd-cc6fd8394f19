<?php
namespace App\Http\Controllers\Admin;

use App\DataTables\FacilitiesCategoryDataTable;
use App\Http\Controllers\Controller;
use App\Models\Admin\FacilitiesCategory;
use App\Models\AuditLog;
use Illuminate\Http\Request;

class FacilitiesCategoryController extends Controller
{
    private const NAME   = 'facilities_categories';
    private const NAME_S = 'facilities_category';

    public function __construct()
    {
        $this->middleware(['permission:read ' . self::NAME])->only('index');
        $this->middleware(['permission:create ' . self::NAME])->only('create');
        $this->middleware(['permission:update ' . self::NAME])->only('edit');
        $this->middleware(['permission:delete ' . self::NAME])->only('destroy');
    }

    public function index(FacilitiesCategoryDataTable $dataTable)
    {
        $name  = self::NAME;
        $nameS = self::NAME_S;
        return $dataTable->render('content.' . self::NAME . '.index', compact('name', 'nameS'));
    }

    public function create()
    {
        $name  = self::NAME;
        $nameS = self::NAME_S;
        return view('content.' . self::NAME . '.create', compact('name', 'nameS'));
    }

    public function store(Request $request)
    {
        $data = $request->validate([
            'title_en' => 'required|string|max:255',
            'title_ar' => 'required|string|max:255',
            'order'    => 'nullable|integer',
        ]);
        $data['order'] = $data['order'] ?? 0;
        $item = FacilitiesCategory::create($data);
        AuditLog::create([
            'user_id'            => auth()->user()->id,
            'description'        => __(self::NAME_S . '_created'),
            'ip_address'         => $request->getClientIp(),
            'relationmodel_type' => get_class($item),
            'relationmodel_id'   => $item->id,
        ]);
        session()->flash('status', __(self::NAME_S . '_created'));
        return redirect(route(self::NAME . '.index'));
    }

    public function edit($id)
    {
        $item = FacilitiesCategory::find($id);
        if ($item) {
            $name  = self::NAME;
            $nameS = self::NAME_S;
            return view('content.' . self::NAME . '.edit', compact('name', 'nameS', 'item'));
        }
        abort(404);
    }

    public function update(Request $request, $id)
    {
        $item = FacilitiesCategory::find($id);
        if ($item) {
            $data = $request->validate([
                'title_en' => 'required|string|max:255',
                'title_ar' => 'required|string|max:255',
                'order'    => 'nullable|integer',
            ]);
            $data['order'] = $data['order'] ?? 0;
            $item->update($data);
            AuditLog::create([
                'user_id'            => auth()->user()->id,
                'description'        => __(self::NAME_S . '_updated'),
                'ip_address'         => $request->getClientIp(),
                'relationmodel_type' => get_class($item),
                'relationmodel_id'   => $item->id,
            ]);
            session()->flash('status', __(self::NAME_S . '_updated'));
            return redirect(route(self::NAME . '.index'));
        }
        abort(404);
    }

    public function destroy(Request $request, $id)
    {
        $item = FacilitiesCategory::find($id);
        if ($item) {
            $item->delete();
            AuditLog::create([
                'user_id'            => auth()->user()->id,
                'description'        => __(self::NAME_S . '_deleted'),
                'ip_address'         => $request->getClientIp(),
                'relationmodel_type' => get_class($item),
                'relationmodel_id'   => $item->id,
            ]);
            session()->flash('status', __(self::NAME_S . '_deleted'));
            return redirect(route(self::NAME . '.index'));
        }
        abort(404);
    }
}
