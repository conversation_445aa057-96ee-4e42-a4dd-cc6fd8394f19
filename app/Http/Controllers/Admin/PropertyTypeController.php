<?php
namespace App\Http\Controllers\Admin;

use App\DataTables\PropertyTypeDataTable;
use App\Http\Controllers\Controller;
use App\Models\Admin\PropertyType;
use App\Models\AuditLog;
use Illuminate\Http\Request;

class PropertyTypeController extends Controller
{
    private const NAME   = 'property_types';
    private const NAME_S = 'property_type';

    public function __construct()
    {
        $this->middleware(['permission:read ' . self::NAME])->only('index');
        $this->middleware(['permission:create ' . self::NAME])->only('create');
        $this->middleware(['permission:update ' . self::NAME])->only('edit');
        $this->middleware(['permission:delete ' . self::NAME])->only('destroy');
    }

    public function index(PropertyTypeDataTable $dataTable)
    {
        $name  = self::NAME;
        $nameS = self::NAME_S;
        return $dataTable->render('content.' . self::NAME . '.index', compact('name', 'nameS'));
    }

    public function create()
    {
        $name  = self::NAME;
        $nameS = self::NAME_S;
        return view('content.' . self::NAME . '.create', compact('name', 'nameS'));
    }

    public function store(Request $request)
    {
        $data = $request->validate([
            'title_en' => 'required|string|max:255',
            'title_ar' => 'required|string|max:255',
            'order'    => 'nullable|integer',
        ]);
        $data['order'] = $data['order'] ?? 0;
        $item = PropertyType::create($data);
        AuditLog::create([
            'user_id'            => auth()->user()->id,
            'description'        => __(self::NAME_S . '_created'),
            'ip_address'         => $request->getClientIp(),
            'relationmodel_type' => get_class($item),
            'relationmodel_id'   => $item->id,
        ]);
        session()->flash('status', __(self::NAME_S . '_created'));
        return redirect(route(self::NAME . '.index'));
    }

    public function edit($id)
    {
        $item = PropertyType::find($id);
        if ($item) {
            $name  = self::NAME;
            $nameS = self::NAME_S;
            return view('content.' . self::NAME . '.edit', compact('item', 'name', 'nameS'));
        }
        abort(404);
    }

    public function update(Request $request, $id)
    {
        $item = PropertyType::find($id);
        if (!$item) {
            abort(404);
        }
        $data = $request->validate([
            'title_en' => 'required|string|max:255',
            'title_ar' => 'required|string|max:255',
            'order'    => 'nullable|integer',
        ]);
        $data['order'] = $data['order'] ?? 0;
        $item->update($data);
        AuditLog::create([
            'user_id'            => auth()->user()->id,
            'description'        => __(self::NAME_S . '_updated'),
            'ip_address'         => $request->getClientIp(),
            'relationmodel_type' => get_class($item),
            'relationmodel_id'   => $item->id,
        ]);
        session()->flash('status', __(self::NAME_S . '_updated'));
        return redirect(route(self::NAME . '.index'));
    }

    public function destroy($id)
    {
        $item = PropertyType::find($id);
        if ($item) {
            $item->delete();
            AuditLog::create([
                'user_id'            => auth()->user()->id,
                'description'        => __(self::NAME_S . '_deleted'),
                'ip_address'         => request()->getClientIp(),
                'relationmodel_type' => get_class($item),
                'relationmodel_id'   => $item->id,
            ]);
            session()->flash('status', __(self::NAME_S . '_deleted'));
        }
        return redirect(route(self::NAME . '.index'));
    }
}
