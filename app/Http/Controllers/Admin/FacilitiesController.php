<?php
namespace App\Http\Controllers\Admin;

use App\DataTables\FacilitiesDataTable;
use App\Http\Controllers\Controller;
use App\Models\Admin\Facilities;
use App\Models\Admin\FacilitiesCategory;
use App\Models\AuditLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\Laravel\Facades\Image;

class FacilitiesController extends Controller
{
    private const NAME   = 'facilities';
    private const NAME_S = 'facility';

    public function __construct()
    {
        $this->middleware(['permission:read ' . self::NAME])->only('index');
        $this->middleware(['permission:create ' . self::NAME])->only('create');
        $this->middleware(['permission:update ' . self::NAME])->only('edit');
        $this->middleware(['permission:delete ' . self::NAME])->only('destroy');
    }

    /**
     * Display a listing of the resource.
     *
     * @param FacilitiesDataTable $dataTable
     * @return \Illuminate\Http\Response
     */
    public function index(FacilitiesDataTable $dataTable)
    {
        $name  = self::NAME;
        $nameS = self::NAME_S;
        return $dataTable->render('content.' . self::NAME . '.index', compact('name', 'nameS'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $name             = self::NAME;
        $nameS            = self::NAME_S;
        $categories       = FacilitiesCategory::all();
        return view('content.' . self::NAME . '.create', compact('name', 'nameS', 'categories'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data = $request->validate([
            'title_en'                 => 'required|string',
            'title_ar'                 => 'required|string',
            'icon'                     => 'required|image|mimes:jpeg,png,jpg,gif,svg|max:2048', // Validate image file
            'order'                    => 'nullable|integer',
            'have_count'          => 'nullable|boolean',
            'is_featured'              => 'nullable|boolean',
            'facilities_category_id'   => 'nullable|exists:facilities_categories,id',
        ]);

        // Set default values if not provided
        $data['order']        = $data['order'] ?? 0;
        $data['have_count']   = $data['have_count'] ?? 0;
        $data['is_featured']  = $request->has('is_featured') ? (bool)$request->input('is_featured') : false;
        $data['facilities_category_id'] = $request->input('facilities_category_id');

        // Handle file upload and resize the icon
        if ($request->hasFile('icon')) {
            $image        = $request->file('icon');
            $resizedImage = Image::read($image)->resize(100, 100); // Resize to 100x100 pixels
            $iconPath     = 'icons/' . uniqid() . '.' . $image->getClientOriginalExtension();
            Storage::disk('public')->put($iconPath, $resizedImage->encode());
            $data['icon'] = $iconPath;
        }

        $item = Facilities::create($data);

        // Log the creation
        AuditLog::create([
            'user_id'            => auth()->user()->id,
            'description'        => __(self::NAME_S . '_created'),
            'ip_address'         => $request->getClientIp(),
            'relationmodel_type' => get_class($item),
            'relationmodel_id'   => $item->id,
        ]);

        session()->flash('status', __(self::NAME_S . '_created'));
        return redirect(route(self::NAME . '.index'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $item = Facilities::find($id);
        if ($item) {
            $name             = self::NAME;
            $nameS            = self::NAME_S;
            $categories       = FacilitiesCategory::all();
            return view('content.' . self::NAME . '.edit', compact('name', 'nameS', 'item', 'categories'));
        }
        abort(404);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $item = Facilities::find($id);
        if ($item) {
            $data = $request->validate([
                'title_en'                 => 'required|string',
                'title_ar'                 => 'required|string',
                'icon'                     => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048', // Validate image file
                'order'                    => 'nullable|integer',
                'have_count'          => 'nullable|boolean',
                'is_featured'              => 'nullable|boolean',
                'facilities_category_id'   => 'nullable|exists:facilities_categories,id',
            ]);

            // Set default values if not provided
            $data['order']        = $data['order'] ?? 0;
            $data['have_count']   = $data['have_count'] ?? 0;
            $data['is_featured']  = $request->has('is_featured') ? (bool)$request->input('is_featured') : false;
            $data['facilities_category_id'] = $request->input('facilities_category_id');

            // Handle file upload and resize the icon
            if ($request->hasFile('icon')) {
                // Delete old icon if it exists
                if ($item->icon && Storage::disk('public')->exists($item->icon)) {
                    Storage::disk('public')->delete($item->icon);
                }

                // Resize and save the new icon
                $image        = $request->file('icon');
                $resizedImage = Image::read($image)->resize(100, 100); // Resize to 100x100 pixels
                $iconPath     = 'icons/' . uniqid() . '.' . $image->getClientOriginalExtension();
                Storage::disk('public')->put($iconPath, $resizedImage->encode());
                $data['icon'] = $iconPath;
            }

            $item->update($data);

            // Log the update
            AuditLog::create([
                'user_id'            => auth()->user()->id,
                'description'        => __(self::NAME_S . '_updated'),
                'ip_address'         => $request->getClientIp(),
                'relationmodel_type' => get_class($item),
                'relationmodel_id'   => $item->id,
            ]);

            session()->flash('status', __(self::NAME_S . '_updated'));
            return redirect(route(self::NAME . '.index'));
        }
        abort(404);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $id)
    {
        $item = Facilities::find($id);
        if ($item) {

            // Delete associated files
            if ($item->icon) {
                Storage::disk('public')->delete($item->icon);
            }

            $item->delete();

            // Log the deletion
            AuditLog::create([
                'user_id'            => auth()->user()->id,
                'description'        => __(self::NAME_S . '_deleted'),
                'ip_address'         => $request->getClientIp(),
                'relationmodel_type' => get_class($item),
                'relationmodel_id'   => $item->id,
            ]);

            session()->flash('status', __(self::NAME_S . '_deleted'));
            return redirect(route(self::NAME . '.index'));
        }
        abort(404);
    }
}
