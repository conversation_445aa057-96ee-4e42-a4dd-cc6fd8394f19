<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Notification;
use App\Models\NotificationPreference;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class NotificationController extends Controller
{
    /**
     * Get user's notifications.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $perPage = $request->get('per_page', 20);
            $type = $request->get('type');
            $unreadOnly = $request->boolean('unread_only', false);

            $query = $user->notifications()
                ->orderBy('created_at', 'desc');

            if ($type) {
                $query->byType($type);
            }

            if ($unreadOnly) {
                $query->unread();
            }

            $notifications = $query->paginate($perPage);

            $data = $notifications->map(function ($notification) {
                return [
                    'id' => $notification->id,
                    'type' => $notification->type,
                    'type_display' => $notification->type_display,
                    'type_icon' => $notification->type_icon,
                    'title' => $notification->title,
                    'message' => $notification->message,
                    'data' => $notification->data,
                    'is_read' => $notification->isRead(),
                    'created_at' => $notification->created_at->format('Y-m-d H:i'),
                    'read_at' => $notification->read_at?->format('Y-m-d H:i'),
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $data,
                'pagination' => [
                    'current_page' => $notifications->currentPage(),
                    'last_page' => $notifications->lastPage(),
                    'per_page' => $notifications->perPage(),
                    'total' => $notifications->total(),
                ],
                'unread_count' => $user->notifications()->unread()->count(),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب الإشعارات',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Mark notification as read.
     */
    public function markAsRead(Request $request, $id): JsonResponse
    {
        try {
            $user = Auth::user();
            $notification = $user->notifications()->findOrFail($id);
            
            $notification->markAsRead();

            return response()->json([
                'success' => true,
                'message' => 'تم تحديد الإشعار كمقروء',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تحديث الإشعار',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Mark all notifications as read.
     */
    public function markAllAsRead(): JsonResponse
    {
        try {
            $user = Auth::user();
            
            $user->notifications()
                ->unread()
                ->update(['read_at' => now()]);

            return response()->json([
                'success' => true,
                'message' => 'تم تحديد جميع الإشعارات كمقروءة',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تحديث الإشعارات',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Delete notification.
     */
    public function destroy($id): JsonResponse
    {
        try {
            $user = Auth::user();
            $notification = $user->notifications()->findOrFail($id);
            
            $notification->delete();

            return response()->json([
                'success' => true,
                'message' => 'تم حذف الإشعار',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء حذف الإشعار',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get notification preferences.
     */
    public function getPreferences(): JsonResponse
    {
        try {
            $user = Auth::user();
            
            $preferences = NotificationPreference::where('user_id', $user->id)->get();
            
            // If no preferences exist, create defaults
            if ($preferences->isEmpty()) {
                NotificationPreference::createDefaultForUser($user->id);
                $preferences = NotificationPreference::where('user_id', $user->id)->get();
            }

            $data = $preferences->map(function ($preference) {
                return [
                    'type' => $preference->type,
                    'type_display' => $preference->type_display,
                    'push_enabled' => $preference->push_enabled,
                    'email_enabled' => $preference->email_enabled,
                    'sms_enabled' => $preference->sms_enabled,
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $data,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب تفضيلات الإشعارات',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update notification preferences.
     */
    public function updatePreferences(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'preferences' => 'required|array',
                'preferences.*.type' => 'required|string|in:booking,review,system,promotion,payment,host',
                'preferences.*.push_enabled' => 'required|boolean',
                'preferences.*.email_enabled' => 'required|boolean',
                'preferences.*.sms_enabled' => 'required|boolean',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'بيانات غير صحيحة',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $user = Auth::user();
            
            foreach ($request->preferences as $preferenceData) {
                NotificationPreference::updateOrCreate(
                    [
                        'user_id' => $user->id,
                        'type' => $preferenceData['type'],
                    ],
                    [
                        'push_enabled' => $preferenceData['push_enabled'],
                        'email_enabled' => $preferenceData['email_enabled'],
                        'sms_enabled' => $preferenceData['sms_enabled'],
                    ]
                );
            }

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث تفضيلات الإشعارات بنجاح',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تحديث التفضيلات',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get notification statistics.
     */
    public function getStats(): JsonResponse
    {
        try {
            $user = Auth::user();
            
            $stats = [
                'total_notifications' => $user->notifications()->count(),
                'unread_notifications' => $user->notifications()->unread()->count(),
                'notifications_by_type' => $user->notifications()
                    ->selectRaw('type, COUNT(*) as count')
                    ->groupBy('type')
                    ->pluck('count', 'type'),
                'recent_activity' => $user->notifications()
                    ->latest()
                    ->limit(5)
                    ->get()
                    ->map(function ($notification) {
                        return [
                            'type' => $notification->type,
                            'title' => $notification->title,
                            'created_at' => $notification->created_at->format('Y-m-d H:i'),
                        ];
                    }),
            ];

            return response()->json([
                'success' => true,
                'data' => $stats,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب إحصائيات الإشعارات',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Create a test notification (for development).
     */
    public function createTestNotification(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            
            $notification = Notification::createForUser(
                $user->id,
                'system',
                'إشعار تجريبي',
                'هذا إشعار تجريبي لاختبار النظام',
                ['test' => true]
            );

            return response()->json([
                'success' => true,
                'message' => 'تم إنشاء إشعار تجريبي',
                'data' => [
                    'id' => $notification->id,
                    'title' => $notification->title,
                    'message' => $notification->message,
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إنشاء الإشعار التجريبي',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
