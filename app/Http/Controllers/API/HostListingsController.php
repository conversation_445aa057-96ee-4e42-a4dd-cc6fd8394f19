<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Resources\ServiceCategoryItemResource;
use App\Models\Admin\ServiceCategoryItem;
use App\Models\Admin\ServiceCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class HostListingsController extends Controller
{
    /**
     * Get host's listings with filters and pagination
     */
    public function index(Request $request)
    {
        try {
            $user = Auth::user();
            
            $request->validate([
                'page' => 'nullable|integer|min:1',
                'limit' => 'nullable|integer|min:1|max:100',
                'status' => 'nullable|string|in:active,inactive,draft,pending',
                'category' => 'nullable|string',
                'sort_by' => 'nullable|string|in:created_at,updated_at,price,title',
                'sort_order' => 'nullable|string|in:asc,desc',
                'search' => 'nullable|string|max:255',
            ]);

            $query = ServiceCategoryItem::where('user_id', $user->id);

            // Apply filters
            if ($request->status) {
                switch ($request->status) {
                    case 'active':
                        $query->where('active', 1);
                        break;
                    case 'inactive':
                        $query->where('active', 0);
                        break;
                    case 'draft':
                        $query->where('active', 0)->whereNull('published_at');
                        break;
                    case 'pending':
                        $query->where('active', 0)->whereNotNull('published_at');
                        break;
                }
            }

            if ($request->category) {
                $query->where('service_category_id', $request->category);
            }

            if ($request->search) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('title', 'LIKE', "%{$search}%")
                      ->orWhere('content', 'LIKE', "%{$search}%");
                });
            }

            // Apply sorting
            $sortBy = $request->sort_by ?? 'created_at';
            $sortOrder = $request->sort_order ?? 'desc';
            $query->orderBy($sortBy, $sortOrder);

            // Paginate results
            $limit = $request->limit ?? 20;
            $listings = $query->with(['serviceCategory', 'facilities'])->paginate($limit);

            return response()->json([
                'success' => true,
                'data' => [
                    'listings' => ServiceCategoryItemResource::collection($listings->items()),
                    'pagination' => [
                        'current_page' => $listings->currentPage(),
                        'last_page' => $listings->lastPage(),
                        'per_page' => $listings->perPage(),
                        'total' => $listings->total(),
                        'has_more' => $listings->hasMorePages(),
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch listings',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get host's listing statistics
     */
    public function stats(Request $request)
    {
        try {
            $user = Auth::user();
            
            $totalListings = ServiceCategoryItem::where('user_id', $user->id)->count();
            $activeListings = ServiceCategoryItem::where('user_id', $user->id)->where('active', 1)->count();
            $inactiveListings = ServiceCategoryItem::where('user_id', $user->id)->where('active', 0)->count();
            
            // Calculate total views and bookings
            $totalViews = ServiceCategoryItem::where('user_id', $user->id)->sum('views');
            
            // Get recent performance (last 30 days)
            $recentViews = ServiceCategoryItem::where('user_id', $user->id)
                ->where('updated_at', '>=', now()->subDays(30))
                ->sum('views');

            return response()->json([
                'success' => true,
                'data' => [
                    'total_listings' => $totalListings,
                    'active_listings' => $activeListings,
                    'inactive_listings' => $inactiveListings,
                    'draft_listings' => $inactiveListings, // For now, same as inactive
                    'total_views' => $totalViews,
                    'recent_views' => $recentViews,
                    'total_bookings' => 0, // TODO: Implement when reservations are ready
                    'total_revenue' => 0.0, // TODO: Implement when payments are ready
                    'average_rating' => 4.5, // TODO: Implement when reviews are ready
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch listing stats',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Toggle listing status (active/inactive)
     */
    public function toggleStatus(Request $request, $id)
    {
        try {
            $user = Auth::user();
            $listing = ServiceCategoryItem::where('id', $id)->where('user_id', $user->id)->first();

            if (!$listing) {
                return response()->json([
                    'success' => false,
                    'message' => 'Listing not found or access denied'
                ], 404);
            }

            $listing->active = !$listing->active;
            $listing->save();

            return response()->json([
                'success' => true,
                'message' => 'Listing status updated successfully',
                'data' => [
                    'id' => $listing->id,
                    'active' => $listing->active
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to toggle listing status',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a listing
     */
    public function delete(Request $request, $id)
    {
        try {
            $user = Auth::user();
            $listing = ServiceCategoryItem::where('id', $id)->where('user_id', $user->id)->first();

            if (!$listing) {
                return response()->json([
                    'success' => false,
                    'message' => 'Listing not found or access denied'
                ], 404);
            }

            // Delete associated files if they exist
            if ($listing->image) {
                Storage::disk('public')->delete($listing->image);
            }
            if ($listing->video) {
                Storage::disk('public')->delete($listing->video);
            }

            $listing->delete();

            return response()->json([
                'success' => true,
                'message' => 'Listing deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete listing',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Duplicate a listing
     */
    public function duplicate(Request $request, $id)
    {
        try {
            $user = Auth::user();
            $originalListing = ServiceCategoryItem::where('id', $id)->where('user_id', $user->id)->first();

            if (!$originalListing) {
                return response()->json([
                    'success' => false,
                    'message' => 'Listing not found or access denied'
                ], 404);
            }

            // Create duplicate
            $duplicatedListing = $originalListing->replicate();
            $duplicatedListing->title = $originalListing->title . ' (Copy)';
            $duplicatedListing->active = 0; // Start as inactive
            $duplicatedListing->views = 0; // Reset views
            $duplicatedListing->created_at = now();
            $duplicatedListing->updated_at = now();
            $duplicatedListing->save();

            return response()->json([
                'success' => true,
                'message' => 'Listing duplicated successfully',
                'data' => new ServiceCategoryItemResource($duplicatedListing->load(['serviceCategory', 'facilities']))
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to duplicate listing',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk update listing status
     */
    public function bulkUpdateStatus(Request $request)
    {
        try {
            $request->validate([
                'listing_ids' => 'required|array',
                'listing_ids.*' => 'integer|exists:service_category_items,id',
                'status' => 'required|string|in:active,inactive'
            ]);

            $user = Auth::user();
            $listingIds = $request->listing_ids;
            $status = $request->status === 'active' ? 1 : 0;

            $updated = ServiceCategoryItem::where('user_id', $user->id)
                ->whereIn('id', $listingIds)
                ->update(['active' => $status]);

            return response()->json([
                'success' => true,
                'message' => "Successfully updated {$updated} listings",
                'data' => [
                    'updated_count' => $updated,
                    'status' => $request->status
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to bulk update status',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk delete listings
     */
    public function bulkDelete(Request $request)
    {
        try {
            $request->validate([
                'listing_ids' => 'required|array',
                'listing_ids.*' => 'integer|exists:service_category_items,id'
            ]);

            $user = Auth::user();
            $listingIds = $request->listing_ids;

            // Get listings to delete (for file cleanup)
            $listings = ServiceCategoryItem::where('user_id', $user->id)
                ->whereIn('id', $listingIds)
                ->get();

            // Delete associated files
            foreach ($listings as $listing) {
                if ($listing->image) {
                    Storage::disk('public')->delete($listing->image);
                }
                if ($listing->video) {
                    Storage::disk('public')->delete($listing->video);
                }
            }

            // Delete listings
            $deleted = ServiceCategoryItem::where('user_id', $user->id)
                ->whereIn('id', $listingIds)
                ->delete();

            return response()->json([
                'success' => true,
                'message' => "Successfully deleted {$deleted} listings",
                'data' => [
                    'deleted_count' => $deleted
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to bulk delete listings',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Upload image for property
     */
    public function uploadImage(Request $request)
    {
        try {
            $request->validate([
                'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:5120', // 5MB max
                'type' => 'nullable|string|in:property,profile',
                'index' => 'nullable|integer'
            ]);

            $image = $request->file('image');
            $type = $request->type ?? 'property';
            $index = $request->index ?? 0;

            // Generate unique filename
            $filename = time() . '_' . $index . '.' . $image->getClientOriginalExtension();
            $path = $image->storeAs("uploads/{$type}", $filename, 'public');

            return response()->json([
                'success' => true,
                'message' => 'Image uploaded successfully',
                'data' => [
                    'url' => Storage::url($path),
                    'path' => $path,
                    'filename' => $filename,
                    'size' => $image->getSize(),
                    'mime_type' => $image->getMimeType()
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload image',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Upload video for property
     */
    public function uploadVideo(Request $request)
    {
        try {
            $request->validate([
                'video' => 'required|mimes:mp4,mov,avi,wmv|max:51200', // 50MB max
                'type' => 'nullable|string|in:property,profile'
            ]);

            $video = $request->file('video');
            $type = $request->type ?? 'property';

            // Generate unique filename
            $filename = time() . '_video.' . $video->getClientOriginalExtension();
            $path = $video->storeAs("uploads/{$type}", $filename, 'public');

            return response()->json([
                'success' => true,
                'message' => 'Video uploaded successfully',
                'data' => [
                    'url' => Storage::url($path),
                    'path' => $path,
                    'filename' => $filename,
                    'size' => $video->getSize(),
                    'mime_type' => $video->getMimeType()
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload video',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get property creation metadata
     */
    public function getCreationMetadata(Request $request)
    {
        try {
            // Get categories
            $categories = ServiceCategory::where('active', 1)->get(['id', 'name', 'name_ar']);

            // Get property types (you may need to create this table)
            $propertyTypes = [
                ['id' => 1, 'name' => 'Entire Place', 'name_ar' => 'المكان بالكامل', 'description' => 'Guests have the entire place to themselves'],
                ['id' => 2, 'name' => 'Private Room', 'name_ar' => 'غرفة خاصة', 'description' => 'Guests have a private room in a shared space'],
                ['id' => 3, 'name' => 'Shared Room', 'name_ar' => 'غرفة مشتركة', 'description' => 'Guests sleep in a room shared with others']
            ];

            // Get cancellation policies
            $cancellationPolicies = [
                ['id' => 1, 'name' => 'Flexible', 'name_ar' => 'مرن', 'description' => 'Full refund 1 day prior to arrival'],
                ['id' => 2, 'name' => 'Moderate', 'name_ar' => 'متوسط', 'description' => 'Full refund 5 days prior to arrival'],
                ['id' => 3, 'name' => 'Strict', 'name_ar' => 'صارم', 'description' => 'Full refund 14 days prior to arrival']
            ];

            // Get facilities
            $facilities = [
                // Essential
                ['id' => 1, 'name' => 'WiFi', 'name_ar' => 'واي فاي', 'icon' => 'wifi', 'category' => 'Essential'],
                ['id' => 2, 'name' => 'Air Conditioning', 'name_ar' => 'تكييف', 'icon' => 'ac_unit', 'category' => 'Essential'],
                ['id' => 3, 'name' => 'Kitchen', 'name_ar' => 'مطبخ', 'icon' => 'kitchen', 'category' => 'Essential'],
                ['id' => 4, 'name' => 'Parking', 'name_ar' => 'موقف سيارات', 'icon' => 'local_parking', 'category' => 'Essential'],

                // Entertainment
                ['id' => 5, 'name' => 'TV', 'name_ar' => 'تلفزيون', 'icon' => 'tv', 'category' => 'Entertainment'],
                ['id' => 6, 'name' => 'Netflix', 'name_ar' => 'نتفليكس', 'icon' => 'movie', 'category' => 'Entertainment'],

                // Outdoor
                ['id' => 7, 'name' => 'Pool', 'name_ar' => 'مسبح', 'icon' => 'pool', 'category' => 'Outdoor'],
                ['id' => 8, 'name' => 'Garden', 'name_ar' => 'حديقة', 'icon' => 'local_florist', 'category' => 'Outdoor'],
                ['id' => 9, 'name' => 'Balcony', 'name_ar' => 'شرفة', 'icon' => 'balcony', 'category' => 'Outdoor'],

                // Safety
                ['id' => 10, 'name' => 'Security Camera', 'name_ar' => 'كاميرا أمان', 'icon' => 'security', 'category' => 'Safety'],
                ['id' => 11, 'name' => 'Smoke Detector', 'name_ar' => 'كاشف دخان', 'icon' => 'smoke_detector', 'category' => 'Safety'],

                // Fitness
                ['id' => 12, 'name' => 'Gym', 'name_ar' => 'صالة رياضية', 'icon' => 'fitness_center', 'category' => 'Fitness'],
                ['id' => 13, 'name' => 'Spa', 'name_ar' => 'سبا', 'icon' => 'spa', 'category' => 'Fitness'],

                // Family
                ['id' => 14, 'name' => 'Baby Crib', 'name_ar' => 'سرير أطفال', 'icon' => 'child_care', 'category' => 'Family'],
                ['id' => 15, 'name' => 'High Chair', 'name_ar' => 'كرسي عالي', 'icon' => 'chair', 'category' => 'Family']
            ];

            return response()->json([
                'success' => true,
                'data' => [
                    'categories' => $categories,
                    'property_types' => $propertyTypes,
                    'cancellation_policies' => $cancellationPolicies,
                    'facilities' => $facilities
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch metadata',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Save property draft
     */
    public function saveDraft(Request $request)
    {
        try {
            $user = Auth::user();

            $request->validate([
                'draft_data' => 'required|array',
                'step' => 'nullable|integer|min:0|max:10',
            ]);

            // For now, store in a simple way (you may want to create a drafts table)
            $draftData = [
                'user_id' => $user->id,
                'draft_data' => $request->draft_data,
                'step' => $request->step ?? 0,
                'last_updated' => now(),
            ];

            // Store in cache or database (using cache for simplicity)
            $draftId = 'property_draft_' . $user->id;
            cache()->put($draftId, $draftData, now()->addDays(7)); // Keep for 7 days

            return response()->json([
                'success' => true,
                'message' => 'Draft saved successfully',
                'data' => [
                    'draft_id' => $draftId,
                    'last_updated' => $draftData['last_updated']
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to save draft',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Load property draft
     */
    public function loadDraft(Request $request, $id)
    {
        try {
            $user = Auth::user();
            $draftId = 'property_draft_' . $user->id;

            $draftData = cache()->get($draftId);

            if (!$draftData) {
                return response()->json([
                    'success' => false,
                    'message' => 'Draft not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $draftData
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to load draft',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
