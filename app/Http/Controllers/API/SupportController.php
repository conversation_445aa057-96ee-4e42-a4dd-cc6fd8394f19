<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\SupportTicket;
use App\Models\SupportFaq;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class SupportController extends Controller
{
    /**
     * Get all FAQs.
     */
    public function getFaqs(Request $request): JsonResponse
    {
        try {
            $category = $request->get('category');
            
            $faqs = SupportFaq::active()
                ->ordered()
                ->when($category, function ($query, $category) {
                    return $query->where('category', $category);
                })
                ->get()
                ->map(function ($faq) {
                    return [
                        'id' => $faq->id,
                        'question' => $faq->question,
                        'answer' => $faq->answer,
                        'category' => $faq->category,
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => $faqs,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب الأسئلة الشائعة',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get user's support tickets.
     */
    public function getTickets(): JsonResponse
    {
        try {
            $tickets = SupportTicket::where('user_id', Auth::id())
                ->orderBy('created_at', 'desc')
                ->get()
                ->map(function ($ticket) {
                    return [
                        'id' => $ticket->id,
                        'subject' => $ticket->subject,
                        'description' => $ticket->description,
                        'category' => $ticket->category,
                        'category_display' => $ticket->category_display,
                        'priority' => $ticket->priority,
                        'priority_display' => $ticket->priority_display,
                        'status' => $ticket->status,
                        'status_display' => $ticket->status_display,
                        'created_at' => $ticket->created_at->format('Y-m-d H:i'),
                        'resolved_at' => $ticket->resolved_at?->format('Y-m-d H:i'),
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => $tickets,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب التذاكر',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Create a new support ticket.
     */
    public function createTicket(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'subject' => 'required|string|max:255',
                'description' => 'required|string',
                'category' => 'required|in:general,technical,billing,property,booking,account',
                'priority' => 'sometimes|in:low,medium,high,urgent',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'بيانات غير صحيحة',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $ticket = SupportTicket::create([
                'user_id' => Auth::id(),
                'subject' => $request->subject,
                'description' => $request->description,
                'category' => $request->category,
                'priority' => $request->priority ?? 'medium',
            ]);

            return response()->json([
                'success' => true,
                'message' => 'تم إنشاء التذكرة بنجاح',
                'data' => [
                    'id' => $ticket->id,
                    'subject' => $ticket->subject,
                    'status' => $ticket->status,
                    'status_display' => $ticket->status_display,
                    'created_at' => $ticket->created_at->format('Y-m-d H:i'),
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إنشاء التذكرة',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get support categories.
     */
    public function getCategories(): JsonResponse
    {
        $categories = [
            ['value' => 'general', 'label' => 'عام'],
            ['value' => 'technical', 'label' => 'تقني'],
            ['value' => 'billing', 'label' => 'الفواتير'],
            ['value' => 'property', 'label' => 'العقارات'],
            ['value' => 'booking', 'label' => 'الحجوزات'],
            ['value' => 'account', 'label' => 'الحساب'],
        ];

        return response()->json([
            'success' => true,
            'data' => $categories,
        ]);
    }
}
