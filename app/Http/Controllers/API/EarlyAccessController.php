<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\EarlyAccessFeature;
use App\Models\UserEarlyAccess;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class EarlyAccessController extends Controller
{
    /**
     * Get all available early access features
     */
    public function index(Request $request)
    {
        try {
            $user = Auth::user();
            $features = EarlyAccessFeature::active()
                ->ordered()
                ->get();

            $featuresData = $features->map(function ($feature) use ($user) {
                $userEnrollment = $user->earlyAccessFeatures()
                    ->where('feature_id', $feature->id)
                    ->first();

                return [
                    'id' => $feature->id,
                    'name' => $feature->name_ar,
                    'description' => $feature->description_ar,
                    'icon' => $feature->icon,
                    'status' => $feature->status,
                    'status_display' => $feature->status_display,
                    'status_color' => $feature->status_color,
                    'progress_percentage' => $feature->progress_percentage,
                    'estimated_release_date' => $feature->estimated_release_date?->format('Y-m-d'),
                    'is_premium' => $feature->is_premium,
                    'enrollment_count' => $feature->enrollment_count,
                    'waitlist_count' => $feature->waitlist_count,
                    'average_rating' => round($feature->average_rating ?? 0, 1),
                    'user_enrollment' => $userEnrollment ? [
                        'status' => $userEnrollment->pivot->status,
                        'enrolled_at' => $userEnrollment->pivot->enrolled_at?->format('Y-m-d H:i:s'),
                        'last_used_at' => $userEnrollment->pivot->last_used_at?->format('Y-m-d H:i:s'),
                        'rating' => $userEnrollment->pivot->rating,
                        'feedback' => $userEnrollment->pivot->feedback,
                    ] : null,
                    'can_enroll' => $feature->canUserEnroll($user),
                ];
            });

            return $this->ApiResponse(
                true,
                trans('api.data_retrieved_success'),
                $featuresData->toArray(),
                200
            );
        } catch (\Exception $e) {
            return $this->ApiResponse(
                false,
                trans('api.something_wrong'),
                [],
                500
            );
        }
    }

    /**
     * Get user's enrolled early access features
     */
    public function myFeatures(Request $request)
    {
        try {
            $user = Auth::user();
            $features = $user->enrolledEarlyAccessFeatures()->get();

            $featuresData = $features->map(function ($feature) {
                return [
                    'id' => $feature->id,
                    'name' => $feature->name_ar,
                    'description' => $feature->description_ar,
                    'icon' => $feature->icon,
                    'status' => $feature->status,
                    'status_display' => $feature->status_display,
                    'progress_percentage' => $feature->progress_percentage,
                    'enrolled_at' => $feature->pivot->enrolled_at?->format('Y-m-d H:i:s'),
                    'last_used_at' => $feature->pivot->last_used_at?->format('Y-m-d H:i:s'),
                    'rating' => $feature->pivot->rating,
                    'feedback' => $feature->pivot->feedback,
                ];
            });

            return $this->ApiResponse(
                true,
                trans('api.data_retrieved_success'),
                $featuresData->toArray(),
                200
            );
        } catch (\Exception $e) {
            return $this->ApiResponse(
                false,
                trans('api.something_wrong'),
                [],
                500
            );
        }
    }

    /**
     * Enroll in early access feature
     */
    public function enroll(Request $request)
    {
        try {
            $request->validate([
                'feature_id' => 'required|integer|exists:early_access_features,id',
            ]);

            $user = Auth::user();
            $featureId = $request->feature_id;

            $success = $user->enrollInEarlyAccess($featureId);

            if (!$success) {
                return $this->ApiResponse(
                    false,
                    'لا يمكن التسجيل في هذه الميزة',
                    [],
                    400
                );
            }

            return $this->ApiResponse(
                true,
                'تم التسجيل في الميزة بنجاح',
                [],
                201
            );
        } catch (\Exception $e) {
            return $this->ApiResponse(
                false,
                trans('api.something_wrong'),
                [],
                500
            );
        }
    }

    /**
     * Unenroll from early access feature
     */
    public function unenroll(Request $request)
    {
        try {
            $request->validate([
                'feature_id' => 'required|integer|exists:early_access_features,id',
            ]);

            $user = Auth::user();
            $featureId = $request->feature_id;

            $enrollment = UserEarlyAccess::where('user_id', $user->id)
                ->where('feature_id', $featureId)
                ->first();

            if (!$enrollment) {
                return $this->ApiResponse(
                    false,
                    'لم يتم العثور على التسجيل',
                    [],
                    404
                );
            }

            $enrollment->remove();

            return $this->ApiResponse(
                true,
                'تم إلغاء التسجيل بنجاح',
                [],
                200
            );
        } catch (\Exception $e) {
            return $this->ApiResponse(
                false,
                trans('api.something_wrong'),
                [],
                500
            );
        }
    }

    /**
     * Submit feedback for early access feature
     */
    public function submitFeedback(Request $request)
    {
        try {
            $request->validate([
                'feature_id' => 'required|integer|exists:early_access_features,id',
                'feedback' => 'required|string|max:1000',
                'rating' => 'nullable|integer|min:1|max:5',
            ]);

            $user = Auth::user();
            $featureId = $request->feature_id;

            $enrollment = UserEarlyAccess::where('user_id', $user->id)
                ->where('feature_id', $featureId)
                ->where('status', 'enrolled')
                ->first();

            if (!$enrollment) {
                return $this->ApiResponse(
                    false,
                    'يجب أن تكون مسجلاً في الميزة لتقديم التقييم',
                    [],
                    403
                );
            }

            $enrollment->submitFeedback($request->feedback, $request->rating);

            return $this->ApiResponse(
                true,
                'تم إرسال التقييم بنجاح',
                [],
                200
            );
        } catch (\Exception $e) {
            return $this->ApiResponse(
                false,
                trans('api.something_wrong'),
                [],
                500
            );
        }
    }

    /**
     * Mark feature as used
     */
    public function markAsUsed(Request $request)
    {
        try {
            $request->validate([
                'feature_id' => 'required|integer|exists:early_access_features,id',
            ]);

            $user = Auth::user();
            $featureId = $request->feature_id;

            $enrollment = UserEarlyAccess::where('user_id', $user->id)
                ->where('feature_id', $featureId)
                ->where('status', 'enrolled')
                ->first();

            if (!$enrollment) {
                return $this->ApiResponse(
                    false,
                    'لم يتم العثور على التسجيل',
                    [],
                    404
                );
            }

            $enrollment->markAsUsed();

            return $this->ApiResponse(
                true,
                'تم تحديث آخر استخدام',
                [],
                200
            );
        } catch (\Exception $e) {
            return $this->ApiResponse(
                false,
                trans('api.something_wrong'),
                [],
                500
            );
        }
    }

    /**
     * Get feature statistics (for admin/analytics)
     */
    public function getFeatureStats(Request $request)
    {
        try {
            $request->validate([
                'feature_id' => 'required|integer|exists:early_access_features,id',
            ]);

            $feature = EarlyAccessFeature::find($request->feature_id);

            $stats = [
                'total_enrollments' => $feature->enrollment_count,
                'waitlist_count' => $feature->waitlist_count,
                'average_rating' => round($feature->average_rating ?? 0, 1),
                'feedback_count' => $feature->enrolledUsers()->wherePivotNotNull('feedback')->count(),
                'active_users_last_week' => $feature->enrolledUsers()
                    ->wherePivot('last_used_at', '>=', now()->subWeek())
                    ->count(),
            ];

            return $this->ApiResponse(
                true,
                trans('api.data_retrieved_success'),
                $stats,
                200
            );
        } catch (\Exception $e) {
            return $this->ApiResponse(
                false,
                trans('api.something_wrong'),
                [],
                500
            );
        }
    }
}
