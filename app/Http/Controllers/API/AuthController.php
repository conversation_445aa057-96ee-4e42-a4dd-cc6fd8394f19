<?php
namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Resources\FollowResource;
use App\Http\Resources\UserResource;
use App\Models\Admin\UserFollow;
use App\Models\CredentialSetting;
use App\Models\User;
use App\Services\Notifications\FcmService;
use App\Services\SMS\MsegatSmsService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class AuthController extends Controller
{
    public const STRNULLABLLE = 'nullable|string';
    public const UNAUTHORIZED = 'api.unauthorized_access';

    // Login by token API
    public function login_token(Request $request)
    {
        $data = $request->validate([
            'login_token'     => 'required|string',
            'login_method'    => 'required|string',
            'full_name'       => 'nullable|string',
            'email'           => 'nullable|email',
            'phone'           => ['nullable', 'numeric'],
            'profile_picture' => 'nullable|image|mimes:jpeg,png,jpg',
            'photo_url'       => 'nullable|string',
            'birthdate'       => 'nullable|date_format:Y-m-d|before:today',
            'gender'          => 'nullable|numeric|min:1|max:2',
            'fb_token'        => self::STRNULLABLLE,
        ]);
        $data['remember_token'] = Str::random(10);

        if (empty($data['gender'])):
            $data['gender'] = 0;
        endif;

        $data['user_type_id'] = 2;

        $user = User::updateOrCreate(['login_token' => $data['login_token']], $data);
        if ($user) {
            $this->update_token_lang($request, $user);
            $token        = $this->create_access_token($user, 'GatherPoint user login');
            $responseData = new UserResource($user, isset($token['token']) ? $token['token'] : null, isset($token['accessToken']) ? $token['accessToken'] : null);
                                                                                             // Return json response
            return $this->ApiResponse(true, trans('api.login_success'), $responseData, 200); //success
        } else {
            // Return json response
            $this->ApiResponse(false, trans(self::UNAUTHORIZED), [], 401);
        }
    }

    // Login as a guest API
    public function login_guest(Request $request)
    {
        // Generate a unique login token for the guest user
        $data = [
            'login_token'     => Str::random(40),                         // Unique token for the guest
            'login_method'    => 'guest',                                 // Set login method as 'guest'
            'full_name'       => 'Guest User',                            // Default name
            'email'           => 'guest_' . uniqid() . '@gatherpoint.sa', // Unique email
            'phone'           => null,                                    // No phone for guest
            'profile_picture' => null,                                    // No profile picture for guest
            'photo_url'       => null,                                    // No photo URL for guest
            'birthdate'       => null,                                    // No birthdate for guest
            'gender'          => 0,                                       // Default gender
            'fb_token'        => null,                                    // No Facebook token for guest
            'remember_token'  => Str::random(10),                         // Random remember token
            'user_type_id'    => 2,                                       // Set user type (if applicable)
            'is_guest'        => true,                                    // Mark as guest
        ];

        // Create or update the guest user
        $user = User::updateOrCreate(['login_token' => $data['login_token']], $data);

        if ($user) {
            // Update token and language (if applicable)
            $this->update_token_lang($request, $user);

            // Generate an access token
            $token = $this->create_access_token($user, 'GatherPoint guest login');

            // Prepare the response data
            $responseData = new UserResource($user, $token['token'] ?? null, $token['accessToken'] ?? null);

            // Return JSON response
            return $this->ApiResponse(true, trans('api.login_success'), $responseData, 200);
        } else {
            // Return JSON response for error
            return $this->ApiResponse(false, trans(self::UNAUTHORIZED), [], 401);
        }
    }

    // Generate random OTP
    private function generate_otp($phone, $auth = 0)
    {
        if ($phone === "512345678") {
            $otp = 1234;
        } else {
            $otp = rand(1000, 9999);
        }
        DB::table('user_otps')->insert(['phone' => $phone, 'otp' => $otp, 'auth' => $auth, 'created_at' => Carbon::now()]);
        return $otp;
    }

    // Validate OTP that has been sent to the user after registration
    public function validate_otp(Request $request)
    {
        // Validate
        $request->validate([
            'otp'   => 'required|numeric',
            'phone' => ['required', 'regex:/^(009665|9665|\+9665|05|5)(5|0|3|6|4|9|1|8|7)([0-9]{7})$/', 'exists:users,phone'],
        ]);
        $user = User::where('phone', $request->phone)->first();
        $data = DB::table('user_otps')->where('otp', $request->otp)->where('phone', $request->phone)->where('created_at', '>', Carbon::now()->subMinutes(10))->first();
        if ($user && $data) {
            $res   = [];
            $token = $this->create_access_token($user, 'GatherPoint user login');
            $user  = User::where('phone', $request->phone)->first();
            $res   = new UserResource($user, isset($token['token']) ? $token['token'] : null, isset($token['accessToken']) ? $token['accessToken'] : null);
            DB::table('user_otps')->where('otp', $request->otp)->where('phone', $request->phone)->delete();
            // Return json response
            return $this->ApiResponse(true, trans('api.valid_otp'), $res, 200);
        } else {
            // Return json response
            return $this->ApiResponse(false, trans('api.invalid_otp'), [], 401);
        }
    }

    // Resend OTP
    public function resend_otp(Request $request)
    {
        $request->validate([
            'phone' => ['required', 'regex:/^(009665|9665|\+9665|05|5)(5|0|3|6|4|9|1|8|7)([0-9]{7})$/', 'exists:users,phone'],
        ]);

        // generate otp & send it to user phone
        $successed = $this->generatePhoneOtp($request->phone, false);

        if ($successed) {
            // response
            return $this->ApiResponse(true, trans('api.otp_sent_success'), [], 201);
        } else {
            return $this->ApiResponse(false, trans('api.otp_not_sent'), [], 401);
        }
    }

    // Generate OTP on resend OTP
    private function generatePhoneOtp($phone, $register = true)
    {
        try {
            $otp = $this->generate_otp($phone, $register);

            if ($phone === "512345678") {
                return true;
            }
            
            // Initialize SMS service
            $smsService = new MsegatSmsService();

            // Send OTP via SMS
            $result = $smsService->sendOtp($phone, $otp, 'Gather Point');

            if ($result['success']) {
                Log::info('OTP sent successfully', [
                    'phone'    => $phone,
                    'otp'      => $otp,
                    'response' => $result['response'],
                ]);
                return true;
            } else {
                Log::error('Failed to send OTP SMS', [
                    'phone' => $phone,
                    'error' => $result['message'],
                ]);
                return false;
            }
        } catch (\Exception $e) {
            Log::error('Exception in generatePhoneOtp', [
                'phone' => $phone,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    // Login API
    public function login(Request $request)
    {
        $responseData = [];
        // Validate
        $request->validate([
            'phone'    => ['required', 'numeric', 'regex:/^(009665|9665|\+9665|05|5)(5|0|3|6|4|9|1|8|7)([0-9]{7})$/'],
            'fb_token' => self::STRNULLABLLE,
        ]);
        $check = User::where(['phone' => $request->phone])->where('user_type_id', 2)->first();
        $user  = $check ? $check : User::create([
            'phone' => $request->phone,
            'user_type_id' => 2,
            'is_hoster_mode' => false // Default to client mode
        ]);
        if ($user) {
            $this->generatePhoneOtp($request->phone);
            $this->update_token_lang($request, $user);
            $responseData = new UserResource($user);
            return $this->ApiResponse(true, trans('api.login_success'), $responseData, 200); //success
        } else {
            return $this->ApiResponse(false, trans(self::UNAUTHORIZED), $responseData, 401); //not found user
        }
    }

    // Logout API
    public function logout(Request $request)
    {
        $user   = auth('api')->user();
        $logout = false;
        if ($user) {
            $user->update(['fb_token' => null]);
            $logout = $user->token()->revoke();
        }
        // Return json response
        return $this->ApiResponse(
            $logout ? true : false,                                // Response status (Boolean)
            trans('api.logout_' . ($logout ? 'success' : 'fail')), // Response message (String)
            [],                                                    // Response data (Array)
            $logout ? 201 : 422                                    // HTTP response status code (Integer)
        );
    }

    // Delete account API
    public function delete_account(Request $request)
    {
        $user = $request->user();
        // delete related tokens then delete account
        $user->tokens()->delete();

        $user->deleted_email = $user->email;
        $user->deleted_phone = $user->phone;
        $user->email         = $user->phone         = null;
        $user->save();
        $deleted = $user->delete();

        return response()->json(['message' => trans('api.delete_account_' . ($deleted ? 'success' : 'fail'))]);
    }

    // Validate token still active or has been expired and return user data
    public function validate_token(Request $request)
    {
        $user = auth('api')->user();
        if ($user) {
            $token = $user ? $user->token() : null;
            if ($token) {
                $now        = Carbon::now()->toDateTimeString();
                $expires_at = Carbon::parse($token->expires_at)->toDateTimeString();
                if ($expires_at > $now) {
                    // Token is valid - return user data with updated information
                    $responseData = new UserResource($user);
                    return $this->ApiResponse(true, trans('api.token_valid'), $responseData, 200);
                } else {
                    return $this->ApiResponse(false, trans('api.token_expired'), [], 401);
                }
            } else {
                return $this->ApiResponse(false, trans(self::UNAUTHORIZED), [], 401);
            }
        } else {
            return $this->ApiResponse(false, trans(self::UNAUTHORIZED), [], 401);
        }
    }

    // Toggle hoster mode for authenticated users
    public function toggle_hoster_mode(Request $request)
    {
        $user = auth('api')->user();

        if (!$user) {
            return $this->ApiResponse(false, trans('api.unauthorized'), [], 401);
        }

        // Validate request
        $data = $request->validate([
            'is_hoster_mode' => 'required|boolean'
        ]);

        try {
            // Update user's hoster mode
            $user->is_hoster_mode = $data['is_hoster_mode'];
            $user->save();

            // Return updated user data
            $responseData = new UserResource($user);

            $message = $data['is_hoster_mode']
                ? 'تم تفعيل وضع المضيف بنجاح'
                : 'تم إلغاء تفعيل وضع المضيف بنجاح';

            return $this->ApiResponse(true, $message, $responseData, 200);

        } catch (\Exception $e) {
            return $this->ApiResponse(false, trans('api.something_went_wrong'), [], 500);
        }
    }

    // Update firebase token for user (To be able to receive notifications)
    private function update_token_lang($request, $user)
    {
        $update = [];
        //update default lang to user
        $lang = $request->hasHeader('X-localization') ? $request->Header('X-localization') : 'ar';
        if ($user && $lang != $user->lang) {
            $update['lang'] = $lang;
        }

        //update firebase token to user
        if ($request->fb_token && $user) {
            $update['fb_token'] = $request->fb_token;
        }

        // Apply user updates if exist
        if ($update && $user) {
            User::where('id', $user->id)->update($update);
            return true;
        }
        return false;
    }

    // Create user access token
    private function create_access_token($user, $desc = 'Laravel Password Grant User')
    {
        $tokenResult = $user->createToken($desc);
        $token       = $tokenResult ? $tokenResult->token : null;
        $accessToken = $tokenResult ? $tokenResult->accessToken : null;
        return ['token' => $token, 'accessToken' => $accessToken];
    }

    // Edit user profile API
    public function edit_profile(Request $request)
    {
        $user              = $request->user();
        $credentialSetting = CredentialSetting::firstOrCreate(['id' => 1]);
        // Validate
        $data = $request->validate([
            'full_name'       => 'nullable|string',
            'email'           => ['nullable', 'email', Rule::unique('users', 'email')->ignore($user->id, 'id')],
            'phone'           => ['nullable', 'numeric', 'regex:/^(009665|9665|\+9665|05|5)(5|0|3|6|4|9|1|8|7)([0-9]{7})$/', Rule::unique('users', 'phone')->ignore($user->id, 'id')],
            'profile_picture' => 'nullable|image|mimes:jpeg,png,jpg',
            'birthdate'       => 'nullable|date_format:Y-m-d|before:today',
            'gender'          => 'nullable|numeric|min:1|max:2',
            'fb_token'        => 'nullable|string',
        ]);

        if (empty($data['fb_token'])) {
            unset($data['fb_token']);
        }

        if ($request->profile_picture):
            // Images
            $hashedFile = $request->profile_picture->hashName();
            Storage::disk('public')->put('users', $request->profile_picture);
            $data['profile_picture'] = $hashedFile;
        endif;

        // Update user info
        $updated = $user ? $user->update($data) : null;

        $this->update_token_lang($request, $user);

        // Return json response
        return $this->ApiResponse(
            $updated ? true : false,                                        // Response status (Boolean)
            trans('api.profile_update_' . ($updated ? 'success' : 'fail')), // Response message (String)
            $user ? new UserResource($user) : [],                           // Response data (Array)
            $updated ? 200 : 422,                                           // HTTP response status code (Integer)
        );
    }

    // Get User Follow List API
    public function follow_list(Request $request)
    {
        $responseData = [];
        // Validate
        $request->validate([
            'following' => 'nullable|integer',
            'followers' => 'nullable|integer',
        ]);
        $user = auth()->user();
        if ($request->following) {
            $followingList                  = FollowResource::collection(UserFollow::where('user_id', $user->id)->get(), true);
            $responseData['following_list'] = $followingList;
        }
        if ($request->followers) {
            $followersList                  = FollowResource::collection(UserFollow::where('follower_id', $user->id)->get());
            $responseData['followers_list'] = $followersList;
        }
        return $this->ApiResponse(true, trans('api.data_retrieved_success'), $responseData, 200); //success

    }

    // Set follow API
    public function follow(Request $request)
    {
        // Validate
        $request->validate([
            'id' => 'required|integer',
        ]);
        $following = User::find($request->id);
        $user      = auth()->user();
        if ($following && $user && $following->id !== $user->id) {
            $follow = UserFollow::updateOrCreate(['user_id' => $user->id, 'follower_id' => $following->id]);
            return $this->ApiResponse($follow ? true : false, $follow ? trans('api.data_submitted_success') : trans('api.something_wrong'), [], $follow ? 200 : 422);
        }
        return $this->ApiResponse(false, trans('api.something_wrong'), [], 422); //failed
    }

    // Unset follow API
    public function unfollow(Request $request)
    {
        // Validate
        $request->validate([
            'id' => 'required|integer',
        ]);
        $following = User::find($request->id);
        $user      = auth()->user();
        if ($following && $user && $following->id !== $user->id) {
            $follow = UserFollow::where(['user_id' => $user->id, 'follower_id' => $following->id])->first();
            if ($follow) {
                $follow->delete();
            }
            return $this->ApiResponse($follow ? true : false, $follow ? trans('api.data_submitted_success') : trans('api.something_wrong'), [], $follow ? 200 : 422);
        }
        return $this->ApiResponse(false, trans('api.something_wrong'), [], 422); //failed
    }

    // Get client info API (current authenticated user)
    public function client_info(Request $request)
    {
        $user = auth('api')->user();

        if (!$user) {
            return $this->ApiResponse(false, trans('api.unauthorized'), [], 401);
        }

        try {
            // Return current user data
            $responseData = new UserResource($user);
            return $this->ApiResponse(true, trans('api.data_retrieved_success'), $responseData, 200);
        } catch (\Exception $e) {
            return $this->ApiResponse(false, trans('api.something_went_wrong'), [], 500);
        }
    }

    // Get user info API
    public function user_info(Request $request)
    {
        // Validate
        $request->validate([
            'id' => 'required|integer',
        ]);
        $info = User::find($request->id);
        $user = auth()->user();
        if ($info && $user) {
            return $this->ApiResponse(true, trans('api.data_submitted_success'), new UserResource($info, null, null, false), 200);
        }
        return $this->ApiResponse(false, trans('api.something_wrong'), [], 422); //failed
    }

    // Send Push Notification API
    public function notification(Request $request)
    {
        // Validate
        $request->validate([
            'id'    => 'required|integer',
            'title' => 'required|string',
            'body'  => 'required|string',
        ]);
        $reciver = User::find($request->id);
        $user    = auth()->user();
        if ($user && $reciver && $reciver->fb_token) {
            $operation = FcmService::send($request->title, $request->body, [$reciver->fb_token]);
            return $this->ApiResponse($operation ? true : false, $operation ? trans('api.data_submitted_success') : trans('api.something_wrong'), [], $operation ? 200 : 422);
        }
        return $this->ApiResponse(false, trans('api.something_wrong'), [], 422); //failed
    }

    // Test SMS functionality
    public function test_sms(Request $request)
    {
        $request->validate([
            'phone'   => ['required', 'regex:/^(009665|9665|\+9665|05|5)(5|0|3|6|4|9|1|8|7)([0-9]{7})$/'],
            'message' => 'nullable|string|max:160',
        ]);

        try {
            $smsService = new MsegatSmsService();
            $message    = $request->message ?? 'هذه رسالة تجريبية من تطبيق Gather Point';

            $result = $smsService->sendSms($request->phone, $message);

            if ($result['success']) {
                return $this->ApiResponse(true, 'SMS sent successfully', [
                    'phone'    => $request->phone,
                    'message'  => $message,
                    'response' => $result['response'],
                ], 200);
            } else {
                return $this->ApiResponse(false, 'Failed to send SMS: ' . $result['message'], [
                    'error' => $result['message'],
                ], 400);
            }
        } catch (\Exception $e) {
            Log::error('SMS Test Exception', [
                'phone' => $request->phone,
                'error' => $e->getMessage(),
            ]);

            return $this->ApiResponse(false, 'SMS service error: ' . $e->getMessage(), [], 500);
        }
    }

}
