<?php
namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Resources\PropertyTypesResource;
use App\Models\Admin\PropertyType;
use Illuminate\Http\Request;

class PropertyTypesController extends Controller
{
    public function __construct()
    {
    }

    /**
     * Get facilities list.
     *
     * @return \Illuminate\Http\Response
     */
    public function get_list(Request $request)
    {
        $list = PropertyType::orderBy('order')->get();
        // Return json response
        return $this->ApiResponse(
            ! empty($list) ? true : false,                                                 // Response status (Boolean)
            trans('api.' . (! empty($list) ? 'data_retrieved_success' : 'no_data_found')), // Response message (String)
            PropertyTypesResource::collection($list),                                     // Response data (Array)
            ! empty($list) ? 200 : 404,                                                    // HTTP response status code (Integer)
        );
    }
}
