<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\CoHost;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class CoHostController extends Controller
{
    /**
     * Get user's co-hosts (for main hosts)
     */
    public function index(Request $request)
    {
        try {
            $user = Auth::user();
            $coHosts = $user->acceptedCoHosts()->get();

            $coHostsData = $coHosts->map(function ($coHostRelation) {
                $coHost = $coHostRelation->coHost;
                return [
                    'id' => $coHostRelation->id,
                    'co_host' => [
                        'id' => $coHost->id,
                        'full_name' => $coHost->full_name,
                        'email' => $coHost->email,
                        'phone' => $coHost->phone,
                        'profile_photo_url' => $coHost->profile_photo_url,
                    ],
                    'property' => $coHostRelation->property ? [
                        'id' => $coHostRelation->property->id,
                        'title' => $coHostRelation->property->title,
                    ] : null,
                    'permissions' => $coHostRelation->permissions,
                    'commission_percentage' => $coHostRelation->commission_percentage,
                    'accepted_at' => $coHostRelation->accepted_at->format('Y-m-d H:i:s'),
                ];
            });

            return $this->ApiResponse(
                true,
                trans('api.data_retrieved_success'),
                $coHostsData->toArray(),
                200
            );
        } catch (\Exception $e) {
            return $this->ApiResponse(
                false,
                trans('api.something_wrong'),
                [],
                500
            );
        }
    }

    /**
     * Get pending co-host invitations (received)
     */
    public function pendingInvitations(Request $request)
    {
        try {
            $user = Auth::user();
            $invitations = $user->pendingCoHostInvitationsReceived()->get();

            $invitationsData = $invitations->map(function ($invitation) {
                $host = $invitation->host;
                return [
                    'id' => $invitation->id,
                    'host' => [
                        'id' => $host->id,
                        'full_name' => $host->full_name,
                        'email' => $host->email,
                        'phone' => $host->phone,
                        'profile_photo_url' => $host->profile_photo_url,
                    ],
                    'property' => $invitation->property ? [
                        'id' => $invitation->property->id,
                        'title' => $invitation->property->title,
                        'image' => $invitation->property->image,
                    ] : null,
                    'permissions' => $invitation->permissions,
                    'commission_percentage' => $invitation->commission_percentage,
                    'message' => $invitation->message,
                    'created_at' => $invitation->created_at->format('Y-m-d H:i:s'),
                ];
            });

            return $this->ApiResponse(
                true,
                trans('api.data_retrieved_success'),
                $invitationsData->toArray(),
                200
            );
        } catch (\Exception $e) {
            return $this->ApiResponse(
                false,
                trans('api.something_wrong'),
                [],
                500
            );
        }
    }

    /**
     * Send co-host invitation
     */
    public function sendInvitation(Request $request)
    {
        try {
            $request->validate([
                'co_host_id' => 'required|integer|exists:users,id',
                'property_id' => 'nullable|integer|exists:items,id',
                'permissions' => 'nullable|array',
                'permissions.*' => 'string|in:' . implode(',', array_keys(CoHost::getAllPermissions())),
                'commission_percentage' => 'nullable|numeric|min:0|max:100',
                'message' => 'nullable|string|max:500',
            ]);

            $user = Auth::user();
            $coHostId = $request->co_host_id;

            // Can't invite yourself
            if ($user->id == $coHostId) {
                return $this->ApiResponse(
                    false,
                    'لا يمكنك دعوة نفسك كمضيف مشارك',
                    [],
                    400
                );
            }

            // Check if target user is a host
            $targetUser = User::find($coHostId);
            if ($targetUser->user_type_id != 2) {
                return $this->ApiResponse(
                    false,
                    'يمكن دعوة المضيفين فقط كمضيفين مشاركين',
                    [],
                    400
                );
            }

            // If property_id is provided, check if user owns it
            if ($request->property_id) {
                $property = $user->serviceCategoryItems()->find($request->property_id);
                if (!$property) {
                    return $this->ApiResponse(
                        false,
                        'العقار غير موجود أو غير مملوك لك',
                        [],
                        404
                    );
                }
            }

            $invitation = $user->inviteCoHost(
                $coHostId,
                $request->property_id,
                $request->permissions,
                $request->commission_percentage ?? 0,
                $request->message
            );

            if (!$invitation) {
                return $this->ApiResponse(
                    false,
                    'دعوة مضيف مشارك موجودة بالفعل',
                    [],
                    400
                );
            }

            return $this->ApiResponse(
                true,
                'تم إرسال دعوة المضيف المشارك بنجاح',
                ['invitation_id' => $invitation->id],
                201
            );
        } catch (\Exception $e) {
            return $this->ApiResponse(
                false,
                trans('api.something_wrong'),
                [],
                500
            );
        }
    }

    /**
     * Accept co-host invitation
     */
    public function acceptInvitation(Request $request)
    {
        try {
            $request->validate([
                'invitation_id' => 'required|integer|exists:co_hosts,id',
            ]);

            $user = Auth::user();
            $invitation = CoHost::find($request->invitation_id);

            // Check if user is the recipient of this invitation
            if ($invitation->co_host_id != $user->id) {
                return $this->ApiResponse(
                    false,
                    'غير مصرح لك بقبول هذه الدعوة',
                    [],
                    403
                );
            }

            if ($invitation->status != 'pending') {
                return $this->ApiResponse(
                    false,
                    'هذه الدعوة ليست معلقة',
                    [],
                    400
                );
            }

            $invitation->accept();

            return $this->ApiResponse(
                true,
                'تم قبول دعوة المضيف المشارك بنجاح',
                [],
                200
            );
        } catch (\Exception $e) {
            return $this->ApiResponse(
                false,
                trans('api.something_wrong'),
                [],
                500
            );
        }
    }

    /**
     * Decline co-host invitation
     */
    public function declineInvitation(Request $request)
    {
        try {
            $request->validate([
                'invitation_id' => 'required|integer|exists:co_hosts,id',
            ]);

            $user = Auth::user();
            $invitation = CoHost::find($request->invitation_id);

            // Check if user is the recipient of this invitation
            if ($invitation->co_host_id != $user->id) {
                return $this->ApiResponse(
                    false,
                    'غير مصرح لك برفض هذه الدعوة',
                    [],
                    403
                );
            }

            $invitation->decline();

            return $this->ApiResponse(
                true,
                'تم رفض دعوة المضيف المشارك',
                [],
                200
            );
        } catch (\Exception $e) {
            return $this->ApiResponse(
                false,
                trans('api.something_wrong'),
                [],
                500
            );
        }
    }

    /**
     * Remove co-host
     */
    public function removeCoHost(Request $request)
    {
        try {
            $request->validate([
                'co_host_id' => 'required|integer|exists:co_hosts,id',
            ]);

            $user = Auth::user();
            $coHostRelation = CoHost::find($request->co_host_id);

            // Check if user is the host of this relationship
            if ($coHostRelation->host_id != $user->id) {
                return $this->ApiResponse(
                    false,
                    'غير مصرح لك بحذف هذا المضيف المشارك',
                    [],
                    403
                );
            }

            $coHostRelation->remove();

            return $this->ApiResponse(
                true,
                'تم حذف المضيف المشارك بنجاح',
                [],
                200
            );
        } catch (\Exception $e) {
            return $this->ApiResponse(
                false,
                trans('api.something_wrong'),
                [],
                500
            );
        }
    }

    /**
     * Search for potential co-hosts
     */
    public function searchCoHosts(Request $request)
    {
        try {
            $request->validate([
                'query' => 'required|string|min:2',
            ]);

            $user = Auth::user();
            $query = $request->query;

            // Search for hosts (user_type_id = 2) excluding current user
            $users = User::where('id', '!=', $user->id)
                ->where('user_type_id', 2) // Only hosts
                ->where(function ($q) use ($query) {
                    $q->where('full_name', 'LIKE', "%{$query}%")
                      ->orWhere('email', 'LIKE', "%{$query}%")
                      ->orWhere('phone', 'LIKE', "%{$query}%");
                })
                ->limit(20)
                ->get();

            $usersData = $users->map(function ($searchUser) use ($user) {
                $invitationStatus = 'none';
                
                // Check if there's already an invitation
                $existingInvitation = CoHost::where('host_id', $user->id)
                    ->where('co_host_id', $searchUser->id)
                    ->first();

                if ($existingInvitation) {
                    $invitationStatus = $existingInvitation->status;
                }

                return [
                    'id' => $searchUser->id,
                    'full_name' => $searchUser->full_name,
                    'email' => $searchUser->email,
                    'phone' => $searchUser->phone,
                    'profile_photo_url' => $searchUser->profile_photo_url,
                    'invitation_status' => $invitationStatus,
                    'properties_count' => $searchUser->serviceCategoryItems()->count(),
                ];
            });

            return $this->ApiResponse(
                true,
                trans('api.data_retrieved_success'),
                $usersData->toArray(),
                200
            );
        } catch (\Exception $e) {
            return $this->ApiResponse(
                false,
                trans('api.something_wrong'),
                [],
                500
            );
        }
    }

    /**
     * Get available permissions
     */
    public function getPermissions()
    {
        try {
            $permissions = CoHost::getAllPermissions();

            return $this->ApiResponse(
                true,
                trans('api.data_retrieved_success'),
                $permissions,
                200
            );
        } catch (\Exception $e) {
            return $this->ApiResponse(
                false,
                trans('api.something_wrong'),
                [],
                500
            );
        }
    }
}
