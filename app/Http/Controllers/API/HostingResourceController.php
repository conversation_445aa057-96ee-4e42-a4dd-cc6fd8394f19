<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\HostingResource;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class HostingResourceController extends Controller
{
    /**
     * Get all hosting resources.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $category = $request->get('category');
            $type = $request->get('type');
            $featured = $request->boolean('featured', false);

            $query = HostingResource::active()->ordered();

            if ($category) {
                $query->byCategory($category);
            }

            if ($type) {
                $query->byType($type);
            }

            if ($featured) {
                $query->featured();
            }

            $resources = $query->get()->map(function ($resource) {
                return [
                    'id' => $resource->id,
                    'title' => $resource->title,
                    'description' => $resource->description,
                    'category' => $resource->category,
                    'category_display' => $resource->category_display,
                    'type' => $resource->type,
                    'type_display' => $resource->type_display,
                    'icon' => $resource->icon,
                    'image_url' => $resource->image_url,
                    'video_url' => $resource->video_url,
                    'tags' => $resource->tags,
                    'is_featured' => $resource->is_featured,
                    'created_at' => $resource->created_at->format('Y-m-d'),
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $resources,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب موارد الاستضافة',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get a specific hosting resource.
     */
    public function show($id): JsonResponse
    {
        try {
            $resource = HostingResource::active()->findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $resource->id,
                    'title' => $resource->title,
                    'description' => $resource->description,
                    'content' => $resource->content,
                    'category' => $resource->category,
                    'category_display' => $resource->category_display,
                    'type' => $resource->type,
                    'type_display' => $resource->type_display,
                    'icon' => $resource->icon,
                    'image_url' => $resource->image_url,
                    'video_url' => $resource->video_url,
                    'tags' => $resource->tags,
                    'is_featured' => $resource->is_featured,
                    'created_at' => $resource->created_at->format('Y-m-d'),
                    'updated_at' => $resource->updated_at->format('Y-m-d H:i'),
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب المورد',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get hosting resource categories.
     */
    public function getCategories(): JsonResponse
    {
        try {
            $categories = [
                ['value' => 'guide', 'label' => 'دليل', 'icon' => 'book'],
                ['value' => 'tip', 'label' => 'نصيحة', 'icon' => 'lightbulb'],
                ['value' => 'legal', 'label' => 'قانوني', 'icon' => 'gavel'],
                ['value' => 'marketing', 'label' => 'تسويق', 'icon' => 'campaign'],
                ['value' => 'finance', 'label' => 'مالي', 'icon' => 'account_balance'],
                ['value' => 'safety', 'label' => 'أمان', 'icon' => 'security'],
                ['value' => 'maintenance', 'label' => 'صيانة', 'icon' => 'build'],
            ];

            return response()->json([
                'success' => true,
                'data' => $categories,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب الفئات',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get featured resources for dashboard.
     */
    public function getFeatured(): JsonResponse
    {
        try {
            $resources = HostingResource::active()
                ->featured()
                ->ordered()
                ->limit(6)
                ->get()
                ->map(function ($resource) {
                    return [
                        'id' => $resource->id,
                        'title' => $resource->title,
                        'description' => $resource->description,
                        'category' => $resource->category,
                        'category_display' => $resource->category_display,
                        'type' => $resource->type,
                        'type_display' => $resource->type_display,
                        'icon' => $resource->icon,
                        'image_url' => $resource->image_url,
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => $resources,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب الموارد المميزة',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Search hosting resources.
     */
    public function search(Request $request): JsonResponse
    {
        try {
            $query = $request->get('q');
            
            if (!$query) {
                return response()->json([
                    'success' => false,
                    'message' => 'يرجى إدخال كلمة البحث',
                ], 422);
            }

            $resources = HostingResource::active()
                ->where(function ($q) use ($query) {
                    $q->where('title_ar', 'like', "%{$query}%")
                      ->orWhere('title_en', 'like', "%{$query}%")
                      ->orWhere('description_ar', 'like', "%{$query}%")
                      ->orWhere('description_en', 'like', "%{$query}%")
                      ->orWhere('content_ar', 'like', "%{$query}%")
                      ->orWhere('content_en', 'like', "%{$query}%");
                })
                ->ordered()
                ->get()
                ->map(function ($resource) {
                    return [
                        'id' => $resource->id,
                        'title' => $resource->title,
                        'description' => $resource->description,
                        'category' => $resource->category,
                        'category_display' => $resource->category_display,
                        'type' => $resource->type,
                        'type_display' => $resource->type_display,
                        'icon' => $resource->icon,
                        'image_url' => $resource->image_url,
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => $resources,
                'query' => $query,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء البحث',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
