<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\UserPreference;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class SettingsController extends Controller
{
    /**
     * Get all user settings.
     */
    public function getSettings(): JsonResponse
    {
        try {
            $user = Auth::user();
            $preferences = UserPreference::getAllUserPreferences($user->id);
            
            // Set default values if not exists
            $settings = [
                'general' => array_merge([
                    'language' => 'ar',
                    'theme' => 'light',
                    'currency' => 'SAR',
                    'timezone' => 'Asia/Riyadh',
                ], $preferences['general'] ?? []),
                
                'notifications' => array_merge([
                    'push_enabled' => true,
                    'email_enabled' => true,
                    'sms_enabled' => false,
                    'booking_notifications' => true,
                    'review_notifications' => true,
                    'payment_notifications' => true,
                    'marketing_notifications' => false,
                ], $preferences['notifications'] ?? []),
                
                'privacy' => array_merge([
                    'profile_visibility' => 'public',
                    'show_phone' => false,
                    'show_email' => false,
                    'allow_reviews' => true,
                    'data_sharing' => false,
                ], $preferences['privacy'] ?? []),
                
                'app' => array_merge([
                    'sound_enabled' => true,
                    'vibration_enabled' => true,
                    'auto_backup' => true,
                    'offline_mode' => false,
                    'analytics_enabled' => true,
                ], $preferences['app'] ?? []),
            ];

            return response()->json([
                'success' => true,
                'data' => $settings,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب الإعدادات',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update user settings.
     */
    public function updateSettings(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'category' => 'required|string|in:general,notifications,privacy,app',
                'settings' => 'required|array',
            ]);

            $user = Auth::user();
            $category = $request->category;
            $settings = $request->settings;

            // Define allowed settings for each category
            $allowedSettings = [
                'general' => [
                    'language' => 'string',
                    'theme' => 'string',
                    'currency' => 'string',
                    'timezone' => 'string',
                ],
                'notifications' => [
                    'push_enabled' => 'boolean',
                    'email_enabled' => 'boolean',
                    'sms_enabled' => 'boolean',
                    'booking_notifications' => 'boolean',
                    'review_notifications' => 'boolean',
                    'payment_notifications' => 'boolean',
                    'marketing_notifications' => 'boolean',
                ],
                'privacy' => [
                    'profile_visibility' => 'string',
                    'show_phone' => 'boolean',
                    'show_email' => 'boolean',
                    'allow_reviews' => 'boolean',
                    'data_sharing' => 'boolean',
                ],
                'app' => [
                    'sound_enabled' => 'boolean',
                    'vibration_enabled' => 'boolean',
                    'auto_backup' => 'boolean',
                    'offline_mode' => 'boolean',
                    'analytics_enabled' => 'boolean',
                ],
            ];

            // Update each setting
            foreach ($settings as $key => $value) {
                if (isset($allowedSettings[$category][$key])) {
                    $type = $allowedSettings[$category][$key];
                    UserPreference::setPreference($user->id, $category, $key, $value, $type);
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث الإعدادات بنجاح',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تحديث الإعدادات',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get specific category settings.
     */
    public function getCategorySettings(string $category): JsonResponse
    {
        try {
            if (!in_array($category, ['general', 'notifications', 'privacy', 'app'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'فئة الإعدادات غير صحيحة',
                ], 422);
            }

            $user = Auth::user();
            $preferences = UserPreference::getCategoryPreferences($user->id, $category);

            return response()->json([
                'success' => true,
                'data' => $preferences,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب إعدادات الفئة',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Reset settings to default.
     */
    public function resetSettings(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'category' => 'nullable|string|in:general,notifications,privacy,app',
            ]);

            $user = Auth::user();
            $category = $request->category;

            if ($category) {
                // Reset specific category
                UserPreference::where('user_id', $user->id)
                    ->where('category', $category)
                    ->delete();
            } else {
                // Reset all settings
                UserPreference::where('user_id', $user->id)->delete();
            }

            return response()->json([
                'success' => true,
                'message' => $category 
                    ? "تم إعادة تعيين إعدادات {$category} إلى القيم الافتراضية"
                    : 'تم إعادة تعيين جميع الإعدادات إلى القيم الافتراضية',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إعادة تعيين الإعدادات',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Export user settings.
     */
    public function exportSettings(): JsonResponse
    {
        try {
            $user = Auth::user();
            $preferences = UserPreference::getAllUserPreferences($user->id);

            return response()->json([
                'success' => true,
                'data' => [
                    'user_id' => $user->id,
                    'exported_at' => now()->toISOString(),
                    'settings' => $preferences,
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تصدير الإعدادات',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Import user settings.
     */
    public function importSettings(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'settings' => 'required|array',
            ]);

            $user = Auth::user();
            $settings = $request->settings;

            // Define type mappings
            $typeMapping = [
                'general' => [
                    'language' => 'string',
                    'theme' => 'string',
                    'currency' => 'string',
                    'timezone' => 'string',
                ],
                'notifications' => [
                    'push_enabled' => 'boolean',
                    'email_enabled' => 'boolean',
                    'sms_enabled' => 'boolean',
                    'booking_notifications' => 'boolean',
                    'review_notifications' => 'boolean',
                    'payment_notifications' => 'boolean',
                    'marketing_notifications' => 'boolean',
                ],
                'privacy' => [
                    'profile_visibility' => 'string',
                    'show_phone' => 'boolean',
                    'show_email' => 'boolean',
                    'allow_reviews' => 'boolean',
                    'data_sharing' => 'boolean',
                ],
                'app' => [
                    'sound_enabled' => 'boolean',
                    'vibration_enabled' => 'boolean',
                    'auto_backup' => 'boolean',
                    'offline_mode' => 'boolean',
                    'analytics_enabled' => 'boolean',
                ],
            ];

            // Import settings
            foreach ($settings as $category => $categorySettings) {
                if (isset($typeMapping[$category])) {
                    foreach ($categorySettings as $key => $value) {
                        if (isset($typeMapping[$category][$key])) {
                            $type = $typeMapping[$category][$key];
                            UserPreference::setPreference($user->id, $category, $key, $value, $type);
                        }
                    }
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'تم استيراد الإعدادات بنجاح',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء استيراد الإعدادات',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
