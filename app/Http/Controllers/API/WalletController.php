<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\WalletTransaction;
use App\Models\WithdrawalRequest;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class WalletController extends Controller
{
    /**
     * Get wallet balance and summary.
     */
    public function getBalance(): JsonResponse
    {
        try {
            $user = Auth::user();
            
            // Calculate balance from completed transactions
            $earnings = WalletTransaction::where('user_id', $user->id)
                ->completed()
                ->whereIn('type', ['earning', 'bonus', 'refund'])
                ->sum('amount');
                
            $withdrawals = WalletTransaction::where('user_id', $user->id)
                ->completed()
                ->whereIn('type', ['withdrawal', 'fee'])
                ->sum('amount');
                
            $balance = $earnings - $withdrawals;
            
            // Get pending withdrawals
            $pendingWithdrawals = WithdrawalRequest::where('user_id', $user->id)
                ->whereIn('status', ['pending', 'approved'])
                ->sum('amount');
                
            // Get this month's earnings
            $thisMonthEarnings = WalletTransaction::where('user_id', $user->id)
                ->completed()
                ->where('type', 'earning')
                ->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->sum('amount');
                
            // Get total lifetime earnings
            $totalEarnings = WalletTransaction::where('user_id', $user->id)
                ->completed()
                ->where('type', 'earning')
                ->sum('amount');

            return response()->json([
                'success' => true,
                'data' => [
                    'balance' => round($balance, 2),
                    'currency' => 'SAR',
                    'pending_withdrawals' => round($pendingWithdrawals, 2),
                    'this_month_earnings' => round($thisMonthEarnings, 2),
                    'total_earnings' => round($totalEarnings, 2),
                    'available_for_withdrawal' => round($balance - $pendingWithdrawals, 2),
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب رصيد المحفظة',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get wallet transactions.
     */
    public function getTransactions(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $type = $request->get('type');
            $status = $request->get('status');
            $limit = $request->get('limit', 20);
            
            $query = WalletTransaction::where('user_id', $user->id)
                ->orderBy('created_at', 'desc');
                
            if ($type) {
                $query->byType($type);
            }
            
            if ($status) {
                $query->byStatus($status);
            }
            
            $transactions = $query->limit($limit)->get()->map(function ($transaction) {
                return [
                    'id' => $transaction->id,
                    'type' => $transaction->type,
                    'type_display' => $transaction->type_display,
                    'amount' => $transaction->amount,
                    'currency' => $transaction->currency,
                    'status' => $transaction->status,
                    'status_display' => $transaction->status_display,
                    'description' => $transaction->description,
                    'created_at' => $transaction->created_at->format('Y-m-d H:i'),
                    'processed_at' => $transaction->processed_at?->format('Y-m-d H:i'),
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $transactions,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب المعاملات',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Create withdrawal request.
     */
    public function requestWithdrawal(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'amount' => 'required|numeric|min:50|max:10000',
                'method' => 'required|string|in:bank_transfer,paypal,stc_pay',
                'payment_details' => 'required|array',
                'notes' => 'nullable|string|max:500',
            ]);

            $user = Auth::user();
            $amount = $request->amount;
            
            // Check available balance
            $balance = $this->getUserBalance($user->id);
            $pendingWithdrawals = WithdrawalRequest::where('user_id', $user->id)
                ->whereIn('status', ['pending', 'approved'])
                ->sum('amount');
                
            $availableBalance = $balance - $pendingWithdrawals;
            
            if ($amount > $availableBalance) {
                return response()->json([
                    'success' => false,
                    'message' => 'الرصيد المتاح غير كافي',
                ], 422);
            }

            DB::beginTransaction();
            
            // Create withdrawal request
            $withdrawalRequest = WithdrawalRequest::create([
                'user_id' => $user->id,
                'amount' => $amount,
                'method' => $request->method,
                'payment_details' => $request->payment_details,
                'notes' => $request->notes,
                'status' => 'pending',
            ]);
            
            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم إرسال طلب السحب بنجاح',
                'data' => [
                    'id' => $withdrawalRequest->id,
                    'amount' => $withdrawalRequest->amount,
                    'status' => $withdrawalRequest->status,
                    'status_display' => $withdrawalRequest->status_display,
                ],
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إرسال طلب السحب',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get withdrawal requests.
     */
    public function getWithdrawalRequests(): JsonResponse
    {
        try {
            $user = Auth::user();
            
            $requests = WithdrawalRequest::where('user_id', $user->id)
                ->orderBy('created_at', 'desc')
                ->get()
                ->map(function ($request) {
                    return [
                        'id' => $request->id,
                        'amount' => $request->amount,
                        'currency' => $request->currency,
                        'method' => $request->method,
                        'method_display' => $request->method_display,
                        'status' => $request->status,
                        'status_display' => $request->status_display,
                        'status_color' => $request->status_color,
                        'notes' => $request->notes,
                        'admin_notes' => $request->admin_notes,
                        'created_at' => $request->created_at->format('Y-m-d H:i'),
                        'processed_at' => $request->processed_at?->format('Y-m-d H:i'),
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => $requests,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب طلبات السحب',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get earnings analytics.
     */
    public function getEarningsAnalytics(): JsonResponse
    {
        try {
            $user = Auth::user();
            
            // Monthly earnings for the last 6 months
            $monthlyEarnings = [];
            for ($i = 5; $i >= 0; $i--) {
                $date = now()->subMonths($i);
                $earnings = WalletTransaction::where('user_id', $user->id)
                    ->completed()
                    ->where('type', 'earning')
                    ->whereMonth('created_at', $date->month)
                    ->whereYear('created_at', $date->year)
                    ->sum('amount');
                    
                $monthlyEarnings[] = [
                    'month' => $date->format('Y-m'),
                    'month_name' => $date->format('M Y'),
                    'earnings' => round($earnings, 2),
                ];
            }
            
            // Top earning properties (if available)
            $topProperties = DB::table('wallet_transactions')
                ->join('reservations', 'wallet_transactions.reference_id', '=', 'reservations.id')
                ->join('items', 'reservations.item_id', '=', 'items.id')
                ->where('wallet_transactions.user_id', $user->id)
                ->where('wallet_transactions.type', 'earning')
                ->where('wallet_transactions.status', 'completed')
                ->where('wallet_transactions.reference_type', 'reservation')
                ->select('items.id', 'items.title', DB::raw('SUM(wallet_transactions.amount) as total_earnings'))
                ->groupBy('items.id', 'items.title')
                ->orderBy('total_earnings', 'desc')
                ->limit(5)
                ->get()
                ->map(function ($item) {
                    return [
                        'property_id' => $item->id,
                        'property_name' => $item->title,
                        'total_earnings' => round($item->total_earnings, 2),
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => [
                    'monthly_earnings' => $monthlyEarnings,
                    'top_properties' => $topProperties,
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب تحليلات الأرباح',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get user balance.
     */
    private function getUserBalance(int $userId): float
    {
        $earnings = WalletTransaction::where('user_id', $userId)
            ->completed()
            ->whereIn('type', ['earning', 'bonus', 'refund'])
            ->sum('amount');
            
        $withdrawals = WalletTransaction::where('user_id', $userId)
            ->completed()
            ->whereIn('type', ['withdrawal', 'fee'])
            ->sum('amount');
            
        return $earnings - $withdrawals;
    }
}
