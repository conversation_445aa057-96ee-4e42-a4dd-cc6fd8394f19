<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\LegalDocument;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class LegalController extends Controller
{
    /**
     * Get all legal documents.
     */
    public function index(): JsonResponse
    {
        try {
            $documents = LegalDocument::active()
                ->orderBy('type')
                ->get()
                ->groupBy('type')
                ->map(function ($docs) {
                    return $docs->sortByDesc('effective_date')->first();
                })
                ->values()
                ->map(function ($doc) {
                    return [
                        'id' => $doc->id,
                        'type' => $doc->type,
                        'title' => $doc->title,
                        'version' => $doc->version,
                        'effective_date' => $doc->effective_date->format('Y-m-d'),
                        'updated_at' => $doc->updated_at->format('Y-m-d H:i'),
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => $documents,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب الوثائق القانونية',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get a specific legal document by type.
     */
    public function getByType(Request $request, $type): JsonResponse
    {
        try {
            $document = LegalDocument::latestVersion($type);

            if (!$document) {
                return response()->json([
                    'success' => false,
                    'message' => 'الوثيقة غير موجودة',
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $document->id,
                    'type' => $document->type,
                    'title' => $document->title,
                    'content' => $document->content,
                    'version' => $document->version,
                    'effective_date' => $document->effective_date->format('Y-m-d'),
                    'updated_at' => $document->updated_at->format('Y-m-d H:i'),
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب الوثيقة',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get terms of service.
     */
    public function getTerms(): JsonResponse
    {
        return $this->getByType(request(), 'terms');
    }

    /**
     * Get privacy policy.
     */
    public function getPrivacyPolicy(): JsonResponse
    {
        return $this->getByType(request(), 'privacy');
    }

    /**
     * Get cookie policy.
     */
    public function getCookiePolicy(): JsonResponse
    {
        return $this->getByType(request(), 'cookies');
    }

    /**
     * Get user agreement.
     */
    public function getUserAgreement(): JsonResponse
    {
        return $this->getByType(request(), 'user_agreement');
    }

    /**
     * Get host agreement.
     */
    public function getHostAgreement(): JsonResponse
    {
        return $this->getByType(request(), 'host_agreement');
    }
}
