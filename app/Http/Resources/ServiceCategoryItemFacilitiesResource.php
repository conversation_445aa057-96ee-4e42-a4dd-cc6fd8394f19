<?php
namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class ServiceCategoryItemFacilitiesResource extends JsonResource
{
    /**
     * User resource construct.
     *
     * @param  mixed  $collection
     * @param  \Laravel\Passport\Token|null  $token
     * @param  String|null  $accessToken
     */
    public function __construct($collection)
    {
        parent::__construct($collection);
    }

    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $lang = $request->hasHeader('X-localization') && in_array($request->Header('X-localization'), ['ar', 'en']) ? $request->Header('X-localization') : 'ar';

        $resource = [
            'id'         => (int) $this->id,
            'category'   => $this->facility->category ? $this->facility->category->{'title_' . $lang} : null,
            'title'      => ! empty($this->facility->{'title_' . $lang}) ? $this->facility->{'title_' . $lang} : null,
            'icon'       => ! empty($this->facility->icon) ? asset(path : 'storage/' . $this->facility->icon): null,
            'count'      => $this->count,
            'is_featured'      => $this->facility->is_featured ?? false,
            'order'      => $this->facility->order ?? 0,
            'created_at' => $this->created_at,
        ];
        return $resource;
    }

}
