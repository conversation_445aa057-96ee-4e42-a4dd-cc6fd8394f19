import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/core/services/service_locator.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/core/managers/locale_cubit/locale_cubit.dart';
import 'package:gather_point/core/utils/app_assets.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:gather_point/feature/reels/presentation/views/widgets/video_player_widget.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:hive/hive.dart';

class ReelsPage extends StatefulWidget {
  final List<Map<String, dynamic>> searchResults;
  final String searchQuery;
  final int serviceCategoryId;
  final Map<String, dynamic>? selectedItem; // The item clicked from home
  final bool showBackButton; // Whether to show back button

  const ReelsPage({
    super.key,
    required this.searchResults,
    required this.searchQuery,
    required this.serviceCategoryId,
    this.selectedItem,
    this.showBackButton = false,
  });

  @override
  State<ReelsPage> createState() => _ReelsPageState();
}

class _ReelsPageState extends State<ReelsPage> {
  List<Map<String, dynamic>> videoData = [];
  List<Map<String, dynamic>> filteredVideoData = [];
  bool isLoading = true;
  bool hasError = false;
  late final DioConsumer dioConsumer;
  late final PageController _pageController;
  int _selectedItemIndex = 0;

  // Search and Filter
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedCategory = '';
  String _sortBy = 'newest';
  bool _showSearchBar = false;

  @override
  void initState() {
    super.initState();
    dioConsumer = DioConsumer(
      dio: getIt<Dio>(),
      profileBox: getIt<Box<UserEntity>>(),
    );

    // Initialize PageController with initial page
    _pageController = PageController(initialPage: 0);

    if (widget.searchResults.isNotEmpty) {
      setState(() {
        videoData = widget.searchResults;
        filteredVideoData = List.from(videoData);
        isLoading = false;
        hasError = false;
      });
      _findSelectedItemIndex();
    } else {
      fetchReels();
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  /// Find the index of the selected item in the filtered data
  void _findSelectedItemIndex() {
    if (widget.selectedItem != null) {
      final selectedId = widget.selectedItem!['id'];
      final index = filteredVideoData.indexWhere((item) => item['id'] == selectedId);
      if (index != -1) {
        _selectedItemIndex = index;
        // Update PageController to start at the selected item
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (_pageController.hasClients) {
            _pageController.jumpToPage(_selectedItemIndex);
          }
        });
      }
    }
  }

  Future<void> fetchReels() async {
    try {
      if (!mounted) return;
      setState(() {
        isLoading = true;
        hasError = false;
      });

      final response = await dioConsumer.get(
        '/api/items/list',
        queryParameters: {
          'service_category_id': widget.serviceCategoryId,
          'reels': 1
        },
      );

      if (!mounted) return;
      if (response['data'] != null) {
        setState(() {
          videoData = List<Map<String, dynamic>>.from(response['data']);
          filteredVideoData = List.from(videoData);
          isLoading = false;
          hasError = false;
        });
        _applyFilters();
        _findSelectedItemIndex(); // Find selected item after fetching
      } else {
        setState(() {
          isLoading = false;
          hasError = true;
        });
      }
    } catch (e) {
      debugPrint("Error fetching reels: $e");
      if (!mounted) return;
      setState(() {
        isLoading = false;
        hasError = true;
      });
    }
  }

  void _applyFilters() {
    if (!mounted) return;
    setState(() {
      filteredVideoData = videoData.where((video) {
        // Search filter - search in multiple fields
        bool matchesSearch = _searchQuery.isEmpty ||
            video['title']
                    ?.toLowerCase()
                    .contains(_searchQuery.toLowerCase()) ==
                true ||
            video['description']
                    ?.toLowerCase()
                    .contains(_searchQuery.toLowerCase()) ==
                true ||
            video['location']
                    ?.toLowerCase()
                    .contains(_searchQuery.toLowerCase()) ==
                true;

        // Category filter
        bool matchesCategory = _selectedCategory.isEmpty ||
            video['category'] == _selectedCategory ||
            video['service_category']?['title'] == _selectedCategory;

        return matchesSearch && matchesCategory;
      }).toList();

      // Sort results
      _sortResults();
    });
  }

  void _sortResults() {
    try {
      switch (_sortBy) {
        case 'newest':
          filteredVideoData.sort((a, b) {
            try {
              final dateA =
                  DateTime.tryParse(a['created_at'] ?? '') ?? DateTime.now();
              final dateB =
                  DateTime.tryParse(b['created_at'] ?? '') ?? DateTime.now();
              return dateB.compareTo(dateA);
            } catch (e) {
              return 0;
            }
          });
          break;
        case 'oldest':
          filteredVideoData.sort((a, b) {
            try {
              final dateA =
                  DateTime.tryParse(a['created_at'] ?? '') ?? DateTime.now();
              final dateB =
                  DateTime.tryParse(b['created_at'] ?? '') ?? DateTime.now();
              return dateA.compareTo(dateB);
            } catch (e) {
              return 0;
            }
          });
          break;
        case 'mostLiked':
          filteredVideoData.sort((a, b) {
            final likesA =
                (a['likes_count'] ?? a['favorite_count'] ?? 0) as int;
            final likesB =
                (b['likes_count'] ?? b['favorite_count'] ?? 0) as int;
            return likesB.compareTo(likesA);
          });
          break;
        case 'mostCommented':
          filteredVideoData.sort((a, b) {
            final commentsA = (a['comments_count'] ?? 0) as int;
            final commentsB = (b['comments_count'] ?? 0) as int;
            return commentsB.compareTo(commentsA);
          });
          break;
      }
    } catch (e) {
      debugPrint('Error sorting results: $e');
      // If sorting fails, keep original order
    }
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
    });
    _applyFilters();
  }

  void _showFilterDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildFilterDialog(),
    );
  }

  void _toggleSearchBar() {
    setState(() {
      _showSearchBar = !_showSearchBar;
      if (!_showSearchBar) {
        _searchController.clear();
        _searchQuery = '';
        _applyFilters();
      }
    });
  }

  /// Enhanced refresh functionality with haptic feedback
  Future<void> _onRefresh() async {
    try {
      // Add haptic feedback
      HapticFeedback.mediumImpact();

      // Refresh reels data
      await fetchReels();

      // Add success haptic feedback
      HapticFeedback.lightImpact();
    } catch (e) {
      debugPrint('Refresh error: $e');
      // Add error haptic feedback
      HapticFeedback.heavyImpact();
    }
  }

  Widget _buildLoading() {
    final theme = Theme.of(context);
    final s = S.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      color: isDark ? Colors.black : theme.scaffoldBackgroundColor,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 60,
              height: 60,
              child: CircularProgressIndicator(
                strokeWidth: 3,
                valueColor: AlwaysStoppedAnimation<Color>(
                  isDark ? Colors.white : theme.colorScheme.primary,
                ),
              ),
            ),
            const SizedBox(height: 24),
            Text(
              s.loadingReels,
              style: AppTextStyles.font16Regular.copyWith(
                color: isDark ? Colors.white : theme.textTheme.bodyLarge?.color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildError() {
    final theme = Theme.of(context);
    final s = S.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      color: isDark ? Colors.black : theme.scaffoldBackgroundColor,
      child: SafeArea(
        child: Column(
          children: [
            // Enhanced App Bar
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Column(
                children: [
                  Row(
                    children: [
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: Icon(
                          Icons.arrow_back_ios_rounded,
                          color: isDark ? Colors.white : theme.iconTheme.color,
                        ),
                      ),
                      Expanded(
                        child: Text(
                          s.reels,
                          style: AppTextStyles.font18Bold.copyWith(
                            color: isDark
                                ? Colors.white
                                : theme.textTheme.bodyLarge?.color,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      IconButton(
                        onPressed: _toggleSearchBar,
                        icon: Icon(
                          _showSearchBar ? Icons.close : Icons.search,
                          color: isDark ? Colors.white : theme.iconTheme.color,
                        ),
                        tooltip: s.searchReels,
                      ),
                      IconButton(
                        onPressed: _showFilterDialog,
                        icon: Icon(
                          Icons.filter_list,
                          color: isDark ? Colors.white : theme.iconTheme.color,
                        ),
                        tooltip: s.filterReels,
                      ),
                    ],
                  ),

                  // Search Bar
                  if (_showSearchBar)
                    Container(
                      margin: const EdgeInsets.only(top: 12),
                      child: TextField(
                        controller: _searchController,
                        onChanged: _onSearchChanged,
                        style: AppTextStyles.font14Regular.copyWith(
                          color: isDark
                              ? Colors.white
                              : theme.textTheme.bodyLarge?.color,
                        ),
                        decoration: InputDecoration(
                          hintText: s.searchHint,
                          hintStyle: AppTextStyles.font14Regular.copyWith(
                            color: isDark
                                ? Colors.grey[400]
                                : theme.textTheme.bodyMedium?.color,
                          ),
                          prefixIcon: Icon(
                            Icons.search,
                            color: isDark
                                ? Colors.grey[400]
                                : theme.iconTheme.color,
                          ),
                          suffixIcon: _searchQuery.isNotEmpty
                              ? IconButton(
                                  onPressed: () {
                                    _searchController.clear();
                                    _onSearchChanged('');
                                  },
                                  icon: Icon(
                                    Icons.clear,
                                    color: isDark
                                        ? Colors.grey[400]
                                        : theme.iconTheme.color,
                                  ),
                                )
                              : null,
                          filled: true,
                          fillColor:
                              isDark ? Colors.grey[800] : Colors.grey[100],
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide.none,
                          ),
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 12,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
            // Error Content
            Expanded(
              child: Center(
                child: Padding(
                  padding: const EdgeInsets.all(32),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(24),
                        decoration: BoxDecoration(
                          color: Colors.red.withValues(alpha: 0.1),
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.error_outline_rounded,
                          color: Colors.red,
                          size: 64,
                        ),
                      ),
                      const SizedBox(height: 24),
                      Text(
                        s.failedToLoadReels,
                        style: AppTextStyles.font20Bold.copyWith(
                          color: isDark
                              ? Colors.white
                              : theme.textTheme.bodyLarge?.color,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 12),
                      Text(
                        s.checkConnection,
                        style: AppTextStyles.font14Regular.copyWith(
                          color: isDark
                              ? Colors.grey[400]
                              : theme.textTheme.bodyMedium?.color,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 32),
                      ElevatedButton.icon(
                        onPressed: fetchReels,
                        icon: const Icon(Icons.refresh_rounded),
                        label: Text(s.retry),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: theme.colorScheme.primary,
                          foregroundColor: theme.colorScheme.onPrimary,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 32,
                            vertical: 16,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(25),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNoResults() {
    final theme = Theme.of(context);
    final s = S.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      color: isDark ? Colors.black : theme.scaffoldBackgroundColor,
      child: SafeArea(
        child: Column(
          children: [
            // Enhanced App Bar
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Row(
                children: [
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: Icon(
                      Icons.arrow_back_ios_rounded,
                      color: isDark ? Colors.white : theme.iconTheme.color,
                    ),
                  ),
                  Expanded(
                    child: Text(
                      s.searchResults,
                      style: AppTextStyles.font18Bold.copyWith(
                        color: isDark
                            ? Colors.white
                            : theme.textTheme.bodyLarge?.color,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  const SizedBox(width: 48), // Balance the back button
                ],
              ),
            ),
            // No Results Content
            Expanded(
              child: Center(
                child: Padding(
                  padding: const EdgeInsets.all(32),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(24),
                        decoration: BoxDecoration(
                          color: Colors.grey.withValues(alpha: 0.1),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.search_off_rounded,
                          color: isDark ? Colors.grey : Colors.grey[600],
                          size: 64,
                        ),
                      ),
                      const SizedBox(height: 24),
                      Text(
                        s.noResults,
                        style: AppTextStyles.font20Bold.copyWith(
                          color: isDark
                              ? Colors.white
                              : theme.textTheme.bodyLarge?.color,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 12),
                      Text(
                        '${s.noSearchResults} "${widget.searchQuery}"',
                        style: AppTextStyles.font14Regular.copyWith(
                          color: isDark
                              ? Colors.grey[400]
                              : theme.textTheme.bodyMedium?.color,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 32),
                      ElevatedButton.icon(
                        onPressed: () => Navigator.pop(context),
                        icon: const Icon(Icons.arrow_back_rounded),
                        label: Text(s.backToSearch),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: theme.colorScheme.primary,
                          foregroundColor: theme.colorScheme.onPrimary,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 32,
                            vertical: 16,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(25),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReels() {
    final s = S.of(context);
    final hasActiveFilters = _searchQuery.isNotEmpty || _sortBy != 'newest';
    final isRTL = context.read<LocaleCubit>().isArabic();

    return SafeArea(
      child: Stack(
        children: [
          // Main Video Content with RefreshIndicator
          RefreshIndicator(
            onRefresh: _onRefresh,
            color: AppColors.yellow,
            backgroundColor: Colors.black.withValues(alpha: 0.8),
            strokeWidth: 3,
            displacement: 60,
            child: PageView.builder(
              controller: _pageController,
              scrollDirection: Axis.vertical,
              itemCount: filteredVideoData.length,
              itemBuilder: (context, index) {
              final item = filteredVideoData[index];
              return VideoPlayerWidget(
                facilities: item['facilities'] ?? [],
                videoUrl: item['video'],
                title: item['title'],
                location: item['title'],
                id: item['id'],
                price: (item['price'] as num).toDouble(),
                serviceCategoryId: widget.serviceCategoryId,
                favorite: item['favorite'],
                dioConsumer: dioConsumer,
              );
            },
            ),
          ),

          // Search and Filter Overlay
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.black.withValues(alpha: 0.7),
                    Colors.black.withValues(alpha: 0.3),
                    Colors.transparent,
                  ],
                  stops: const [0.0, 0.5, 1.0],
                ),
              ),
              child: Column(
                children: [
                  // Enhanced Action Bar with Logo and Controls
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                    child: Directionality(
                      textDirection: context.read<LocaleCubit>().isArabic()
                          ? TextDirection.rtl
                          : TextDirection.ltr,
                      child: Row(
                        children: [
                          // Back button (show when navigating from home)
                          if (widget.showBackButton)
                            Container(
                              margin: EdgeInsets.only(
                                  right: isRTL ? 2 : 6,
                                  left: isRTL ? 6 : 2),
                              decoration: BoxDecoration(
                                color: Colors.black.withValues(alpha: 0.4),
                                borderRadius: BorderRadius.circular(25),
                                border: Border.all(
                                  color: Colors.white.withValues(alpha: 0.2),
                                  width: 1,
                                ),
                              ),
                              child: IconButton(
                                onPressed: () => Navigator.pop(context),
                                icon: const Icon(
                                  Icons.arrow_back_ios_rounded,
                                  color: Colors.white,
                                  size: 20,
                                ),
                                padding: const EdgeInsets.all(8),
                              ),
                            ),

                          // Logo and App Name Section
                          Expanded(
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                // Logo Container
                                Container(
                                  padding: const EdgeInsets.all(6),
                                  decoration: BoxDecoration(
                                    color: Colors.black.withValues(alpha: 0.4),
                                    borderRadius: BorderRadius.circular(16),
                                    border: Border.all(
                                      color: AppColors.yellow
                                          .withValues(alpha: 0.3),
                                      width: 1,
                                    ),
                                  ),
                                  child: Image.asset(
                                    AppAssets.imagesLogoCircle,
                                    width: 36,
                                    height: 36,
                                  ),
                                ),

                                const SizedBox(width: 12),

                                // App Name and Section
                                Flexible(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Text(
                                        s.appName.toUpperCase(),
                                        style:
                                            AppTextStyles.font16Bold.copyWith(
                                          color: Colors.white,
                                          letterSpacing: 1.5,
                                          height: 1.1,
                                          shadows: [
                                            Shadow(
                                              color: Colors.black
                                                  .withValues(alpha: 0.5),
                                              offset: const Offset(0, 1),
                                              blurRadius: 2,
                                            ),
                                          ],
                                        ),
                                      ),
                                      Text(
                                        s.reels,
                                        style: AppTextStyles.font12Regular
                                            .copyWith(
                                          color: AppColors.yellow,
                                          letterSpacing: 0.5,
                                          height: 1.0,
                                          shadows: [
                                            Shadow(
                                              color: Colors.black
                                                  .withValues(alpha: 0.5),
                                              offset: const Offset(0, 1),
                                              blurRadius: 2,
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),

                          // Action Buttons Section
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              // Search Button
                              Container(
                                decoration: BoxDecoration(
                                  color: Colors.black.withValues(alpha: 0.4),
                                  borderRadius: BorderRadius.circular(25),
                                  border: Border.all(
                                    color: Colors.white.withValues(alpha: 0.2),
                                    width: 1,
                                  ),
                                ),
                                child: IconButton(
                                  onPressed: _toggleSearchBar,
                                  icon: Icon(
                                    _showSearchBar ? Icons.close : Icons.search,
                                    color: Colors.white,
                                    size: 22,
                                  ),
                                  tooltip: s.searchReels,
                                  padding: const EdgeInsets.all(8),
                                ),
                              ),

                              const SizedBox(width: 8),

                              // Filter Button with Badge
                              Container(
                                decoration: BoxDecoration(
                                  color: Colors.black.withValues(alpha: 0.4),
                                  borderRadius: BorderRadius.circular(25),
                                  border: Border.all(
                                    color: hasActiveFilters
                                        ? AppColors.yellow
                                            .withValues(alpha: 0.6)
                                        : Colors.white.withValues(alpha: 0.2),
                                    width: 1,
                                  ),
                                ),
                                child: Stack(
                                  children: [
                                    IconButton(
                                      onPressed: _showFilterDialog,
                                      icon: Icon(
                                        Icons.filter_list,
                                        color: hasActiveFilters
                                            ? AppColors.yellow
                                            : Colors.white,
                                        size: 22,
                                      ),
                                      tooltip: s.filterReels,
                                      padding: const EdgeInsets.all(8),
                                    ),
                                    if (hasActiveFilters)
                                      Positioned(
                                        right: 4,
                                        top: 4,
                                        child: Container(
                                          width: 10,
                                          height: 10,
                                          decoration: BoxDecoration(
                                            color: AppColors.yellow,
                                            shape: BoxShape.circle,
                                            border: Border.all(
                                              color: Colors.black,
                                              width: 1,
                                            ),
                                          ),
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                  // Search Bar
                  if (_showSearchBar)
                    Container(
                      margin: const EdgeInsets.symmetric(horizontal: 16),
                      padding: const EdgeInsets.only(bottom: 12),
                      child: TextField(
                        controller: _searchController,
                        onChanged: _onSearchChanged,
                        style: AppTextStyles.font14Regular.copyWith(
                          color: Colors.white,
                        ),
                        decoration: InputDecoration(
                          hintText: s.searchHint,
                          hintStyle: AppTextStyles.font14Regular.copyWith(
                            color: Colors.white70,
                          ),
                          prefixIcon: const Icon(
                            Icons.search,
                            color: Colors.white70,
                          ),
                          suffixIcon: _searchQuery.isNotEmpty
                              ? IconButton(
                                  onPressed: () {
                                    _searchController.clear();
                                    _onSearchChanged('');
                                  },
                                  icon: const Icon(
                                    Icons.clear,
                                    color: Colors.white70,
                                  ),
                                )
                              : null,
                          filled: true,
                          fillColor: Colors.black.withValues(alpha: 0.3),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(25),
                            borderSide: BorderSide.none,
                          ),
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 20,
                            vertical: 12,
                          ),
                        ),
                      ),
                    ),

                  // Results Counter
                  if (hasActiveFilters)
                    Container(
                      margin: const EdgeInsets.symmetric(horizontal: 16),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.5),
                        borderRadius: BorderRadius.circular(15),
                      ),
                      child: Text(
                        '${filteredVideoData.length} ${filteredVideoData.length == 1 ? 'result' : 'results'}',
                        style: AppTextStyles.font12Regular.copyWith(
                          color: Colors.white70,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    // Set status bar based on theme
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarBrightness: isDark ? Brightness.dark : Brightness.light,
        statusBarIconBrightness: isDark ? Brightness.light : Brightness.dark,
        systemNavigationBarColor:
            isDark ? Colors.black : theme.scaffoldBackgroundColor,
        systemNavigationBarIconBrightness:
            isDark ? Brightness.light : Brightness.dark,
      ),
    );

    return Scaffold(
      backgroundColor: isDark ? Colors.black : theme.scaffoldBackgroundColor,
      body: AnimatedSwitcher(
        duration: const Duration(milliseconds: 300),
        child: isLoading
            ? _buildLoading()
            : hasError
                ? _buildError()
                : filteredVideoData.isEmpty
                    ? _buildNoResults()
                    : _buildReels(),
      ),
    );
  }

  Widget _buildFilterDialog() {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final s = S.of(context);
    final isRTL = context.read<LocaleCubit>().isArabic();

    return Container(
      height: MediaQuery.of(context).size.height * 0.6,
      decoration: BoxDecoration(
        color: isDark ? AppColors.darkGrey : AppColors.white,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Directionality(
        textDirection: isRTL ? TextDirection.rtl : TextDirection.ltr,
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: isDark ? AppColors.darkGrey2 : AppColors.lightGrey2,
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.filter_list,
                    color: AppColors.yellow,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      s.filterReels,
                      style: AppTextStyles.font18Bold.copyWith(
                        color: ThemeHelper.getPrimaryTextColor(context),
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: Icon(
                      Icons.close_rounded,
                      color: ThemeHelper.getSecondaryTextColor(context),
                    ),
                  ),
                ],
              ),
            ),

            // Filter Options
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Sort By Section
                    Text(
                      s.sortBy,
                      style: AppTextStyles.font16Bold.copyWith(
                        color: ThemeHelper.getPrimaryTextColor(context),
                      ),
                    ),
                    const SizedBox(height: 12),

                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: [
                        _buildFilterChip(s.newest, 'newest', isRTL),
                        _buildFilterChip(s.oldest, 'oldest', isRTL),
                        _buildFilterChip(s.mostLiked, 'mostLiked', isRTL),
                        _buildFilterChip(
                            s.mostCommented, 'mostCommented', isRTL),
                      ],
                    ),

                    const SizedBox(height: 24),

                    // Action Buttons
                    Row(
                      children: [
                        Expanded(
                          child: OutlinedButton(
                            onPressed: () {
                              setState(() {
                                _selectedCategory = '';
                                _sortBy = 'newest';
                              });
                              _applyFilters();
                              Navigator.pop(context);
                            },
                            style: OutlinedButton.styleFrom(
                              side: const BorderSide(color: AppColors.yellow),
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: Text(
                              s.clearFilter,
                              style: AppTextStyles.font14SemiBold.copyWith(
                                color: AppColors.yellow,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () {
                              _applyFilters();
                              Navigator.pop(context);
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.yellow,
                              foregroundColor: AppColors.black,
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: Text(
                              s.applyFilter,
                              style: AppTextStyles.font14SemiBold.copyWith(
                                color: AppColors.black,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterChip(String label, String value, bool isRTL) {
    final isSelected = _sortBy == value;

    return FilterChip(
      label: Text(
        label,
        style: AppTextStyles.font14Medium.copyWith(
          color: isSelected
              ? AppColors.black
              : ThemeHelper.getPrimaryTextColor(context),
        ),
      ),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _sortBy = value;
        });
      },
      selectedColor: AppColors.yellow,
      backgroundColor: Theme.of(context).brightness == Brightness.dark
          ? AppColors.darkGrey2
          : AppColors.lightGrey6,
      checkmarkColor: AppColors.black,
      side: BorderSide(
        color: isSelected ? AppColors.yellow : Colors.transparent,
        width: 1,
      ),
    );
  }
}
