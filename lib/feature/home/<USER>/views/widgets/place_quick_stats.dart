import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/core/widgets/enhanced_ui_components.dart';
import 'package:gather_point/feature/home/<USER>/models/place_detail_model.dart';
import 'package:gather_point/generated/l10n.dart';

class PlaceQuickStats extends StatelessWidget {
  final PlaceDetailModel placeDetail;

  const PlaceQuickStats({
    super.key,
    required this.placeDetail,
  });

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    
    return EnhancedCard(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem(
            Icons.people_rounded,
            '${placeDetail.noGuests ?? 0}',
            s.guests,
            context,
          ),
          _buildStatItem(
            Icons.bed_rounded,
            '${placeDetail.beds ?? 0}',
            s.bedrooms,
            context,
          ),
          _buildStatItem(
            Icons.bathtub_rounded,
            '${placeDetail.baths ?? 0}',
            s.bathrooms,
            context,
          ),
          // Show WiFi if available in facilities
          if (placeDetail.facilities.any((facility) =>
              facility.title.toLowerCase().contains('wifi') ||
              facility.title.toLowerCase().contains('واي فاي')))
            _buildStatItem(Icons.wifi_rounded, '', s.freeWifi, context,iconColor: Colors.green),
          // Show Smart Entry if available in facilities
          if (placeDetail.facilities.any((facility) =>
              facility.title.toLowerCase().contains('smart entry') ||
              facility.title.toLowerCase().contains('دخول ذكي')))
            _buildStatItem(Icons.vpn_key_rounded, '', s.smartEntry, context, iconColor: Colors.green),
        ],
      ),
    );
  }

  Widget _buildStatItem(IconData icon, String value, String label, BuildContext context, {Color? iconColor}) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: context.accentColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            icon,
            size: 24,
            color: iconColor ?? context.accentColor,
          ),
        ),
        const SizedBox(height: 8),
        if (value.isNotEmpty)
          Text(
            value,
            style: AppTextStyles.font16Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
        Text(
          label,
          style: AppTextStyles.font12Regular.copyWith(
            color: context.secondaryTextColor,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
