import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:math' as math;
import 'reserve_screen.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';

import 'package:gather_point/feature/home/<USER>/models/place_detail_model.dart';
import 'package:gather_point/feature/dashboard/data/services/properties_api_service.dart';
import 'package:gather_point/core/services/service_locator.dart';
import 'widgets/enhanced_gallery_viewer.dart';

import 'widgets/shimmer_loading.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:share_plus/share_plus.dart';

class PlaceDetailsScreen extends StatefulWidget {
  final Map<String, dynamic>? placeData;
  final int? placeId;
  final PlaceDetailModel? placeDetail;

  const PlaceDetailsScreen({
    super.key,
    this.placeData,
    this.placeId,
    this.placeDetail,
  }) : assert(placeData != null || placeId != null || placeDetail != null,
            'Either placeData, placeId, or placeDetail must be provided');

  @override
  State<PlaceDetailsScreen> createState() => _PlaceDetailsScreenState();
}

class _PlaceDetailsScreenState extends State<PlaceDetailsScreen>
    with TickerProviderStateMixin {
  bool isFavorite = false;
  bool showFullDescription = false;
  PlaceDetailModel? _placeDetail;
  bool _isLoading = false;
  String? _error;

  // Date selection for reservation
  DateTime _checkInDate = DateTime.now();
  DateTime _checkOutDate = DateTime.now().add(const Duration(days: 2));



  // Gallery controllers
  late PageController _galleryPageController;
  int _currentGalleryIndex = 0;



  @override
  void initState() {
    super.initState();
    _initializeData();
    _galleryPageController = PageController();
  }

  @override
  void dispose() {
    _galleryPageController.dispose();
    super.dispose();
  }

  void _initializeData() {
    if (widget.placeDetail != null) {
      _placeDetail = widget.placeDetail;
      isFavorite = _placeDetail!.favorite;
    } else if (widget.placeId != null) {
      _loadPlaceDetails();
    } else if (widget.placeData != null) {
      // Convert legacy placeData to PlaceDetailModel for compatibility
      _convertLegacyData();
    }
  }

  Future<void> _loadPlaceDetails() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final apiService = getIt<PropertiesApiService>();
      final placeDetail = await apiService.getPlaceDetails(widget.placeId!);
      setState(() {
        _placeDetail = placeDetail;
        isFavorite = placeDetail.favorite;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  void _convertLegacyData() {
    // Convert legacy Map data to PlaceDetailModel for backward compatibility
    final data = widget.placeData!;
    _placeDetail = PlaceDetailModel(
      id: data['id'] ?? 0,
      cityId: data['city_id'] ?? 0,
      title: data['title'] ?? '',
      content: data['description'] ?? data['content'] ?? '',
      image: data['image'],
      video: data['video'],
      price: double.tryParse(data['price']?.toString() ?? '0') ?? 0.0,
      weekendPrice: data['weekend_price'] != null
          ? double.tryParse(data['weekend_price'].toString())
          : null,
      weekPrice: data['week_price'] != null
          ? double.tryParse(data['week_price'].toString())
          : null,
      monthPrice: data['month_price'] != null
          ? double.tryParse(data['month_price'].toString())
          : null,
      lat: data['lat'] != null ? double.tryParse(data['lat'].toString()) : null,
      lon: data['lon'] != null ? double.tryParse(data['lon'].toString()) : null,
      confirmation: data['confirmation'] ?? 0,
      active: data['active'] == 1 || data['active'] == true,
      serviceCategoryId: data['service_category_id'] ?? 0,
      serviceCategoryFormId: data['service_category_form_id'],
      userId: data['user_id'] ?? 0,
      views: data['views'] ?? 0,
      createdAt: data['created_at'] ?? '',
      updatedAt: data['updated_at'] ?? '',
      deletedAt: data['deleted_at'],
      rating: data['rating'] != null || data['reviews'] != null
          ? double.tryParse(
              (data['rating'] ?? data['reviews'])?.toString() ?? '0')
          : null,
      noOfRates: data['no_of_rates'],
      noGuests: data['no_guests'] ?? data['guests'],
      beds: data['beds'] ?? data['bedrooms'],
      baths: data['baths'] ?? data['bathrooms'],
      bookingRules: data['booking_rules'],
      cancelationRules: data['cancelation_rules'],
      includeCommissionDaily: data['include_commission_daily'] ?? 0,
      includeCommissionWeekly: data['include_commission_weekly'] ?? 0,
      includeCommissionMonthly: data['include_commission_monthly'] ?? 0,
      favorite: data['favorite'] ?? false,
      gallery: (data['gallery'] as List<dynamic>?)
              ?.map((item) => GalleryItemModel.fromJson(item))
              .toList() ??
          [],
      facilities: (data['facilities'] as List<dynamic>?)
              ?.map((item) => FacilityItemModel.fromJson(item))
              .toList() ??
          [],
      country: data['country'] ?? '',
      city: data['city'] ?? '',
      url: data['url'] ?? '',
      hoster: HosterModel.fromJson(data['hoster'] ?? {
        'name': '',
        'bio': '',
        'registered_since': '',
        'rating': null,
        'total_no_of_rates': null,
      }),
      tourismPermitNumber: data['tourism_permit_number'],
      ratings: data['ratings'] != null
          ? (data['ratings'] as List<dynamic>?)
                  ?.map((item) => RatingModel.fromJson(item))
                  .toList() ??
              []
          : [],
    );
    isFavorite = _placeDetail!.favorite;
  }

  String getValidImageUrl(String? url) {
    if (url == null || url.isEmpty || url == 'null') {
      return 'https://placehold.co/400x300';
    }
    return url;
  }

  // Date selection methods
  Future<void> _showDateSelectionDialog() async {
    final result = await showDialog<Map<String, DateTime>>(
      context: context,
      builder: (context) => _DateSelectionDialog(
        initialCheckIn: _checkInDate,
        initialCheckOut: _checkOutDate,
      ),
    );

    if (result != null) {
      setState(() {
        _checkInDate = result['checkIn']!;
        _checkOutDate = result['checkOut']!;
      });
    }
  }

  String _formatDateRange() {
    final checkInFormatted = "${_checkInDate.day}/${_checkInDate.month}";
    final checkOutFormatted = "${_checkOutDate.day}/${_checkOutDate.month}";
    return "$checkInFormatted - $checkOutFormatted";
  }

  String _getCancellationPolicyNote(S s) {
    // Try to get the localized string, fallback to hardcoded if not available
    try {
      // This will work once the localization is properly generated
      return 'تذكر أن السياسة التي يضعها المضيف تناسب ظروفك، في حالات نادرة قد تكون مؤهلاً لاسترداد جزئي أو كامل وفقاً لسياسة الموقع.';
    } catch (e) {
      // Fallback for English
      return 'Remember that the policy set by the host suits your circumstances. In rare cases, you may be eligible for a partial or full refund according to the site\'s policy.';
    }
  }

  String _getPolicyContent() {
    // Use new cancellation policy if available
    if (_placeDetail!.cancellationPolicy != null) {
      return _placeDetail!.cancellationPolicy!.formattedDescription;
    }

    // Fallback to legacy rules
    return _placeDetail!.cancelationRules ?? '';
  }





  void _showGallery(BuildContext context, {int initialIndex = 0}) {
    debugPrint('Gallery tap detected');
    final images = _placeDetail!.gallery
        .map((item) => item.image)
        .where((img) => img.isNotEmpty && img != 'null')
        .toList();
    debugPrint('Gallery images: ${images.toString()}');
    if (images.isEmpty && (_placeDetail!.image?.isNotEmpty ?? false)) {
      images.add(_placeDetail!.image!);
      debugPrint('Fallback to main image: ${_placeDetail!.image!}');
    }
    if (images.isEmpty) {
      debugPrint('No images to show in gallery.');
      return;
    }
    debugPrint('Opening EnhancedGalleryViewer...');
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EnhancedGalleryViewer(
          images: images,
          initialIndex: initialIndex,
          heroTag: 'place_image_$initialIndex',
          placeUrl: _placeDetail?.url,
        ),
      ),
    );
  }

  Widget _buildHorizontalGallery() {
    final images = _placeDetail!.gallery
        .map((item) => item.image)
        .where((img) => img.isNotEmpty && img != 'null')
        .toList();

    // If no gallery images, use main image as fallback
    if (images.isEmpty && (_placeDetail!.image?.isNotEmpty ?? false)) {
      images.add(_placeDetail!.image!);
    }

    if (images.isEmpty) {
      return Container(
        color: context.secondaryTextColor.withValues(alpha: 0.1),
        child: Icon(
          Icons.image_not_supported_rounded,
          size: 64,
          color: context.secondaryTextColor,
        ),
      );
    }

    return Stack(
      fit: StackFit.expand,
      children: [
        // Horizontal scrolling gallery
        PageView.builder(
          controller: _galleryPageController,
          itemCount: images.length,
          onPageChanged: (index) {
            setState(() {
              _currentGalleryIndex = index;
            });
          },
          itemBuilder: (context, index) {
            return Hero(
              tag: 'place_image_$index',
              child: GestureDetector(
                onTap: () {
                  debugPrint('Gallery image tapped at index: $index');
                  _showGallery(context, initialIndex: index);
                },
                child: Image.network(
                  getValidImageUrl(images[index]),
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) => Container(
                    color: context.secondaryTextColor.withValues(alpha: 0.1),
                    child: Icon(
                      Icons.image_not_supported_rounded,
                      size: 64,
                      color: context.secondaryTextColor,
                    ),
                  ),
                ),
              ),
            );
          },
        ),

        // Page indicators
        if (images.length > 1)
          Positioned(
            bottom: 16,
            left: 0,
            right: 0,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(
                images.length,
                (index) => AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  width: _currentGalleryIndex == index ? 16 : 8,
                  height: 8,
                  decoration: BoxDecoration(
                    color: _currentGalleryIndex == index
                        ? Colors.white
                        : Colors.white.withValues(alpha: 0.4),
                    borderRadius: BorderRadius.circular(4),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.3),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),

        // Gallery count indicator
        if (images.length > 1)
          Positioned(
            bottom: 16,
            right: 16,
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 6,
              ),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.7),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(
                    Icons.photo_library_rounded,
                    color: Colors.white,
                    size: 16,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${_currentGalleryIndex + 1}/${images.length}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    if (_isLoading) {
      return const PlaceDetailsShimmer();
    }

    if (_error != null) {
      return Scaffold(
        backgroundColor: context.backgroundColor,
        appBar: AppBar(
          backgroundColor: context.backgroundColor,
          elevation: 0,
          leading: IconButton(
            icon: Icon(Icons.arrow_back_ios_rounded,
                color: context.primaryTextColor),
            onPressed: () => Navigator.pop(context),
          ),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error_outline,
                  size: 64, color: context.secondaryTextColor),
              const SizedBox(height: 16),
              Text(
                s.dataLoadError,
                style: AppTextStyles.font18Bold
                    .copyWith(color: context.primaryTextColor),
              ),
              const SizedBox(height: 8),
              Text(
                _error!,
                style: AppTextStyles.font14Regular
                    .copyWith(color: context.secondaryTextColor),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  if (widget.placeId != null) {
                    _loadPlaceDetails();
                  }
                },
                child: Text(s.retry),
              ),
            ],
          ),
        ),
      );
    }

    if (_placeDetail == null) {
      return Scaffold(
        backgroundColor: context.backgroundColor,
        appBar: AppBar(
          backgroundColor: context.backgroundColor,
          elevation: 0,
          leading: IconButton(
            icon: Icon(Icons.arrow_back_ios_rounded,
                color: context.primaryTextColor),
            onPressed: () => Navigator.pop(context),
          ),
        ),
        body: Center(child: Text(s.noDataAvailable)),
      );
    }

    return Scaffold(
      backgroundColor: context.backgroundColor,
      body: CustomScrollView(
        slivers: [
          // Hero Image Section with App Bar
          SliverAppBar(
            expandedHeight: 300,
            pinned: true,
            backgroundColor: context.backgroundColor,
            elevation: 0,
            systemOverlayStyle: context.isDarkMode
                ? SystemUiOverlayStyle.light
                : SystemUiOverlayStyle.dark,
            leading: Container(
              margin: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.9),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: IconButton(
                icon: const Icon(
                  Icons.arrow_back_ios_rounded,
                  color: Colors.black,
                ),
                onPressed: () => Navigator.pop(context),
              ),
            ),
            actions: [
              Container(
                margin: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.9),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: IconButton(
                  icon: const Icon(
                    Icons.share_rounded,
                    color: Colors.black,
                  ),
                  onPressed: () {
                    final url = _placeDetail?.url;
                    if (url != null && url.isNotEmpty) {
                      Share.share(url);
                    }
                  },
                ),
              ),
              Container(
                margin: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.9),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: IconButton(
                  icon: Icon(
                    isFavorite
                        ? Icons.favorite_rounded
                        : Icons.favorite_border_rounded,
                    color: isFavorite ? Colors.red : Colors.black,
                  ),
                  onPressed: () {
                    setState(() {
                      isFavorite = !isFavorite;
                    });
                  },
                ),
              ),
            ],
            flexibleSpace: FlexibleSpaceBar(
              background: _buildHorizontalGallery(),
            ),
          ),

          // Content Section
          SliverToBoxAdapter(
            child: Container(
              color: context.backgroundColor,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title and Basic Info Section
                  _buildTitleSection(s),

                  // Quick Stats Section
                  _buildQuickStatsSection(s),

                  // Host Information Section
                  _buildHostSection(s),

                  // Description Section
                  _buildDescriptionSection(s),

                  // Amenities Section
                  _buildAmenitiesSection(s),

                  // Location Section
                  _buildLocationSection(s),

                  // Cancellation Policy Section
                  _buildCancellationPolicySection(s),

                  // Reviews Section
                  _buildReviewsSection(s),

                  const SizedBox(height: 100), // Space for bottom booking bar
                ],
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: _buildBookingBottomBar(s),
    );
  }

  Widget _buildTitleSection(S s) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            _placeDetail!.title,
            style: AppTextStyles.font24Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 8),

          // Tourism Permit Number
          if (_placeDetail!.tourismPermitNumber != null &&
              _placeDetail!.tourismPermitNumber!.isNotEmpty) ...[
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: AppColors.yellow.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: AppColors.yellow.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(
                    Icons.verified_outlined,
                    size: 16,
                    color: AppColors.yellow,
                  ),
                  const SizedBox(width: 6),
                  Text(
                    '${s.tourismPermitNumber}: ${_placeDetail!.tourismPermitNumber}',
                    style: AppTextStyles.font12SemiBold.copyWith(
                      color: context.primaryTextColor,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
          ],

          // Location
          Row(
            children: [
              Icon(
                Icons.location_on_outlined,
                size: 16,
                color: context.secondaryTextColor,
              ),
              const SizedBox(width: 4),
              Text(
                "${_placeDetail!.city}, ${_placeDetail!.country}",
                style: AppTextStyles.font14Regular.copyWith(
                  color: context.secondaryTextColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Rating and Reviews
          Row(
            children: [
              // Rating with stars
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: AppColors.yellow.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      Icons.star,
                      size: 16,
                      color: AppColors.yellow,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      _placeDetail!.rating?.toStringAsFixed(1) ?? '0.0',
                      style: AppTextStyles.font14SemiBold.copyWith(
                        color: context.primaryTextColor,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 12),

              // Reviews count
              Text(
                '${_placeDetail!.ratings.length} ${s.reviews}',
                style: AppTextStyles.font14Regular.copyWith(
                  color: context.secondaryTextColor,
                ),
              ),

              const Spacer(),

              // Superhost badge if applicable
              if (_placeDetail!.hoster.rating != null && _placeDetail!.hoster.rating! >= 4.8)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppColors.yellow,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    s.superhost,
                    style: AppTextStyles.font12SemiBold.copyWith(
                      color: Colors.white,
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStatsSection(S s) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        children: [
          // Guests
          if (_placeDetail!.noGuests != null)
            _buildStatItem(
              icon: Icons.people_outline,
              label: '${_placeDetail!.noGuests} ${s.guests}',
            ),

          // Bedrooms
          if (_placeDetail!.beds != null)
            _buildStatItem(
              icon: Icons.bed_outlined,
              label: '${_placeDetail!.beds} ${s.bedrooms}',
            ),

          // Bathrooms
          if (_placeDetail!.baths != null)
            _buildStatItem(
              icon: Icons.bathroom_outlined,
              label: '${_placeDetail!.baths} ${s.bathrooms}',
            ),
        ],
      ),
    );
  }

  Widget _buildStatItem({required IconData icon, required String label}) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(16),
        margin: const EdgeInsets.only(right: 8),
        decoration: BoxDecoration(
          color: context.cardColor,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: context.secondaryTextColor.withValues(alpha: 0.1),
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              size: 24,
              color: context.accentColor,
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: AppTextStyles.font12Regular.copyWith(
                color: context.secondaryTextColor,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildHostSection(S s) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: context.cardColor,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              s.hostedBy,
              style: AppTextStyles.font18Bold.copyWith(
                color: context.primaryTextColor,
              ),
            ),
            const SizedBox(height: 16),

            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Host Avatar
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: AppColors.yellow.withValues(alpha: 0.2),
                    border: Border.all(
                      color: AppColors.yellow,
                      width: 2,
                    ),
                  ),
                  child: const Icon(
                    Icons.person,
                    size: 30,
                    color: AppColors.yellow,
                  ),
                ),
                const SizedBox(width: 16),

                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Host Name
                      Text(
                        _placeDetail!.hoster.name,
                        style: AppTextStyles.font18Bold.copyWith(
                          color: context.primaryTextColor,
                        ),
                      ),
                      const SizedBox(height: 4),

                      // Host Rating
                      if (_placeDetail!.hoster.rating != null)
                        Row(
                          children: [
                            const Icon(
                              Icons.star,
                              size: 16,
                              color: AppColors.yellow,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              _placeDetail!.hoster.rating!.toStringAsFixed(1),
                              style: AppTextStyles.font14SemiBold.copyWith(
                                color: context.primaryTextColor,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              '${_placeDetail!.hoster.totalNoOfRates ?? 0} ${s.reviews}',
                              style: AppTextStyles.font14Regular.copyWith(
                                color: context.secondaryTextColor,
                              ),
                            ),
                          ],
                        ),

                      // Since hosting (formatted)
                      if (_placeDetail!.hoster.registeredSince.isNotEmpty)
                        Text(
                          '${s.since} ${_placeDetail!.hoster.registeredSince}',
                          style: AppTextStyles.font14Regular.copyWith(
                            color: context.secondaryTextColor,
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),

            // Host Bio
            if (_placeDetail!.hoster.bio.isNotEmpty) ...[
              const SizedBox(height: 16),
              Text(
                _placeDetail!.hoster.bio,
                style: AppTextStyles.font14Regular.copyWith(
                  color: context.secondaryTextColor,
                  height: 1.5,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDescriptionSection(S s) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            s.aboutThisPlace,
            style: AppTextStyles.font18Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 12),

          Text(
            _placeDetail!.content,
            style: AppTextStyles.font14Regular.copyWith(
              color: context.secondaryTextColor,
              height: 1.5,
            ),
            maxLines: showFullDescription ? null : 3,
            overflow: showFullDescription ? null : TextOverflow.ellipsis,
          ),

          if (_placeDetail!.content.length > 150)
            TextButton(
              onPressed: () {
                setState(() {
                  showFullDescription = !showFullDescription;
                });
              },
              child: Text(
                showFullDescription ? s.showLess : s.showMore,
                style: AppTextStyles.font14SemiBold.copyWith(
                  color: context.accentColor,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildAmenitiesSection(S s) {
    if (_placeDetail!.facilities.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            s.whatThisPlaceOffers,
            style: AppTextStyles.font18Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),

          // Show first 6 amenities
          ...(_placeDetail!.facilities.take(6).map((facility) =>
            Container(
              margin: const EdgeInsets.only(bottom: 12),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.check,
                      size: 16,
                      color: Colors.green,
                    ),
                  ),
                  const SizedBox(width: 12),

                  Expanded(
                    child: Text(
                      facility.title,
                      style: AppTextStyles.font14Regular.copyWith(
                        color: context.primaryTextColor,
                      ),
                    ),
                  ),

                  if (facility.count > 0)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: context.accentColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '${facility.count}',
                        style: AppTextStyles.font12SemiBold.copyWith(
                          color: context.accentColor,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          )),

          // Show all amenities button if more than 6
          if (_placeDetail!.facilities.length > 6)
            Container(
              margin: const EdgeInsets.only(top: 16),
              width: double.infinity,
              child: OutlinedButton(
                onPressed: () {
                  _showAllAmenitiesDialog(context, s);
                },
                style: OutlinedButton.styleFrom(
                  side: BorderSide(color: context.accentColor),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(
                  '${s.showAllAmenities} (${_placeDetail!.facilities.length})',
                  style: AppTextStyles.font14SemiBold.copyWith(
                    color: context.accentColor,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  void _showAllAmenitiesDialog(BuildContext context, S s) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Container(
          padding: const EdgeInsets.all(20),
          constraints: const BoxConstraints(maxHeight: 500),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                s.whatThisPlaceOffers,
                style: AppTextStyles.font18Bold.copyWith(
                  color: context.primaryTextColor,
                ),
              ),
              const SizedBox(height: 16),

              Expanded(
                child: ListView.builder(
                  itemCount: _placeDetail!.facilities.length,
                  itemBuilder: (context, index) {
                    final facility = _placeDetail!.facilities[index];
                    return Container(
                      margin: const EdgeInsets.only(bottom: 12),
                      child: Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Colors.green.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: const Icon(
                              Icons.check,
                              size: 16,
                              color: Colors.green,
                            ),
                          ),
                          const SizedBox(width: 12),

                          Expanded(
                            child: Text(
                              facility.title,
                              style: AppTextStyles.font14Regular.copyWith(
                                color: context.primaryTextColor,
                              ),
                            ),
                          ),

                          if (facility.count > 0)
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: context.accentColor.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                '${facility.count}',
                                style: AppTextStyles.font12SemiBold.copyWith(
                                  color: context.accentColor,
                                ),
                              ),
                            ),
                        ],
                      ),
                    );
                  },
                ),
              ),

              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () => Navigator.pop(context),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: context.accentColor,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text(
                    'Close',
                    style: AppTextStyles.font14SemiBold.copyWith(
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLocationSection(S s) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            s.whereYoullBe,
            style: AppTextStyles.font18Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            "${_placeDetail!.city}, ${_placeDetail!.country}",
            style: AppTextStyles.font14Regular.copyWith(
              color: context.secondaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          _buildMapWidget(),
        ],
      ),
    );
  }

  Widget _buildMapWidget() {
    // Default location (Riyadh) if no coordinates are available
    const defaultLat = 24.7136;
    const defaultLon = 46.6753;

    final originalLat = _placeDetail!.lat ?? defaultLat;
    final originalLon = _placeDetail!.lon ?? defaultLon;

    // Add random offset for privacy (radius of ~500-1000 meters)
    final random = DateTime.now().millisecondsSinceEpoch % 1000;
    final radiusKm = 0.5 + (random / 1000.0) * 0.5; // 0.5-1.0 km radius
    final angle = (random / 1000.0) * 2 * 3.14159; // Random angle

    // Convert radius to lat/lon offset (approximate)
    final latOffset = (radiusKm / 111.0) * math.cos(angle); // 1 degree lat ≈ 111 km
    final lonOffset = (radiusKm / (111.0 * math.cos(originalLat * math.pi / 180))) * math.sin(angle);

    final displayLat = originalLat + latOffset;
    final displayLon = originalLon + lonOffset;

    return Container(
      height: 200,
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: context.secondaryTextColor.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Stack(
          children: [
            FlutterMap(
              options: MapOptions(
                initialCenter: LatLng(displayLat, displayLon),
                initialZoom: 14.0, // Slightly zoomed out to show area
                interactionOptions: const InteractionOptions(
                  flags: InteractiveFlag.pinchZoom | InteractiveFlag.drag,
                ),
                backgroundColor: context.backgroundColor,
              ),
              children: [
                // Primary tile layer with fallback
                TileLayer(
                  urlTemplate: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
                  subdomains: const ['a', 'b', 'c'],
                  userAgentPackageName: 'sa.gatherpoint.app',
                  maxZoom: 19,
                  errorTileCallback: (tile, error, stackTrace) {
                    debugPrint('Map tile error: $error');
                  },
                  fallbackUrl: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                ),

                // Area circle to show approximate location
                CircleLayer(
                  circles: [
                    CircleMarker(
                      point: LatLng(displayLat, displayLon),
                      radius: 75, // 75 meter radius
                      color: context.accentColor.withValues(alpha: 0.2),
                      borderColor: context.accentColor,
                      borderStrokeWidth: 2,
                    ),
                  ],
                ),

                // Center marker
                MarkerLayer(
                  markers: [
                    Marker(
                      point: LatLng(displayLat, displayLon),
                      width: 50,
                      height: 50,
                      child: Container(
                        decoration: BoxDecoration(
                          color: context.accentColor,
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.3),
                              blurRadius: 8,
                              offset: const Offset(0, 3),
                            ),
                          ],
                          border: Border.all(
                            color: Colors.white,
                            width: 3,
                          ),
                        ),
                        child: const Icon(
                          Icons.location_on,
                          color: Colors.white,
                          size: 28,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),

            // Privacy notice overlay
            Positioned(
              top: 8,
              right: 8,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.7),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.info_outline,
                      size: 12,
                      color: Colors.white.withValues(alpha: 0.8),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Approximate area',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.8),
                        fontSize: 10,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),


          ],
        ),
      ),
    );
  }

  Widget _buildCancellationPolicySection(S s) {
    // Show if either cancellation policy or legacy rules exist
    final hasPolicy = _placeDetail!.cancellationPolicy != null;
    final hasLegacyRules = _placeDetail!.cancelationRules != null &&
                          _placeDetail!.cancelationRules!.isNotEmpty;

    if (!hasPolicy && !hasLegacyRules) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            s.cancellationPolicy,
            style: AppTextStyles.font18Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),

          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: context.cardColor,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: context.secondaryTextColor.withValues(alpha: 0.1),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Policy icon and title
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: AppColors.yellow.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.policy_outlined,
                        size: 20,
                        color: AppColors.yellow,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      s.cancellationRules,
                      style: AppTextStyles.font16SemiBold.copyWith(
                        color: context.primaryTextColor,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Policy content
                Text(
                  _getPolicyContent(),
                  style: AppTextStyles.font14Regular.copyWith(
                    color: context.secondaryTextColor,
                    height: 1.5,
                  ),
                ),

                // Show policy type badge if using new system
                if (_placeDetail!.cancellationPolicy != null) ...[
                  const SizedBox(height: 12),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: AppColors.yellow.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: AppColors.yellow.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Text(
                      _placeDetail!.cancellationPolicy!.policyTypeDisplayName,
                      style: AppTextStyles.font12SemiBold.copyWith(
                        color: AppColors.yellow,
                      ),
                    ),
                  ),
                ],

                const SizedBox(height: 16),

                // Info note
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Colors.blue.withValues(alpha: 0.2),
                    ),
                  ),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(
                        Icons.info_outline,
                        size: 16,
                        color: Colors.blue.shade600,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _getCancellationPolicyNote(s),
                          style: AppTextStyles.font12Regular.copyWith(
                            color: Colors.blue.shade700,
                            height: 1.4,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReviewsSection(S s) {
    final ratingsList = _placeDetail!.ratings;
    final rating = ratingsList.isNotEmpty
        ? ratingsList.map((e) => e.value).reduce((a, b) => a + b) /
            ratingsList.length
        : (_placeDetail!.rating ?? 0.0);


    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Large rating display with golden wheat icons
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: context.cardColor,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                // Golden wheat icons and rating
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Left wheat icon
                    Transform.rotate(
                      angle: -0.2,
                      child: const Icon(
                        Icons.agriculture,
                        size: 32,
                        color: AppColors.yellow,
                      ),
                    ),
                    const SizedBox(width: 16),

                    // Large rating number
                    Text(
                      rating.toStringAsFixed(1),
                      style: AppTextStyles.font36ExtraBold.copyWith(
                        color: context.primaryTextColor,
                      ),
                    ),

                    const SizedBox(width: 16),
                    // Right wheat icon
                    Transform.rotate(
                      angle: 0.2,
                      child: const Icon(
                        Icons.agriculture,
                        size: 32,
                        color: AppColors.yellow,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 12),

                // "Preferred by guests" text
                Text(
                  'مفضّل لدى الضيوف',
                  style: AppTextStyles.font18Bold.copyWith(
                    color: context.primaryTextColor,
                  ),
                ),

                const SizedBox(width: 8),

                // Rating description
                Text(
                  'هذا المسكن من أفضل ${(rating * 20).round()}% من المساكن المماثلة، استناداً إلى التقييمات والمراجعات وبيانات الموثوقية',
                  style: AppTextStyles.font14Regular.copyWith(
                    color: context.secondaryTextColor,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Individual reviews
          if (ratingsList.isNotEmpty) ...[
            ...ratingsList.take(2).map((review) => Container(
              margin: const EdgeInsets.only(bottom: 16),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: context.cardColor,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: context.secondaryTextColor.withValues(alpha: 0.1),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      // User avatar
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: AppColors.yellow.withValues(alpha: 0.2),
                          border: Border.all(
                            color: AppColors.yellow,
                            width: 2,
                          ),
                        ),
                        child: const Icon(
                          Icons.person,
                          size: 20,
                          color: AppColors.yellow,
                        ),
                      ),
                      const SizedBox(width: 12),

                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              review.userName,
                              style: AppTextStyles.font16SemiBold.copyWith(
                                color: context.primaryTextColor,
                              ),
                            ),
                            Text(
                              review.createdAt,
                              style: AppTextStyles.font12Regular.copyWith(
                                color: context.secondaryTextColor,
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Star rating
                      Row(
                        children: List.generate(5, (index) => Icon(
                          index < review.value.round()
                              ? Icons.star
                              : Icons.star_border,
                          size: 16,
                          color: AppColors.yellow,
                        )),
                      ),
                    ],
                  ),

                  const SizedBox(height: 12),

                  Text(
                    review.comment,
                    style: AppTextStyles.font14Regular.copyWith(
                      color: context.secondaryTextColor,
                      height: 1.5,
                    ),
                  ),
                ],
              ),
            )),

            // Show all reviews button
            if (ratingsList.length > 2)
              Container(
                margin: const EdgeInsets.only(top: 16),
                width: double.infinity,
                child: OutlinedButton(
                  onPressed: () {
                    // Navigate to all reviews screen
                  },
                  style: OutlinedButton.styleFrom(
                    side: BorderSide(color: context.accentColor),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: Text(
                    'إظهار كل التقييمات البالغ عددها ${ratingsList.length} تقييماً',
                    style: AppTextStyles.font14SemiBold.copyWith(
                      color: context.accentColor,
                    ),
                  ),
                ),
              ),
          ] else ...[
            // No reviews state
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: context.cardColor,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: context.secondaryTextColor.withValues(alpha: 0.1),
                ),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.rate_review_outlined,
                    size: 48,
                    color: context.secondaryTextColor.withValues(alpha: 0.5),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'لا توجد تقييمات بعد',
                    style: AppTextStyles.font16SemiBold.copyWith(
                      color: context.primaryTextColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'كن أول من يقيم هذا المكان',
                    style: AppTextStyles.font14Regular.copyWith(
                      color: context.secondaryTextColor,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildBookingBottomBar(S s) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: context.cardColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Price with currency
                  RichText(
                    text: TextSpan(
                      text: s.priceWithCurrency(_placeDetail!.price.toStringAsFixed(0)),
                      style: AppTextStyles.font20Bold.copyWith(
                        color: context.primaryTextColor,
                      ),
                      children: [
                        TextSpan(
                          text: ' / ${s.night}',
                          style: AppTextStyles.font14Regular.copyWith(
                            color: context.secondaryTextColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 4),

                  // Date range with change button
                  Row(
                    children: [
                      Text(
                        _formatDateRange(),
                        style: AppTextStyles.font12Regular.copyWith(
                          color: context.secondaryTextColor,
                        ),
                      ),
                      const SizedBox(width: 8),
                      GestureDetector(
                        onTap: _showDateSelectionDialog,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: context.accentColor,
                              width: 1,
                            ),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            s.change,
                            style: AppTextStyles.font10Bold.copyWith(
                              color: context.accentColor,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(width: 16),

            // Reserve button
            Expanded(
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.yellow, // Pink color as shown in image
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  elevation: 0,
                ),
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => ReserveScreen(
                        placeTitle: _placeDetail!.title,
                        policy: _placeDetail!.cancelationRules ?? '',
                        pricePerNight: _placeDetail!.price,
                        checkInDate: _checkInDate,
                        checkOutDate: _checkOutDate,
                      ),
                    ),
                  );
                },
                child: Text(
                  'حجز',
                  style: AppTextStyles.font16SemiBold.copyWith(
                    color: AppColors.black,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Date Selection Dialog
class _DateSelectionDialog extends StatefulWidget {
  final DateTime initialCheckIn;
  final DateTime initialCheckOut;

  const _DateSelectionDialog({
    required this.initialCheckIn,
    required this.initialCheckOut,
  });

  @override
  State<_DateSelectionDialog> createState() => _DateSelectionDialogState();
}

class _DateSelectionDialogState extends State<_DateSelectionDialog> {
  late DateTime _checkInDate;
  late DateTime _checkOutDate;

  @override
  void initState() {
    super.initState();
    _checkInDate = widget.initialCheckIn;
    _checkOutDate = widget.initialCheckOut;
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Select Dates',
              style: AppTextStyles.font20Bold.copyWith(
                color: context.primaryTextColor,
              ),
            ),
            const SizedBox(height: 24),

            // Check-in date
            _buildDateSelector(
              label: s.checkIn,
              date: _checkInDate,
              onTap: () => _selectDate(true),
            ),

            const SizedBox(height: 16),

            // Check-out date
            _buildDateSelector(
              label: s.checkOut,
              date: _checkOutDate,
              onTap: () => _selectDate(false),
            ),

            const SizedBox(height: 24),

            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: Text(
                    s.cancel,
                    style: AppTextStyles.font14SemiBold.copyWith(
                      color: context.secondaryTextColor,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context, {
                      'checkIn': _checkInDate,
                      'checkOut': _checkOutDate,
                    });
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: context.accentColor,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text(
                    'Done',
                    style: AppTextStyles.font14SemiBold.copyWith(
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateSelector({
    required String label,
    required DateTime date,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(
            color: context.secondaryTextColor.withValues(alpha: 0.3),
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(
              Icons.calendar_today,
              size: 20,
              color: context.accentColor,
            ),
            const SizedBox(width: 12),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: AppTextStyles.font12Regular.copyWith(
                    color: context.secondaryTextColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  "${date.day}/${date.month}/${date.year}",
                  style: AppTextStyles.font16SemiBold.copyWith(
                    color: context.primaryTextColor,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _selectDate(bool isCheckIn) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isCheckIn ? _checkInDate : _checkOutDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (picked != null) {
      setState(() {
        if (isCheckIn) {
          _checkInDate = picked;
          // Ensure check-out is after check-in
          if (_checkOutDate.isBefore(_checkInDate) ||
              _checkOutDate.isAtSameMomentAs(_checkInDate)) {
            _checkOutDate = _checkInDate.add(const Duration(days: 1));
          }
        } else {
          // Ensure check-out is after check-in
          if (picked.isAfter(_checkInDate)) {
            _checkOutDate = picked;
          }
        }
      });
    }
  }
}
