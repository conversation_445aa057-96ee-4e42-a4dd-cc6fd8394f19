// VerifyOTPViewBody
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:conditional_builder_null_safety/conditional_builder_null_safety.dart';
import 'package:gather_point/core/components/primary_button.dart';
import 'package:gather_point/core/routing/routes_keys.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/utils/app_assets.dart';
import 'package:gather_point/core/utils/sound_manager.dart';
import 'package:gather_point/feature/auth/presentation/Manager/validate_otp_cubit/validate_otp_cubit.dart';
import 'package:gather_point/feature/auth/presentation/views/widgets/enter_otp_section.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:go_router/go_router.dart';

class VerifyOTPViewBody extends StatefulWidget {
  final String phone;

  const VerifyOTPViewBody({super.key, required this.phone});

  @override
  State<VerifyOTPViewBody> createState() => _VerifyOTPViewBodyState();
}

class _VerifyOTPViewBodyState extends State<VerifyOTPViewBody> {
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  bool _showVerifyButton = false;

  void _onOtpCompleted() {
    setState(() {
      _showVerifyButton = true;
    });
    // Automatically verify OTP when completed
    _verifyOtp();
  }

  void _verifyOtp() {
    SoundManager.playClickSound();
    if (formKey.currentState?.validate() ?? false) {
      context.read<ValidateOtpCubit>().validateOtp(phone: widget.phone);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return BlocConsumer<ValidateOtpCubit, ValidateOtpState>(
      listener: (context, state) {
        if (state is ValidateOtpSuccessState) {
          GoRouter.of(context).go(RoutesKeys.kHomeViewTab);
        } else if (state is ValidateOtpFailureState) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.errMessage),
              backgroundColor: isDark ? AppColors.darkGrey : AppColors.red,
            ),
          );
        } else if (state is ResendOtpSuccessState) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(S.of(context).verificationCodeSent),
              backgroundColor: AppColors.yellow,
            ),
          );
        } else if (state is ResendOtpFailureState) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.errMessage),
              backgroundColor: isDark ? AppColors.darkGrey : AppColors.red,
            ),
          );
        }
      },
      builder: (context, state) {
        return SingleChildScrollView(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Column(
            children: [
              Image.asset(AppAssets.imagesVerifyOtp),
              const SizedBox(height: 35),
              Text(
                S.of(context).enterVerificationCode,
                style: AppTextStyles.font20Bold.copyWith(
                  color: isDark ? AppColors.white : AppColors.black,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),
              Text(
                S.of(context).verificationCodeSent,
                style: AppTextStyles.font13Bold.copyWith(
                  color: isDark ? AppColors.lightGrey : AppColors.darkGrey8,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 35),
              EnterOTPSection(
                formKey: formKey,
                onOtpCompleted: _onOtpCompleted,
              ),
              const SizedBox(height: 20),
              // Resend code option
              TextButton(
                onPressed: state is ResendOtpLoadingState ? null : () {
                  SoundManager.playClickSound();
                  context.read<ValidateOtpCubit>().resendOtp(phone: widget.phone);
                },
                child: state is ResendOtpLoadingState
                    ? Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              color: AppColors.yellow,
                              strokeWidth: 2,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            S.of(context).resendCode,
                            style: AppTextStyles.font13Bold.copyWith(
                              color: AppColors.yellow,
                            ),
                          ),
                        ],
                      )
                    : RichText(
                        text: TextSpan(
                          text: '${S.of(context).didntReceiveCode} ',
                          style: AppTextStyles.font13Bold.copyWith(
                            color: isDark ? AppColors.lightGrey : AppColors.darkGrey8,
                            fontWeight: FontWeight.w400,
                          ),
                          children: [
                            TextSpan(
                              text: S.of(context).resendCode,
                              style: AppTextStyles.font13Bold.copyWith(
                                color: AppColors.yellow,
                              ),
                            ),
                          ],
                        ),
                      ),
              ),
              const SizedBox(height: 25),
              // Verify button - shows when OTP is complete
              AnimatedSwitcher(
                duration: const Duration(milliseconds: 300),
                child: _showVerifyButton
                    ? ConditionalBuilder(
                        condition: state is! ValidateOtpLoadingState,
                        builder: (context) => PrimaryButton(
                          onPressed: _verifyOtp,
                          label: S.of(context).proceedLabel,
                          padding: const EdgeInsets.all(12),
                        ),
                        fallback: (context) => const CircularProgressIndicator(
                          color: AppColors.yellow,
                        ),
                      )
                    : const SizedBox.shrink(),
              ),
            ],
          ),
        );
      },
    );
  }
}
