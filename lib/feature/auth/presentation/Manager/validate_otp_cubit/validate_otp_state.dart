part of 'validate_otp_cubit.dart';

sealed class ValidateOtpState extends Equatable {
  const ValidateOtpState();

  @override
  List<Object> get props => [];
}

final class ValidateOtpInitial extends ValidateOtpState {}

final class ValidateOtpLoadingState extends ValidateOtpState {}

final class ValidateOtpSuccessState extends ValidateOtpState {}

final class ValidateOtpFailureState extends ValidateOtpState {
  final String errMessage;

  const ValidateOtpFailureState({required this.errMessage});
}

final class LoginTokenLoadingState extends ValidateOtpState {}

final class LoginTokenSuccessState extends ValidateOtpState {}

final class LoginTokenFailureState extends ValidateOtpState {
  final String errMessage;

  const LoginTokenFailureState({required this.errMessage});
}

final class ResendOtpLoadingState extends ValidateOtpState {}

final class ResendOtpSuccessState extends ValidateOtpState {}

final class ResendOtpFailureState extends ValidateOtpState {
  final String errMessage;

  const ResendOtpFailureState({required this.errMessage});
}

final class FetchClientHistoryLoadingState extends ValidateOtpState {}

final class FetchClientHistorySuccessState extends ValidateOtpState {
  final int credit;

  const FetchClientHistorySuccessState({required this.credit});
}

final class FetchClientHistoryFailureState extends ValidateOtpState {
  final String errMessage;

  const FetchClientHistoryFailureState({required this.errMessage});
}