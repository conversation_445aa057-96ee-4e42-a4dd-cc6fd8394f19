import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:gather_point/core/routing/routes_branches.dart';
import 'package:gather_point/core/utils/app_constants.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:gather_point/feature/auth/Domain/Use%20Cases/login_token_use_case.dart';
import 'package:gather_point/feature/auth/Domain/Use%20Cases/validate_otp_use_case.dart';
import 'package:gather_point/feature/auth/Domain/Use%20Cases/resend_otp_use_case.dart';
import 'package:hive_flutter/hive_flutter.dart';

part 'validate_otp_state.dart';

class ValidateOtpCubit extends Cubit<ValidateOtpState> {
  ValidateOtpCubit({
    required this.validateOTPUseCase,
    required this.loginTokenUseCase,
    required this.resendOtpUseCase,
  }) : super(ValidateOtpInitial());

  final ValidateOTPUseCase validateOTPUseCase;
  final LoginTokenUseCase loginTokenUseCase;
  final ResendOtpUseCase resendOtpUseCase;

  String otpCode = '';

  void validateOtp({required String phone}) async {
    if (isClosed) return;
    emit(ValidateOtpLoadingState());

    final result =
        await validateOTPUseCase.call({'phone': phone, 'otp': otpCode});

    if (isClosed) return;

    result.fold(
      (failure) {
        if (!isClosed) {
          emit(ValidateOtpFailureState(errMessage: failure.errMessage));
        }
      },
      (success) {
        Hive.box<UserEntity>(AppConstants.kMyProfileBoxName).put(AppConstants.kMyProfileKey, success);

        // Update global hoster mode notifier
        isHosterModeNotifier.value = success.isHosterMode;

        if (!isClosed) {
          emit(ValidateOtpSuccessState());
        }
      },
    );
  }

  void loginToken({required String accessToken}) async {
    if (isClosed) return;
    emit(LoginTokenLoadingState());

    final result = await loginTokenUseCase.call(accessToken);

    if (isClosed) return;

    result.fold(
      (failure) {
        if (!isClosed) {
          emit(LoginTokenFailureState(errMessage: failure.errMessage));
        }
      },
      (success) {
        Hive.box<UserEntity>(AppConstants.kMyProfileBoxName).put(AppConstants.kMyProfileKey, success);

        // Update global hoster mode notifier
        isHosterModeNotifier.value = success.isHosterMode;

        if (!isClosed) {
          emit(LoginTokenSuccessState());
        }
      },
    );
  }

  void resendOtp({required String phone}) async {
    if (isClosed) return;
    emit(ResendOtpLoadingState());

    final result = await resendOtpUseCase.call(phone);

    if (isClosed) return;

    result.fold(
      (failure) {
        if (!isClosed) {
          emit(ResendOtpFailureState(errMessage: failure.errMessage));
        }
      },
      (success) {
        if (!isClosed) {
          emit(ResendOtpSuccessState());
        }
      },
    );
  }

}
