import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:gather_point/core/routing/routes_branches.dart';
import 'package:gather_point/core/services/social_auth_service.dart';
import 'package:gather_point/core/utils/app_constants.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:gather_point/feature/auth/Domain/Use%20Cases/login_guest_use_case.dart';
import 'package:gather_point/feature/auth/Domain/Use%20Cases/login_token_use_case.dart';
import 'package:gather_point/feature/auth/Domain/Use%20Cases/login_use_case.dart';
import 'package:hive/hive.dart';

part 'login_state.dart';

class LoginCubit extends Cubit<LoginState> {
  LoginCubit(
      {required this.loginUseCase,
      required this.loginTokenUseCase,
      required this.loginGuestUseCase})
      : super(LoginInitial());

  final LoginUseCase loginUseCase;
  final LoginTokenUseCase loginTokenUseCase;
  final LoginGuestUseCase loginGuestUseCase;

  TextEditingController phoneController = TextEditingController();

  Future<void> login() async {
    if (isClosed) return;
    emit(LoginLoadingState());
    final result = await loginUseCase.call((phoneController.text));

    if (isClosed) return;

    result.fold(
      (l) {
        if (!isClosed) {
          emit(LoginFailureState(errMessage: l.errMessage));
        }
      },
      (r) {
        if (!isClosed) {
          emit(LoginSuccessState());
        }
      },
    );
  }

  void loginGuest() async {
    if (isClosed) return;
    emit(LoginGuestLoadingState());

    final result = await loginGuestUseCase.call("");

    if (isClosed) return;

    result.fold(
      (failure) {
        if (!isClosed) {
          emit(LoginGuestFailureState(errMessage: failure.errMessage));
        }
      },
      (success) {
        Hive.box<UserEntity>(AppConstants.kMyProfileBoxName)
            .put(AppConstants.kMyProfileKey, success);

        // Update global hoster mode notifier
        isHosterModeNotifier.value = success.isHosterMode;

        if (!isClosed) {
          emit(LoginGuestSuccessState());
        }
      },
    );
  }

  // Social Login Methods
  Future<void> signInWithGoogle() async {
    if (isClosed) return;
    emit(const SocialLoginLoadingState(provider: 'Google'));

    try {
      final socialAuthService = SocialAuthService();
      final googleToken = await socialAuthService.signInWithGoogle();

      if (isClosed) return;

      if (googleToken == null) {
        if (!isClosed) {
          emit(const SocialLoginFailureState(
            errMessage: 'Google sign-in was cancelled',
            provider: 'Google',
          ));
        }
        return;
      }

      final result = await loginTokenUseCase.call(googleToken);

      if (isClosed) return;

      if (result.isLeft()) {
        final failure = result.fold((l) => l, (r) => null)!;
        if (!isClosed) {
          emit(SocialLoginFailureState(
            errMessage: failure.errMessage,
            provider: 'Google',
          ));
        }
      } else {
        final userEntity = result.fold((l) => null, (r) => r);
        // Save user data
        Hive.box<UserEntity>(AppConstants.kMyProfileBoxName)
            .put(AppConstants.kMyProfileKey, userEntity!);

        // Update global hoster mode notifier
        isHosterModeNotifier.value = userEntity.isHosterMode;

        if (!isClosed) {
          emit(const SocialLoginSuccessState(provider: 'Google'));
        }
      }
    } catch (error) {
      if (!isClosed) {
        emit(SocialLoginFailureState(
          errMessage: 'Google sign-in failed: $error',
          provider: 'Google',
        ));
      }
    }
  }

  Future<void> signInWithApple() async {
    if (isClosed) return;
    emit(const SocialLoginLoadingState(provider: 'Apple'));

    try {
      // For now, we'll create a simple implementation
      // This will be properly integrated with dependency injection later
      final socialAuthService = SocialAuthService();
      final appleToken = await socialAuthService.signInWithApple();

      if (isClosed) return;

      if (appleToken == null) {
        if (!isClosed) {
          emit(const SocialLoginFailureState(
            errMessage: 'Apple sign-in was cancelled',
            provider: 'Apple',
          ));
        }
        return;
      }

      // Use the existing login token use case
      final result = await loginTokenUseCase.call(appleToken);

      if (isClosed) return;

      if (result.isLeft()) {
        final failure = result.fold((l) => l, (r) => null)!;
        if (!isClosed) {
          emit(SocialLoginFailureState(
            errMessage: failure.errMessage,
            provider: 'Apple',
          ));
        }
      } else {
        final userEntity = result.fold((l) => null, (r) => r);
        // Save user data
        Hive.box<UserEntity>(AppConstants.kMyProfileBoxName)
            .put(AppConstants.kMyProfileKey, userEntity!);

        // Update global hoster mode notifier
        isHosterModeNotifier.value = userEntity.isHosterMode;

        if (!isClosed) {
          emit(const SocialLoginSuccessState(provider: 'Apple'));
        }
      }
    } catch (error) {
      if (!isClosed) {
        emit(SocialLoginFailureState(
          errMessage: 'Apple sign-in failed: $error',
          provider: 'Apple',
        ));
      }
    }
  }

  // Helper method to set loading state
  void setLoading(bool isLoading) {
    if (isClosed) return;
    if (isLoading) {
      emit(LoginLoadingState());
    } else {
      emit(LoginInitial());
    }
  }

  @override
  Future<void> close() {
    phoneController.dispose();
    return super.close();
  }
}
