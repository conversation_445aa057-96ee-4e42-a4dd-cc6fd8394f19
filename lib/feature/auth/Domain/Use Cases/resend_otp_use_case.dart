import 'package:dartz/dartz.dart';
import 'package:gather_point/core/errors/failure.dart';
import 'package:gather_point/core/use_cases/use_case.dart';
import 'package:gather_point/feature/auth/Domain/Repos/login_repo.dart';

class ResendOtpUseCase extends UseCase<void, String> {
  final LoginRepo loginRepo;

  ResendOtpUseCase({required this.loginRepo});

  @override
  Future<Either<Failure, void>> call(String phone) async {
    return await loginRepo.resendOTP(phone: phone);
  }
}
