import 'package:dio/dio.dart';
import 'package:gather_point/core/databases/api/end_points.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/feature/auth/Data/Models/user_model/user_model.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';

abstract class LoginRemoteDataSource {
  Future<void> login({required String phone});

  Future<UserEntity> validateOTP({required String phone, required String otp});

  Future<void> resendOTP({required String phone});

  Future<UserEntity> loginToken({required String accessToken});

  Future<UserEntity> loginGuest();

  Future<UserEntity> validateToken({required String token});
}

class LoginRemoteDataSourceImpl implements LoginRemoteDataSource {
  final DioConsumer apiConsumer;

  LoginRemoteDataSourceImpl({required this.apiConsumer});

  @override
  Future<void> login({required String phone}) async {
    await apiConsumer.post(
      EndPoints.login,
      data: {'phone': phone},
    );
  }

  @override
  Future<UserEntity> validateOTP(
      {required String phone, required String otp}) async {
    var data = await apiConsumer.post(
      EndPoints.validateOTP,
      data: {'phone': phone, 'otp': otp},
    );

    UserEntity user = UserModel.fromJson(data['data']);

    return user;
  }

  @override
  Future<void> resendOTP({required String phone}) async {
    await apiConsumer.post(
      EndPoints.resendOTP,
      data: {'phone': phone},
    );
  }

  @override
  Future<UserEntity> loginToken({required String accessToken}) async {
    var data = await apiConsumer.post(
      EndPoints.loginToken,
      data: {
        'login_token': accessToken,
        'login_method': '2',
      },
    );

    UserEntity user = UserModel.fromJson(data['data']);

    return user;
  }

  @override
  Future<UserEntity> loginGuest() async {
    var data = await apiConsumer.post(
      EndPoints.loginGuest,
    );

    UserEntity user = UserModel.fromJson(data['data']);

    return user;
  }

  @override
  Future<UserEntity> validateToken({required String token}) async {
    // Create a temporary Dio instance with the specific token for validation
    final tempDio = Dio();
    tempDio.options.baseUrl = EndPoints.baserUrl;
    tempDio.options.headers['Accept'] = 'application/json';
    tempDio.options.headers['Content-Type'] = 'application/json';
    tempDio.options.headers['Authorization'] = 'Bearer $token';

    // Call the validate token endpoint which now returns user data
    final response = await tempDio.post(EndPoints.validateToken);

    // Parse the response to get updated user data
    if (response.data != null &&
        response.data['code'] == 200 &&
        response.data['status'] == true) {
      final userData = response.data['data'];
      if (userData != null) {
        // Create UserEntity from the response data
        UserEntity user = UserModel.fromJson(userData);
        return user;
      }
    }

    // If we reach here but don't have proper response data, throw an error
    throw Exception('Invalid response from token validation: ${response.data}');
  }
}
