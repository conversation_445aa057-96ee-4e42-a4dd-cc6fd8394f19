import 'package:flutter/material.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:intl/intl.dart';

class ReservationConfirmationPage extends StatelessWidget {
  final Map<String, dynamic> reservationData;
  final DioConsumer dioConsumer;
  final int videoId;
  final String reservationFrom;
  final String reservationTo;

  const ReservationConfirmationPage({
    super.key,
    required this.reservationData,
    required this.dioConsumer,
    required this.videoId,
    required this.reservationFrom,
    required this.reservationTo,
  });

  String formatPrice(num price, BuildContext context) {
    final formatter = NumberFormat("#,##0.00", "ar");
    return S.of(context).priceWithCurrency(formatter.format(price));
  }

  String formatDate(String date, BuildContext context) {
    try {
      DateTime parsedDate = DateTime.parse(date);
      return DateFormat("EEEE, d MMMM yyyy", "ar").format(parsedDate);
    } catch (e) {
      return S.of(context).invalidDate;
    }
  }

  Future<void> _confirmReservation(BuildContext context) async {
    final s = S.of(context);
    try {
      await dioConsumer.post(
        '/api/reservations/create',
        data: {
          'service_category_item_id': videoId,
          'reservation_from': reservationFrom,
          'reservation_to': reservationTo,
        },
      );

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text("${s.reservationConfirmed} ✅")),
        );
        Navigator.popUntil(context, (route) => route.isFirst);
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text("${s.reservationFailed} ❌: ${e.toString()}")),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    final priceDetails = reservationData['price_details'] ?? {};
    final priceType = priceDetails['price_type'] ?? s.notAvailable;
    final dailyPrice = priceDetails['daily_price'] ?? 0;
    final weekendPrice = priceDetails['weekend_price'] ?? 0;
    final normalDays = priceDetails['normal_days'] ?? 0;
    final weekendDays = priceDetails['weekend_days'] ?? 0;

    return Scaffold(
      appBar: AppBar(
        title: Text("${s.confirmReservation} 🏷️"),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildCard(
              context,
              title: "📌 ${s.unitDetails}",
              children: [
                _buildListTile("🏠 ${s.unitName}:",
                    reservationData['unit_title'] ?? s.notAvailable, context),
                _buildListTile("📅 ${s.numberOfDays}:",
                    reservationData['total_days'].toString(), context),
                _buildListTile(
                    "📆 ${s.reservationFrom}:", formatDate(reservationFrom, context), context),
                _buildListTile(
                    "📆 ${s.reservationTo}:", formatDate(reservationTo, context), context),
              ],
            ),
            const SizedBox(height: 10),
            _buildCard(
              context,
              title: "💰 ${s.priceDetails}",
              children: [
                _buildListTile("💵 ${s.price}:",
                    formatPrice(reservationData['price'] ?? 0, context), context),
                _buildListTile("💸 ${s.commission}:",
                    formatPrice(reservationData['commission'] ?? 0, context), context),
                _buildListTile("💳 ${s.finalPrice}:",
                    formatPrice(reservationData['final_price'] ?? 0, context), context),
              ],
            ),
            const SizedBox(height: 10),
            _buildCard(
              context,
              title: "🔹 ${s.priceBredown}",
              children: [
                _buildListTile("▪️ ${s.priceType}:", priceType, context),
                if (priceType == 'daily') ...[
                  _buildListTile(
                      "▪️ ${s.dailyPrice}:", formatPrice(dailyPrice, context), context),
                  _buildListTile("▪️ ${s.weekendPrice}:",
                      formatPrice(weekendPrice, context), context),
                  _buildListTile(
                      "▪️ ${s.normalDays}:", "$normalDays ${s.days}", context),
                  _buildListTile(
                      "▪️ ${s.weekendDays}:", "$weekendDays ${s.days}", context),
                ],
              ],
            ),
            const SizedBox(height: 20),
            Center(
              child: SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: () => _confirmReservation(context),
                  icon: const Icon(Icons.check_circle),
                  label: Text(
                    "${s.confirmBooking} ✅",
                    style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 14),
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12)),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCard(BuildContext context,
      {required String title, required List<Widget> children}) {
    final theme = Theme.of(context);
    return Card(
      color: theme.cardColor,
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: theme.textTheme.titleLarge
                  ?.copyWith(fontWeight: FontWeight.bold),
            ),
            const Divider(thickness: 1.2),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildListTile(String title, String value, BuildContext context) {
    final theme = Theme.of(context);
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(title,
              style: theme.textTheme.bodyMedium
                  ?.copyWith(fontWeight: FontWeight.w500)),
          Text(value,
              style: theme.textTheme.bodyMedium
                  ?.copyWith(fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }
}
