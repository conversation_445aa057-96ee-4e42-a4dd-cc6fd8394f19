import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:table_calendar/table_calendar.dart';

class CalendarWidget extends StatefulWidget {
  final Function(DateTime? start, DateTime? end, double totalPrice)? onDateSelected;

  const CalendarWidget({super.key, this.onDateSelected});

  @override
  _CalendarWidgetState createState() => _CalendarWidgetState();
}

class _CalendarWidgetState extends State<CalendarWidget> {
  final CalendarFormat _calendarFormat = CalendarFormat.month;
  DateTime? _startDate;
  DateTime? _endDate;

  final Map<int, double> prices = {
    DateTime.thursday: 150.0, // سعر الخميس والجمعة
    DateTime.friday: 150.0,
  };
  final double normalPrice = 100.0; // السعر العادي

  void _onDaySelected(DateTime selectedDay, DateTime focusedDay) {
    setState(() {
      if (_startDate == null || (_startDate != null && _endDate != null)) {
        _startDate = selectedDay;
        _endDate = null;
      } else if (_endDate == null && selectedDay.isAfter(_startDate!)) {
        _endDate = selectedDay;
      } else {
        _startDate = selectedDay;
        _endDate = null;
      }

      double totalPrice = _calculateTotalPrice();
      if (widget.onDateSelected != null) {
        widget.onDateSelected!(_startDate, _endDate, totalPrice);
      }
    });
  }

  double _calculateTotalPrice() {
    if (_startDate == null || _endDate == null) return 0.0;

    double total = 0.0;
    DateTime current = _startDate!;
    while (!current.isAfter(_endDate!)) {
      total += prices[current.weekday] ?? normalPrice;
      current = current.add(const Duration(days: 1));
    }
    return total;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        TableCalendar(
          firstDay: DateTime.now(),
          lastDay: DateTime.utc(2030, 12, 31),
          focusedDay: DateTime.now(),
          calendarFormat: _calendarFormat,
          selectedDayPredicate: (day) =>
          (_startDate != null && isSameDay(_startDate, day)) ||
              (_endDate != null && isSameDay(_endDate, day)) ||
              (_startDate != null &&
                  _endDate != null &&
                  day.isAfter(_startDate!) &&
                  day.isBefore(_endDate!)),
          onDaySelected: _onDaySelected,
          calendarStyle: const CalendarStyle(
            todayDecoration: BoxDecoration(
              color: Colors.blue,
              shape: BoxShape.circle,
            ),
            selectedDecoration: BoxDecoration(
              color: Colors.green,
              shape: BoxShape.circle,
            ),
            weekendTextStyle: TextStyle(color: Colors.red),
          ),
          daysOfWeekStyle: const DaysOfWeekStyle(
            weekendStyle: TextStyle(color: Colors.red),
          ),
          headerStyle: const HeaderStyle(
            formatButtonVisible: false,
            titleCentered: true,
          ),
          calendarBuilders: CalendarBuilders(
            defaultBuilder: (context, date, _) {
              double price = prices[date.weekday] ?? normalPrice;
              bool isSelected =
                  (_startDate != null && isSameDay(_startDate, date)) ||
                      (_endDate != null && isSameDay(_endDate, date)) ||
                      (_startDate != null &&
                          _endDate != null &&
                          date.isAfter(_startDate!) &&
                          date.isBefore(_endDate!));

              return Column(
                children: [
                  Container(
                    decoration: BoxDecoration(
                      color: isSelected ? Colors.green.withOpacity(0.5) : null,
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: Text(
                        '${date.day}',
                        style: TextStyle(
                          color: isSelected ? Colors.white : AppColors.yellow,
                          fontWeight:
                          isSelected ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                    ),
                  ),
                  Text(
                    S.of(context).priceWithCurrency(price.toStringAsFixed(0)),
                    style: const TextStyle(fontSize: 10, color: Colors.blue),
                  ),
                ],
              );
            },
          ),
        ),
      ],
    );
  }
}
