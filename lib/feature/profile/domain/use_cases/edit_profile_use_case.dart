import 'package:dartz/dartz.dart';
import 'package:gather_point/core/errors/failure.dart';
import 'package:gather_point/core/use_cases/use_case.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:gather_point/feature/profile/domain/repositories/profile_repository.dart';

class EditProfileParams {
  final String? fullName;
  final String? email;
  final String? phone;
  final String? bio;
  final String? birthdate;
  final int? gender;
  final String? profilePicturePath;

  EditProfileParams({
    this.fullName,
    this.email,
    this.phone,
    this.bio,
    this.birthdate,
    this.gender,
    this.profilePicturePath,
  });
}

class EditProfileUseCase implements UseCase<UserEntity, EditProfileParams> {
  final ProfileRepository repository;

  EditProfileUseCase(this.repository);

  @override
  Future<Either<Failure, UserEntity>> call(EditProfileParams params) async {
    return await repository.editProfile(
      fullName: params.fullName,
      email: params.email,
      phone: params.phone,
      bio: params.bio,
      birthdate: params.birthdate,
      gender: params.gender,
      profilePicturePath: params.profilePicturePath,
    );
  }
}
