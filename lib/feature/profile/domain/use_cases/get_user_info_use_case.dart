import 'package:dartz/dartz.dart';
import 'package:gather_point/core/errors/failure.dart';
import 'package:gather_point/core/use_cases/use_case.dart';
import 'package:gather_point/core/use_cases/no_params.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:gather_point/feature/profile/domain/repositories/profile_repository.dart';

class GetUserInfoUseCase implements UseCase<UserEntity, NoParams> {
  final ProfileRepository repository;

  GetUserInfoUseCase(this.repository);

  @override
  Future<Either<Failure, UserEntity>> call(NoParams params) async {
    return await repository.getUserInfo();
  }
}
