import 'package:dartz/dartz.dart';
import 'package:gather_point/core/errors/failure.dart';
import 'package:gather_point/core/use_cases/use_case.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:gather_point/feature/profile/domain/repositories/profile_repository.dart';

class ToggleHosterModeParams {
  final bool isHosterMode;

  ToggleHosterModeParams({required this.isHosterMode});
}

class ToggleHosterModeUseCase implements UseCase<UserEntity, ToggleHosterModeParams> {
  final ProfileRepository repository;

  ToggleHosterModeUseCase(this.repository);

  @override
  Future<Either<Failure, UserEntity>> call(ToggleHosterModeParams params) async {
    return await repository.toggleHosterMode(isHosterMode: params.isHosterMode);
  }
}
