import 'package:dartz/dartz.dart';
import 'package:gather_point/core/errors/failure.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';

abstract class ProfileRepository {
  Future<Either<Failure, UserEntity>> getUserInfo();
  
  Future<Either<Failure, UserEntity>> editProfile({
    String? fullName,
    String? email,
    String? phone,
    String? bio,
    String? birthdate,
    int? gender,
    String? profilePicturePath,
  });
  
  Future<Either<Failure, void>> logout();
  
  Future<Either<Failure, UserEntity>> toggleHosterMode({
    required bool isHosterMode,
  });
  
  Future<Either<Failure, void>> deleteAccount();
  
  Future<Either<Failure, List<UserEntity>>> getFollowList();
  
  Future<Either<Failure, void>> followUser({required int userId});
  
  Future<Either<Failure, void>> unfollowUser({required int userId});
}
