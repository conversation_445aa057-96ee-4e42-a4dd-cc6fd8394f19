class LegalDocument {
  final int id;
  final String type;
  final String title;
  final String? content;
  final String version;
  final String effectiveDate;
  final String updatedAt;

  LegalDocument({
    required this.id,
    required this.type,
    required this.title,
    this.content,
    required this.version,
    required this.effectiveDate,
    required this.updatedAt,
  });

  factory LegalDocument.fromJson(Map<String, dynamic> json) {
    return LegalDocument(
      id: json['id'],
      type: json['type'],
      title: json['title'],
      content: json['content'],
      version: json['version'],
      effectiveDate: json['effective_date'],
      updatedAt: json['updated_at'],
    );
  }

  String get typeDisplay {
    switch (type) {
      case 'terms':
        return 'شروط الخدمة';
      case 'privacy':
        return 'سياسة الخصوصية';
      case 'cookies':
        return 'سياسة ملفات تعريف الارتباط';
      case 'user_agreement':
        return 'اتفاقية المستخدم';
      case 'host_agreement':
        return 'اتفاقية المضيف';
      default:
        return title;
    }
  }
}
