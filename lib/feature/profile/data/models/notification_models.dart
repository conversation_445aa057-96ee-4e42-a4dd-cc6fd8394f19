class NotificationModel {
  final int id;
  final String type;
  final String typeDisplay;
  final String typeIcon;
  final String title;
  final String message;
  final Map<String, dynamic>? data;
  final bool isRead;
  final String createdAt;
  final String? readAt;

  NotificationModel({
    required this.id,
    required this.type,
    required this.typeDisplay,
    required this.typeIcon,
    required this.title,
    required this.message,
    this.data,
    required this.isRead,
    required this.createdAt,
    this.readAt,
  });

  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    return NotificationModel(
      id: json['id'],
      type: json['type'],
      typeDisplay: json['type_display'],
      typeIcon: json['type_icon'],
      title: json['title'],
      message: json['message'],
      data: json['data'],
      isRead: json['is_read'],
      createdAt: json['created_at'],
      readAt: json['read_at'],
    );
  }
}

class NotificationPreference {
  final String type;
  final String typeDisplay;
  final bool pushEnabled;
  final bool emailEnabled;
  final bool smsEnabled;

  NotificationPreference({
    required this.type,
    required this.typeDisplay,
    required this.pushEnabled,
    required this.emailEnabled,
    required this.smsEnabled,
  });

  factory NotificationPreference.fromJson(Map<String, dynamic> json) {
    return NotificationPreference(
      type: json['type'],
      typeDisplay: json['type_display'],
      pushEnabled: json['push_enabled'],
      emailEnabled: json['email_enabled'],
      smsEnabled: json['sms_enabled'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'push_enabled': pushEnabled,
      'email_enabled': emailEnabled,
      'sms_enabled': smsEnabled,
    };
  }

  NotificationPreference copyWith({
    bool? pushEnabled,
    bool? emailEnabled,
    bool? smsEnabled,
  }) {
    return NotificationPreference(
      type: type,
      typeDisplay: typeDisplay,
      pushEnabled: pushEnabled ?? this.pushEnabled,
      emailEnabled: emailEnabled ?? this.emailEnabled,
      smsEnabled: smsEnabled ?? this.smsEnabled,
    );
  }
}

class NotificationStats {
  final int totalNotifications;
  final int unreadNotifications;
  final Map<String, int> notificationsByType;
  final List<RecentNotification> recentActivity;

  NotificationStats({
    required this.totalNotifications,
    required this.unreadNotifications,
    required this.notificationsByType,
    required this.recentActivity,
  });

  factory NotificationStats.fromJson(Map<String, dynamic> json) {
    return NotificationStats(
      totalNotifications: json['total_notifications'],
      unreadNotifications: json['unread_notifications'],
      notificationsByType: Map<String, int>.from(json['notifications_by_type']),
      recentActivity: (json['recent_activity'] as List)
          .map((activity) => RecentNotification.fromJson(activity))
          .toList(),
    );
  }
}

class RecentNotification {
  final String type;
  final String title;
  final String createdAt;

  RecentNotification({
    required this.type,
    required this.title,
    required this.createdAt,
  });

  factory RecentNotification.fromJson(Map<String, dynamic> json) {
    return RecentNotification(
      type: json['type'],
      title: json['title'],
      createdAt: json['created_at'],
    );
  }
}

class NotificationPagination {
  final int currentPage;
  final int lastPage;
  final int perPage;
  final int total;

  NotificationPagination({
    required this.currentPage,
    required this.lastPage,
    required this.perPage,
    required this.total,
  });

  factory NotificationPagination.fromJson(Map<String, dynamic> json) {
    return NotificationPagination(
      currentPage: json['current_page'],
      lastPage: json['last_page'],
      perPage: json['per_page'],
      total: json['total'],
    );
  }
}

class NotificationResponse {
  final List<NotificationModel> notifications;
  final NotificationPagination pagination;
  final int unreadCount;

  NotificationResponse({
    required this.notifications,
    required this.pagination,
    required this.unreadCount,
  });

  factory NotificationResponse.fromJson(Map<String, dynamic> json) {
    return NotificationResponse(
      notifications: (json['data'] as List)
          .map((notification) => NotificationModel.fromJson(notification))
          .toList(),
      pagination: NotificationPagination.fromJson(json['pagination']),
      unreadCount: json['unread_count'],
    );
  }
}
