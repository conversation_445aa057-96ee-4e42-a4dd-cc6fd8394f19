class SupportFaq {
  final int id;
  final String question;
  final String answer;
  final String category;

  SupportFaq({
    required this.id,
    required this.question,
    required this.answer,
    required this.category,
  });

  factory SupportFaq.fromJson(Map<String, dynamic> json) {
    return SupportFaq(
      id: json['id'],
      question: json['question'],
      answer: json['answer'],
      category: json['category'],
    );
  }
}

class SupportTicket {
  final int id;
  final String subject;
  final String description;
  final String category;
  final String categoryDisplay;
  final String priority;
  final String priorityDisplay;
  final String status;
  final String statusDisplay;
  final String createdAt;
  final String? resolvedAt;

  SupportTicket({
    required this.id,
    required this.subject,
    required this.description,
    required this.category,
    required this.categoryDisplay,
    required this.priority,
    required this.priorityDisplay,
    required this.status,
    required this.statusDisplay,
    required this.createdAt,
    this.resolvedAt,
  });

  factory SupportTicket.fromJson(Map<String, dynamic> json) {
    return SupportTicket(
      id: json['id'] ?? 0,
      subject: json['subject'] ?? '',
      description: json['description'] ?? '',
      category: json['category'] ?? '',
      categoryDisplay: json['category_display'] ?? '',
      priority: json['priority'] ?? '',
      priorityDisplay: json['priority_display'] ?? '',
      status: json['status'] ?? '',
      statusDisplay: json['status_display'] ?? '',
      createdAt: json['created_at'] ?? '',
      resolvedAt: json['resolved_at'],
    );
  }
}

class SupportCategory {
  final String value;
  final String label;

  SupportCategory({
    required this.value,
    required this.label,
  });

  factory SupportCategory.fromJson(Map<String, dynamic> json) {
    return SupportCategory(
      value: json['value'] ?? '',
      label: json['label'] ?? '',
    );
  }
}
