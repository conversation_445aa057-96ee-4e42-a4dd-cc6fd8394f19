class CoHostModel {
  final int id;
  final CoHostUserModel coHost;
  final CoHostPropertyModel? property;
  final List<String> permissions;
  final double commissionPercentage;
  final String acceptedAt;

  CoHostModel({
    required this.id,
    required this.coHost,
    this.property,
    required this.permissions,
    required this.commissionPercentage,
    required this.acceptedAt,
  });

  factory CoHostModel.fromJson(Map<String, dynamic> json) {
    return CoHostModel(
      id: json['id'] ?? 0,
      coHost: CoHostUserModel.fromJson(json['co_host'] ?? {}),
      property: json['property'] != null ? CoHostPropertyModel.fromJson(json['property']) : null,
      permissions: List<String>.from(json['permissions'] ?? []),
      commissionPercentage: (json['commission_percentage'] ?? 0).toDouble(),
      acceptedAt: json['accepted_at'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'co_host': coHost.toJson(),
      'property': property?.toJson(),
      'permissions': permissions,
      'commission_percentage': commissionPercentage,
      'accepted_at': acceptedAt,
    };
  }
}

class CoHostInvitationModel {
  final int id;
  final CoHostUserModel host;
  final CoHostPropertyModel? property;
  final List<String> permissions;
  final double commissionPercentage;
  final String? message;
  final String createdAt;

  CoHostInvitationModel({
    required this.id,
    required this.host,
    this.property,
    required this.permissions,
    required this.commissionPercentage,
    this.message,
    required this.createdAt,
  });

  factory CoHostInvitationModel.fromJson(Map<String, dynamic> json) {
    return CoHostInvitationModel(
      id: json['id'] ?? 0,
      host: CoHostUserModel.fromJson(json['host'] ?? {}),
      property: json['property'] != null ? CoHostPropertyModel.fromJson(json['property']) : null,
      permissions: List<String>.from(json['permissions'] ?? []),
      commissionPercentage: (json['commission_percentage'] ?? 0).toDouble(),
      message: json['message'],
      createdAt: json['created_at'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'host': host.toJson(),
      'property': property?.toJson(),
      'permissions': permissions,
      'commission_percentage': commissionPercentage,
      'message': message,
      'created_at': createdAt,
    };
  }
}

class CoHostUserModel {
  final int id;
  final String fullName;
  final String email;
  final String phone;
  final String? profilePhotoUrl;

  CoHostUserModel({
    required this.id,
    required this.fullName,
    required this.email,
    required this.phone,
    this.profilePhotoUrl,
  });

  factory CoHostUserModel.fromJson(Map<String, dynamic> json) {
    return CoHostUserModel(
      id: json['id'] ?? 0,
      fullName: json['full_name'] ?? '',
      email: json['email'] ?? '',
      phone: json['phone'] ?? '',
      profilePhotoUrl: json['profile_photo_url'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'full_name': fullName,
      'email': email,
      'phone': phone,
      'profile_photo_url': profilePhotoUrl,
    };
  }
}

class CoHostPropertyModel {
  final int id;
  final String title;
  final String? image;

  CoHostPropertyModel({
    required this.id,
    required this.title,
    this.image,
  });

  factory CoHostPropertyModel.fromJson(Map<String, dynamic> json) {
    return CoHostPropertyModel(
      id: json['id'] ?? 0,
      title: json['title'] ?? '',
      image: json['image'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'image': image,
    };
  }
}

class SearchCoHostModel {
  final int id;
  final String fullName;
  final String email;
  final String phone;
  final String? profilePhotoUrl;
  final String invitationStatus; // 'none', 'pending', 'accepted', 'declined'
  final int propertiesCount;

  SearchCoHostModel({
    required this.id,
    required this.fullName,
    required this.email,
    required this.phone,
    this.profilePhotoUrl,
    required this.invitationStatus,
    required this.propertiesCount,
  });

  factory SearchCoHostModel.fromJson(Map<String, dynamic> json) {
    return SearchCoHostModel(
      id: json['id'] ?? 0,
      fullName: json['full_name'] ?? '',
      email: json['email'] ?? '',
      phone: json['phone'] ?? '',
      profilePhotoUrl: json['profile_photo_url'],
      invitationStatus: json['invitation_status'] ?? 'none',
      propertiesCount: json['properties_count'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'full_name': fullName,
      'email': email,
      'phone': phone,
      'profile_photo_url': profilePhotoUrl,
      'invitation_status': invitationStatus,
      'properties_count': propertiesCount,
    };
  }

  bool get canInvite => invitationStatus == 'none';
  bool get hasPendingInvitation => invitationStatus == 'pending';
  bool get isAccepted => invitationStatus == 'accepted';
  bool get isDeclined => invitationStatus == 'declined';
}

class CoHostPermissionModel {
  final String key;
  final String name;

  CoHostPermissionModel({
    required this.key,
    required this.name,
  });

  factory CoHostPermissionModel.fromJson(String key, String name) {
    return CoHostPermissionModel(
      key: key,
      name: name,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'key': key,
      'name': name,
    };
  }
}
