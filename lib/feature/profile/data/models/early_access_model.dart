class EarlyAccessFeatureModel {
  final int id;
  final String name;
  final String description;
  final String? icon;
  final String status;
  final String statusDisplay;
  final String statusColor;
  final int progressPercentage;
  final String? estimatedReleaseDate;
  final bool isPremium;
  final int enrollmentCount;
  final int waitlistCount;
  final double averageRating;
  final UserEarlyAccessEnrollmentModel? userEnrollment;
  final bool canEnroll;

  EarlyAccessFeatureModel({
    required this.id,
    required this.name,
    required this.description,
    this.icon,
    required this.status,
    required this.statusDisplay,
    required this.statusColor,
    required this.progressPercentage,
    this.estimatedReleaseDate,
    required this.isPremium,
    required this.enrollmentCount,
    required this.waitlistCount,
    required this.averageRating,
    this.userEnrollment,
    required this.canEnroll,
  });

  factory EarlyAccessFeatureModel.fromJson(Map<String, dynamic> json) {
    return EarlyAccessFeatureModel(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      icon: json['icon'],
      status: json['status'] ?? '',
      statusDisplay: json['status_display'] ?? '',
      statusColor: json['status_color'] ?? '#8E8E93',
      progressPercentage: json['progress_percentage'] ?? 0,
      estimatedReleaseDate: json['estimated_release_date'],
      isPremium: json['is_premium'] ?? false,
      enrollmentCount: json['enrollment_count'] ?? 0,
      waitlistCount: json['waitlist_count'] ?? 0,
      averageRating: (json['average_rating'] ?? 0).toDouble(),
      userEnrollment: json['user_enrollment'] != null 
          ? UserEarlyAccessEnrollmentModel.fromJson(json['user_enrollment'])
          : null,
      canEnroll: json['can_enroll'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'icon': icon,
      'status': status,
      'status_display': statusDisplay,
      'status_color': statusColor,
      'progress_percentage': progressPercentage,
      'estimated_release_date': estimatedReleaseDate,
      'is_premium': isPremium,
      'enrollment_count': enrollmentCount,
      'waitlist_count': waitlistCount,
      'average_rating': averageRating,
      'user_enrollment': userEnrollment?.toJson(),
      'can_enroll': canEnroll,
    };
  }

  bool get isBeta => status == 'beta';
  bool get isComingSoon => status == 'coming_soon';
  bool get isInDevelopment => status == 'development';
  bool get isReleased => status == 'released';
  bool get isEnrolled => userEnrollment != null && userEnrollment!.status == 'enrolled';
}

class UserEarlyAccessEnrollmentModel {
  final String status;
  final String? enrolledAt;
  final String? lastUsedAt;
  final int? rating;
  final String? feedback;

  UserEarlyAccessEnrollmentModel({
    required this.status,
    this.enrolledAt,
    this.lastUsedAt,
    this.rating,
    this.feedback,
  });

  factory UserEarlyAccessEnrollmentModel.fromJson(Map<String, dynamic> json) {
    return UserEarlyAccessEnrollmentModel(
      status: json['status'] ?? '',
      enrolledAt: json['enrolled_at'],
      lastUsedAt: json['last_used_at'],
      rating: json['rating'],
      feedback: json['feedback'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'enrolled_at': enrolledAt,
      'last_used_at': lastUsedAt,
      'rating': rating,
      'feedback': feedback,
    };
  }

  bool get isEnrolled => status == 'enrolled';
  bool get isOnWaitlist => status == 'waitlist';
  bool get isDeclined => status == 'declined';
  bool get isRemoved => status == 'removed';
}

class MyEarlyAccessFeatureModel {
  final int id;
  final String name;
  final String description;
  final String? icon;
  final String status;
  final String statusDisplay;
  final int progressPercentage;
  final String? enrolledAt;
  final String? lastUsedAt;
  final int? rating;
  final String? feedback;

  MyEarlyAccessFeatureModel({
    required this.id,
    required this.name,
    required this.description,
    this.icon,
    required this.status,
    required this.statusDisplay,
    required this.progressPercentage,
    this.enrolledAt,
    this.lastUsedAt,
    this.rating,
    this.feedback,
  });

  factory MyEarlyAccessFeatureModel.fromJson(Map<String, dynamic> json) {
    return MyEarlyAccessFeatureModel(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      icon: json['icon'],
      status: json['status'] ?? '',
      statusDisplay: json['status_display'] ?? '',
      progressPercentage: json['progress_percentage'] ?? 0,
      enrolledAt: json['enrolled_at'],
      lastUsedAt: json['last_used_at'],
      rating: json['rating'],
      feedback: json['feedback'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'icon': icon,
      'status': status,
      'status_display': statusDisplay,
      'progress_percentage': progressPercentage,
      'enrolled_at': enrolledAt,
      'last_used_at': lastUsedAt,
      'rating': rating,
      'feedback': feedback,
    };
  }

  bool get hasRating => rating != null && rating! > 0;
  bool get hasFeedback => feedback != null && feedback!.isNotEmpty;
}

class EarlyAccessStatsModel {
  final int totalEnrollments;
  final int waitlistCount;
  final double averageRating;
  final int feedbackCount;
  final int activeUsersLastWeek;

  EarlyAccessStatsModel({
    required this.totalEnrollments,
    required this.waitlistCount,
    required this.averageRating,
    required this.feedbackCount,
    required this.activeUsersLastWeek,
  });

  factory EarlyAccessStatsModel.fromJson(Map<String, dynamic> json) {
    return EarlyAccessStatsModel(
      totalEnrollments: json['total_enrollments'] ?? 0,
      waitlistCount: json['waitlist_count'] ?? 0,
      averageRating: (json['average_rating'] ?? 0).toDouble(),
      feedbackCount: json['feedback_count'] ?? 0,
      activeUsersLastWeek: json['active_users_last_week'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total_enrollments': totalEnrollments,
      'waitlist_count': waitlistCount,
      'average_rating': averageRating,
      'feedback_count': feedbackCount,
      'active_users_last_week': activeUsersLastWeek,
    };
  }
}
