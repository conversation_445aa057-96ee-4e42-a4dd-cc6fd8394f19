class FriendModel {
  final int id;
  final String fullName;
  final String email;
  final String phone;
  final String? profilePhotoUrl;
  final bool isHoster;
  final String friendsSince;
  final int mutualFriendsCount;

  FriendModel({
    required this.id,
    required this.fullName,
    required this.email,
    required this.phone,
    this.profilePhotoUrl,
    required this.isHoster,
    required this.friendsSince,
    required this.mutualFriendsCount,
  });

  factory FriendModel.fromJson(Map<String, dynamic> json) {
    return FriendModel(
      id: json['id'] ?? 0,
      fullName: json['full_name'] ?? '',
      email: json['email'] ?? '',
      phone: json['phone'] ?? '',
      profilePhotoUrl: json['profile_photo_url'],
      isHoster: json['is_hoster'] ?? false,
      friendsSince: json['friends_since'] ?? '',
      mutualFriendsCount: json['mutual_friends_count'] ?? 0,
    );
  }

  Map<String, dynamic> to<PERSON>son() {
    return {
      'id': id,
      'full_name': fullName,
      'email': email,
      'phone': phone,
      'profile_photo_url': profilePhotoUrl,
      'is_hoster': isHoster,
      'friends_since': friendsSince,
      'mutual_friends_count': mutualFriendsCount,
    };
  }
}

class FriendRequestModel {
  final int friendshipId;
  final UserModel user;
  final String requestedAt;
  final int mutualFriendsCount;

  FriendRequestModel({
    required this.friendshipId,
    required this.user,
    required this.requestedAt,
    required this.mutualFriendsCount,
  });

  factory FriendRequestModel.fromJson(Map<String, dynamic> json) {
    return FriendRequestModel(
      friendshipId: json['friendship_id'] ?? 0,
      user: UserModel.fromJson(json['user'] ?? {}),
      requestedAt: json['requested_at'] ?? '',
      mutualFriendsCount: json['mutual_friends_count'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'friendship_id': friendshipId,
      'user': user.toJson(),
      'requested_at': requestedAt,
      'mutual_friends_count': mutualFriendsCount,
    };
  }
}

class UserModel {
  final int id;
  final String fullName;
  final String email;
  final String phone;
  final String? profilePhotoUrl;
  final bool isHoster;

  UserModel({
    required this.id,
    required this.fullName,
    required this.email,
    required this.phone,
    this.profilePhotoUrl,
    required this.isHoster,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] ?? 0,
      fullName: json['full_name'] ?? '',
      email: json['email'] ?? '',
      phone: json['phone'] ?? '',
      profilePhotoUrl: json['profile_photo_url'],
      isHoster: json['is_hoster'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'full_name': fullName,
      'email': email,
      'phone': phone,
      'profile_photo_url': profilePhotoUrl,
      'is_hoster': isHoster,
    };
  }
}

class SearchUserModel {
  final int id;
  final String fullName;
  final String email;
  final String phone;
  final String? profilePhotoUrl;
  final bool isHoster;
  final String friendshipStatus; // 'none', 'friends', 'request_sent', 'request_received'
  final int mutualFriendsCount;

  SearchUserModel({
    required this.id,
    required this.fullName,
    required this.email,
    required this.phone,
    this.profilePhotoUrl,
    required this.isHoster,
    required this.friendshipStatus,
    required this.mutualFriendsCount,
  });

  factory SearchUserModel.fromJson(Map<String, dynamic> json) {
    return SearchUserModel(
      id: json['id'] ?? 0,
      fullName: json['full_name'] ?? '',
      email: json['email'] ?? '',
      phone: json['phone'] ?? '',
      profilePhotoUrl: json['profile_photo_url'],
      isHoster: json['is_hoster'] ?? false,
      friendshipStatus: json['friendship_status'] ?? 'none',
      mutualFriendsCount: json['mutual_friends_count'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'full_name': fullName,
      'email': email,
      'phone': phone,
      'profile_photo_url': profilePhotoUrl,
      'is_hoster': isHoster,
      'friendship_status': friendshipStatus,
      'mutual_friends_count': mutualFriendsCount,
    };
  }

  bool get isFriend => friendshipStatus == 'friends';
  bool get hasRequestSent => friendshipStatus == 'request_sent';
  bool get hasRequestReceived => friendshipStatus == 'request_received';
  bool get canSendRequest => friendshipStatus == 'none';
}
