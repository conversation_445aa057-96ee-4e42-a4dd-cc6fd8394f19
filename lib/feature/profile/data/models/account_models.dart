import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:gather_point/feature/auth/Data/Models/user_model/user_model.dart';

class AccountInfo {
  final UserEntity user;
  final AccountStats stats;

  AccountInfo({
    required this.user,
    required this.stats,
  });

  factory AccountInfo.fromJson(Map<String, dynamic> json) {
    return AccountInfo(
      user: UserModel.fromJson(json['user']),
      stats: AccountStats.fromJson(json['account_stats']),
    );
  }
}

class AccountStats {
  final String memberSince;
  final String lastLogin;
  final int totalBookings;
  final int totalReviews;
  final bool isVerified;

  AccountStats({
    required this.memberSince,
    required this.lastLogin,
    required this.totalBookings,
    required this.totalReviews,
    required this.isVerified,
  });

  factory AccountStats.fromJson(Map<String, dynamic> json) {
    return AccountStats(
      memberSince: json['member_since'],
      lastLogin: json['last_login'],
      totalBookings: json['total_bookings'],
      totalReviews: json['total_reviews'],
      isVerified: json['is_verified'],
    );
  }
}

class SecuritySettings {
  final bool twoFactorEnabled;
  final List<LoginSession> loginSessions;
  final List<RecentActivity> recentActivities;

  SecuritySettings({
    required this.twoFactorEnabled,
    required this.loginSessions,
    required this.recentActivities,
  });

  factory SecuritySettings.fromJson(Map<String, dynamic> json) {
    return SecuritySettings(
      twoFactorEnabled: json['two_factor_enabled'],
      loginSessions: (json['login_sessions'] as List)
          .map((session) => LoginSession.fromJson(session))
          .toList(),
      recentActivities: (json['recent_activities'] as List)
          .map((activity) => RecentActivity.fromJson(activity))
          .toList(),
    );
  }
}

class LoginSession {
  final String device;
  final String location;
  final String lastActive;
  final bool isCurrent;

  LoginSession({
    required this.device,
    required this.location,
    required this.lastActive,
    required this.isCurrent,
  });

  factory LoginSession.fromJson(Map<String, dynamic> json) {
    return LoginSession(
      device: json['device'],
      location: json['location'],
      lastActive: json['last_active'],
      isCurrent: json['is_current'],
    );
  }
}

class RecentActivity {
  final String action;
  final String timestamp;
  final String device;

  RecentActivity({
    required this.action,
    required this.timestamp,
    required this.device,
  });

  factory RecentActivity.fromJson(Map<String, dynamic> json) {
    return RecentActivity(
      action: json['action'],
      timestamp: json['timestamp'],
      device: json['device'],
    );
  }
}

class AccountExportData {
  final PersonalInfo personalInfo;
  final List<BookingData> bookings;
  final List<ReviewData> reviews;

  AccountExportData({
    required this.personalInfo,
    required this.bookings,
    required this.reviews,
  });

  factory AccountExportData.fromJson(Map<String, dynamic> json) {
    return AccountExportData(
      personalInfo: PersonalInfo.fromJson(json['personal_info']),
      bookings: (json['bookings'] as List)
          .map((booking) => BookingData.fromJson(booking))
          .toList(),
      reviews: (json['reviews'] as List)
          .map((review) => ReviewData.fromJson(review))
          .toList(),
    );
  }
}

class PersonalInfo {
  final String? fullName;
  final String? email;
  final String? phone;
  final String? birthdate;
  final int? gender;
  final String? bio;
  final String memberSince;

  PersonalInfo({
    this.fullName,
    this.email,
    this.phone,
    this.birthdate,
    this.gender,
    this.bio,
    required this.memberSince,
  });

  factory PersonalInfo.fromJson(Map<String, dynamic> json) {
    return PersonalInfo(
      fullName: json['full_name'],
      email: json['email'],
      phone: json['phone'],
      birthdate: json['birthdate'],
      gender: json['gender'],
      bio: json['bio'],
      memberSince: json['member_since'],
    );
  }
}

class BookingData {
  final String propertyName;
  final String checkIn;
  final String checkOut;
  final double totalPrice;
  final String status;
  final String createdAt;

  BookingData({
    required this.propertyName,
    required this.checkIn,
    required this.checkOut,
    required this.totalPrice,
    required this.status,
    required this.createdAt,
  });

  factory BookingData.fromJson(Map<String, dynamic> json) {
    return BookingData(
      propertyName: json['property_name'],
      checkIn: json['check_in'],
      checkOut: json['check_out'],
      totalPrice: json['total_price'].toDouble(),
      status: json['status'],
      createdAt: json['created_at'],
    );
  }
}

class ReviewData {
  final String propertyName;
  final int rating;
  final String comment;
  final String createdAt;

  ReviewData({
    required this.propertyName,
    required this.rating,
    required this.comment,
    required this.createdAt,
  });

  factory ReviewData.fromJson(Map<String, dynamic> json) {
    return ReviewData(
      propertyName: json['property_name'],
      rating: json['rating'],
      comment: json['comment'],
      createdAt: json['created_at'],
    );
  }
}
