import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/feature/profile/data/models/co_host_model.dart';

class CoHostApiService {
  final DioConsumer _dioConsumer;

  CoHostApiService(this._dioConsumer);
  /// Get user's co-hosts
  Future<List<CoHostModel>> getCoHosts() async {
    try {
      final response = await _dioConsumer.get('/api/co-hosts');

      if (response['success'] == true) {
        final List<dynamic> coHostsJson = response['data'] ?? [];
        return coHostsJson.map((json) => CoHostModel.fromJson(json)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to get co-hosts');
      }
    } catch (e) {
      throw Exception('Failed to get co-hosts: $e');
    }
  }

  /// Get pending co-host invitations (received)
  Future<List<CoHostInvitationModel>> getPendingInvitations() async {
    try {
      final response = await _dioConsumer.get('/api/co-hosts/pending-invitations');

      if (response['success'] == true) {
        final List<dynamic> invitationsJson = response['data'] ?? [];
        return invitationsJson.map((json) => CoHostInvitationModel.fromJson(json)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to get pending invitations');
      }
    } catch (e) {
      throw Exception('Failed to get pending invitations: $e');
    }
  }

  /// Send co-host invitation
  Future<bool> sendInvitation({
    required int coHostId,
    int? propertyId,
    List<String>? permissions,
    double? commissionPercentage,
    String? message,
  }) async {
    try {
      final requestData = {
        'co_host_id': coHostId,
        if (propertyId != null) 'property_id': propertyId,
        if (permissions != null) 'permissions': permissions,
        if (commissionPercentage != null) 'commission_percentage': commissionPercentage,
        if (message != null) 'message': message,
      };

      final response = await _dioConsumer.post('/api/co-hosts/send-invitation', data: requestData);

      return response['success'] == true;
    } catch (e) {
      throw Exception('Failed to send co-host invitation: $e');
    }
  }

  /// Accept co-host invitation
  Future<bool> acceptInvitation(int invitationId) async {
    try {
      final response = await _dioConsumer.post('/api/co-hosts/accept-invitation', data: {
        'invitation_id': invitationId,
      });

      return response['success'] == true;
    } catch (e) {
      throw Exception('Failed to accept co-host invitation: $e');
    }
  }

  /// Decline co-host invitation
  Future<bool> declineInvitation(int invitationId) async {
    try {
      final response = await _dioConsumer.post('/api/co-hosts/decline-invitation', data: {
        'invitation_id': invitationId,
      });

      return response['success'] == true;
    } catch (e) {
      throw Exception('Failed to decline co-host invitation: $e');
    }
  }

  /// Remove co-host
  Future<bool> removeCoHost(int coHostId) async {
    try {
      final response = await _dioConsumer.delete('/api/co-hosts/remove', data: {
        'co_host_id': coHostId,
      });

      return response['success'] == true;
    } catch (e) {
      throw Exception('Failed to remove co-host: $e');
    }
  }

  /// Search for potential co-hosts
  Future<List<SearchCoHostModel>> searchCoHosts(String query) async {
    try {
      final response = await _dioConsumer.get(
        '/api/co-hosts/search',
        queryParameters: {'query': query},
      );

      if (response['success'] == true) {
        final List<dynamic> usersJson = response['data'] ?? [];
        return usersJson.map((json) => SearchCoHostModel.fromJson(json)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to search co-hosts');
      }
    } catch (e) {
      throw Exception('Failed to search co-hosts: $e');
    }
  }

  /// Get available permissions
  Future<List<CoHostPermissionModel>> getPermissions() async {
    try {
      final response = await _dioConsumer.get('/api/co-hosts/permissions');

      if (response['success'] == true) {
        final Map<String, dynamic> permissionsJson = response['data'] ?? {};
        return permissionsJson.entries
            .map((entry) => CoHostPermissionModel.fromJson(entry.key, entry.value))
            .toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to get permissions');
      }
    } catch (e) {
      throw Exception('Failed to get permissions: $e');
    }
  }
}
