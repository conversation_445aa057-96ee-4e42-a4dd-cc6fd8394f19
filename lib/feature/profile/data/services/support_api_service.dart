import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/feature/profile/data/models/support_models.dart';

class SupportApiService {
  final DioConsumer _dioConsumer;

  SupportApiService(this._dioConsumer);
  
  /// Get all FAQs
  Future<List<SupportFaq>> getFaqs({String? category}) async {
    try {
      Map<String, dynamic>? queryParameters;
      if (category != null) {
        queryParameters = {'category': category};
      }

      final response = await _dioConsumer.get(
        '/api/support/faqs',
        queryParameters: queryParameters,
      );

      if (response['success'] == true) {
        final List<dynamic> faqsJson = response['data'] ?? [];
        return faqsJson.map((json) => SupportFaq.fromJson(json)).toList();
      } else {
        throw Exception(response['message'] ?? 'فشل في جلب الأسئلة الشائعة');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: ${e.toString()}');
    }
  }

  /// Get user's support tickets
  Future<List<SupportTicket>> getTickets() async {
    try {
      final response = await _dioConsumer.get('/api/support/tickets');

      if (response['success'] == true) {
        final List<dynamic> ticketsJson = response['data'] ?? [];
        return ticketsJson.map((json) => SupportTicket.fromJson(json)).toList();
      } else {
        throw Exception(response['message'] ?? 'فشل في جلب التذاكر');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: ${e.toString()}');
    }
  }

  /// Create a new support ticket
  Future<SupportTicket> createTicket({
    required String subject,
    required String description,
    required String category,
    String priority = 'medium',
  }) async {
    try {
      final requestData = {
        'subject': subject,
        'description': description,
        'category': category,
        'priority': priority,
      };

      final response = await _dioConsumer.post('/api/support/tickets', data: requestData);

      if (response['success'] == true) {
        return SupportTicket.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'فشل في إنشاء التذكرة');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: ${e.toString()}');
    }
  }

  /// Get support categories
  Future<List<SupportCategory>> getCategories() async {
    try {
      final response = await _dioConsumer.get('/api/support/categories');

      if (response['success'] == true) {
        final List<dynamic> categoriesJson = response['data'] ?? [];
        return categoriesJson.map((json) => SupportCategory.fromJson(json)).toList();
      } else {
        throw Exception(response['message'] ?? 'فشل في جلب الفئات');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: ${e.toString()}');
    }
  }
}
