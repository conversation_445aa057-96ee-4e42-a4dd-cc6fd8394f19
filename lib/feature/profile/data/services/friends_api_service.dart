import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/feature/profile/data/models/friend_model.dart';

class FriendsApiService {
  final DioConsumer _dioConsumer;

  FriendsApiService(this._dioConsumer);
  /// Get user's friends list
  Future<List<FriendModel>> getFriends() async {
    try {
      final response = await _dioConsumer.get('/api/friends');

      if (response['success'] == true) {
        final List<dynamic> friendsJson = response['data'] ?? [];
        return friendsJson.map((json) => FriendModel.fromJson(json)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to get friends');
      }
    } catch (e) {
      throw Exception('Failed to get friends: $e');
    }
  }

  /// Get pending friend requests (received)
  Future<List<FriendRequestModel>> getPendingRequests() async {
    try {
      final response = await _dioConsumer.get('/api/friends/pending-requests');

      if (response['success'] == true) {
        final List<dynamic> requestsJson = response['data'] ?? [];
        return requestsJson.map((json) => FriendRequestModel.fromJson(json)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to get pending requests');
      }
    } catch (e) {
      throw Exception('Failed to get pending requests: $e');
    }
  }

  /// Send friend request
  Future<bool> sendFriendRequest(int friendId) async {
    try {
      final response = await _dioConsumer.post('/api/friends/send-request', data: {
        'friend_id': friendId,
      });

      return response['success'] == true;
    } catch (e) {
      throw Exception('Failed to send friend request: $e');
    }
  }

  /// Accept friend request
  Future<bool> acceptRequest(int friendshipId) async {
    try {
      final response = await _dioConsumer.post('/api/friends/accept-request', data: {
        'friendship_id': friendshipId,
      });

      return response['success'] == true;
    } catch (e) {
      throw Exception('Failed to accept friend request: $e');
    }
  }

  /// Decline friend request
  Future<bool> declineRequest(int friendshipId) async {
    try {
      final response = await _dioConsumer.post('/api/friends/decline-request', data: {
        'friendship_id': friendshipId,
      });

      return response['success'] == true;
    } catch (e) {
      throw Exception('Failed to decline friend request: $e');
    }
  }

  /// Remove friend
  Future<bool> removeFriend(int friendId) async {
    try {
      final response = await _dioConsumer.delete('/api/friends/remove', data: {
        'friend_id': friendId,
      });

      return response['success'] == true;
    } catch (e) {
      throw Exception('Failed to remove friend: $e');
    }
  }

  /// Search for users to add as friends
  Future<List<SearchUserModel>> searchUsers(String query) async {
    try {
      final response = await _dioConsumer.get(
        '/api/friends/search',
        queryParameters: {'query': query},
      );

      if (response['success'] == true) {
        final List<dynamic> usersJson = response['data'] ?? [];
        return usersJson.map((json) => SearchUserModel.fromJson(json)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to search users');
      }
    } catch (e) {
      throw Exception('Failed to search users: $e');
    }
  }
}
