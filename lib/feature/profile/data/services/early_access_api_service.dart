import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/feature/profile/data/models/early_access_model.dart';

class EarlyAccessApiService {
  final DioConsumer _dioConsumer;

  EarlyAccessApiService(this._dioConsumer);
  /// Get all available early access features
  Future<List<EarlyAccessFeatureModel>> getAllFeatures() async {
    try {
      final response = await _dioConsumer.get('/api/early-access');

      if (response['success'] == true) {
        final List<dynamic> featuresJson = response['data'] ?? [];
        return featuresJson.map((json) => EarlyAccessFeatureModel.fromJson(json)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to get early access features');
      }
    } catch (e) {
      throw Exception('Failed to get early access features: $e');
    }
  }

  /// Get user's enrolled early access features
  Future<List<MyEarlyAccessFeatureModel>> getMyFeatures() async {
    try {
      final response = await _dioConsumer.get('/api/early-access/my-features');

      if (response['success'] == true) {
        final List<dynamic> featuresJson = response['data'] ?? [];
        return featuresJson.map((json) => MyEarlyAccessFeatureModel.fromJson(json)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to get my early access features');
      }
    } catch (e) {
      throw Exception('Failed to get my early access features: $e');
    }
  }

  /// Enroll in early access feature
  Future<bool> enrollInFeature(int featureId) async {
    try {
      final response = await _dioConsumer.post('/api/early-access/enroll', data: {
        'feature_id': featureId,
      });

      return response['success'] == true;
    } catch (e) {
      throw Exception('Failed to enroll in early access feature: $e');
    }
  }

  /// Unenroll from early access feature
  Future<bool> unenrollFromFeature(int featureId) async {
    try {
      final response = await _dioConsumer.post('/api/early-access/unenroll', data: {
        'feature_id': featureId,
      });

      return response['success'] == true;
    } catch (e) {
      throw Exception('Failed to unenroll from early access feature: $e');
    }
  }

  /// Submit feedback for early access feature
  Future<bool> submitFeedback(int featureId, String feedback, int? rating) async {
    try {
      final requestData = {
        'feature_id': featureId,
        'feedback': feedback,
        if (rating != null) 'rating': rating,
      };

      final response = await _dioConsumer.post('/api/early-access/feedback', data: requestData);

      return response['success'] == true;
    } catch (e) {
      throw Exception('Failed to submit feedback: $e');
    }
  }

  /// Mark feature as used
  Future<bool> markFeatureAsUsed(int featureId) async {
    try {
      final response = await _dioConsumer.post('/api/early-access/mark-used', data: {
        'feature_id': featureId,
      });

      return response['success'] == true;
    } catch (e) {
      throw Exception('Failed to mark feature as used: $e');
    }
  }

  /// Get feature statistics
  Future<EarlyAccessStatsModel> getFeatureStats(int featureId) async {
    try {
      final response = await _dioConsumer.get(
        '/api/early-access/stats',
        queryParameters: {'feature_id': featureId},
      );

      if (response['success'] == true) {
        return EarlyAccessStatsModel.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to get feature stats');
      }
    } catch (e) {
      throw Exception('Failed to get feature stats: $e');
    }
  }
}
