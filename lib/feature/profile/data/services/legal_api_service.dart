import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/feature/profile/data/models/legal_models.dart';

class LegalApiService {
  final DioConsumer _dioConsumer;

  LegalApiService(this._dioConsumer);
  
  /// Get all legal documents
  Future<List<LegalDocument>> getAllDocuments() async {
    try {
      final response = await _dioConsumer.get('/api/legal/documents');

      if (response['success'] == true) {
        final List<dynamic> documentsJson = response['data'] ?? [];
        return documentsJson.map((json) => LegalDocument.fromJson(json)).toList();
      } else {
        throw Exception(response['message'] ?? 'فشل في جلب الوثائق القانونية');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: ${e.toString()}');
    }
  }

  /// Get a specific legal document by type
  Future<LegalDocument> getDocumentByType(String type) async {
    try {
      final response = await _dioConsumer.get('/api/legal/documents/$type');

      if (response['success'] == true) {
        return LegalDocument.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'فشل في جلب الوثيقة');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: ${e.toString()}');
    }
  }

  /// Get terms of service
  Future<LegalDocument> getTerms() async {
    return getDocumentByType('terms');
  }

  /// Get privacy policy
  Future<LegalDocument> getPrivacyPolicy() async {
    return getDocumentByType('privacy');
  }

  /// Get user agreement
  Future<LegalDocument> getUserAgreement() async {
    return getDocumentByType('user_agreement');
  }

  /// Get host agreement
  Future<LegalDocument> getHostAgreement() async {
    return getDocumentByType('host_agreement');
  }
}
