import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/feature/profile/data/models/notification_models.dart';

class NotificationApiService {
  final DioConsumer _dioConsumer;

  NotificationApiService(this._dioConsumer);
  
  /// Get notifications
  Future<NotificationResponse> getNotifications({
    int page = 1,
    int perPage = 20,
    String? type,
    bool unreadOnly = false,
  }) async {
    try {
      Map<String, dynamic> queryParameters = {
        'page': page,
        'per_page': perPage,
      };

      if (type != null) {
        queryParameters['type'] = type;
      }
      if (unreadOnly) {
        queryParameters['unread_only'] = 'true';
      }

      final response = await _dioConsumer.get(
        '/api/notifications',
        queryParameters: queryParameters,
      );

      if (response['success'] == true) {
        return NotificationResponse.fromJson(response);
      } else {
        throw Exception(response['message'] ?? 'فشل في جلب الإشعارات');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: ${e.toString()}');
    }
  }

  /// Mark notification as read
  Future<void> markAsRead(int notificationId) async {
    try {
      final response = await _dioConsumer.post('/api/notifications/$notificationId/read', data: {});

      if (response['success'] != true) {
        throw Exception(response['message'] ?? 'فشل في تحديث الإشعار');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: ${e.toString()}');
    }
  }

  /// Mark all notifications as read
  Future<void> markAllAsRead() async {
    try {
      final response = await _dioConsumer.post('/api/notifications/mark-all-read', data: {});

      if (response['success'] != true) {
        throw Exception(response['message'] ?? 'فشل في تحديث الإشعارات');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: ${e.toString()}');
    }
  }

  /// Delete notification
  Future<void> deleteNotification(int notificationId) async {
    try {
      final response = await _dioConsumer.delete('/api/notifications/$notificationId');

      if (response['success'] != true) {
        throw Exception(response['message'] ?? 'فشل في حذف الإشعار');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: ${e.toString()}');
    }
  }

  /// Get notification preferences
  Future<List<NotificationPreference>> getPreferences() async {
    try {
      final response = await _dioConsumer.get('/api/notifications/preferences');

      if (response['success'] == true) {
        final List<dynamic> preferencesJson = response['data'] ?? [];
        return preferencesJson.map((json) => NotificationPreference.fromJson(json)).toList();
      } else {
        throw Exception(response['message'] ?? 'فشل في جلب تفضيلات الإشعارات');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: ${e.toString()}');
    }
  }

  /// Update notification preferences
  Future<void> updatePreferences(List<NotificationPreference> preferences) async {
    try {
      final requestData = {
        'preferences': preferences.map((pref) => pref.toJson()).toList(),
      };

      final response = await _dioConsumer.put('/api/notifications/preferences', data: requestData);

      if (response['success'] != true) {
        throw Exception(response['message'] ?? 'فشل في تحديث التفضيلات');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: ${e.toString()}');
    }
  }

  /// Get notification statistics
  Future<NotificationStats> getStats() async {
    try {
      final response = await _dioConsumer.get('/api/notifications/stats');

      if (response['success'] == true) {
        return NotificationStats.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'فشل في جلب إحصائيات الإشعارات');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: ${e.toString()}');
    }
  }

  /// Create test notification (for development)
  Future<void> createTestNotification() async {
    try {
      final response = await _dioConsumer.post('/api/notifications/test', data: {});

      if (response['success'] != true) {
        throw Exception(response['message'] ?? 'فشل في إنشاء الإشعار التجريبي');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: ${e.toString()}');
    }
  }
}
