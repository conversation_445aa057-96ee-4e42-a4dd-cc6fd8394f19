import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/core/routing/routes_branches.dart';
import 'package:gather_point/core/use_cases/no_params.dart';
import 'package:gather_point/core/utils/app_constants.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:gather_point/feature/profile/domain/use_cases/edit_profile_use_case.dart';
import 'package:gather_point/feature/profile/domain/use_cases/get_user_info_use_case.dart';
import 'package:gather_point/feature/profile/domain/use_cases/logout_use_case.dart';
import 'package:gather_point/feature/profile/domain/use_cases/toggle_hoster_mode_use_case.dart';
import 'package:gather_point/feature/profile/domain/use_cases/delete_account_use_case.dart';
import 'package:gather_point/feature/profile/presentation/cubit/profile_state.dart';
import 'package:hive/hive.dart';

class ProfileCubit extends Cubit<ProfileState> {
  final GetUserInfoUseCase getUserInfoUseCase;
  final EditProfileUseCase editProfileUseCase;
  final LogoutUseCase logoutUseCase;
  final ToggleHosterModeUseCase toggleHosterModeUseCase;
  final DeleteAccountUseCase deleteAccountUseCase;

  ProfileCubit({
    required this.getUserInfoUseCase,
    required this.editProfileUseCase,
    required this.logoutUseCase,
    required this.toggleHosterModeUseCase,
    required this.deleteAccountUseCase,
  }) : super(ProfileInitial());

  Future<void> loadUserInfo() async {
    emit(ProfileLoading());

    final result = await getUserInfoUseCase(const NoParams());

    result.fold(
      (failure) => emit(ProfileError(failure.errMessage)),
      (user) {
        // Preserve existing token since profile API doesn't return it
        final preservedUser = _preserveTokenAndAuthState(user);
        _saveUserToLocal(preservedUser);

        // Update the global navigation notifier to ensure bottom navigation is in sync
        isHosterModeNotifier.value = preservedUser.isHosterMode;

        emit(ProfileLoaded(preservedUser));
      },
    );
  }

  Future<void> editProfile({
    String? fullName,
    String? email,
    String? phone,
    String? bio,
    String? birthdate,
    int? gender,
    String? profilePicturePath,
  }) async {
    emit(ProfileLoading());
    
    final result = await editProfileUseCase(EditProfileParams(
      fullName: fullName,
      email: email,
      phone: phone,
      bio: bio,
      birthdate: birthdate,
      gender: gender,
      profilePicturePath: profilePicturePath,
    ));
    
    result.fold(
      (failure) => emit(ProfileError(failure.errMessage)),
      (user) {
        // Preserve existing token since edit profile API doesn't return it
        final preservedUser = _preserveTokenAndAuthState(user);
        _saveUserToLocal(preservedUser);

        // Update the global navigation notifier in case hoster mode changed
        isHosterModeNotifier.value = preservedUser.isHosterMode;

        emit(ProfileUpdated(preservedUser));
      },
    );
  }

  Future<void> logout() async {
    emit(ProfileLoading());
    
    final result = await logoutUseCase(const NoParams());

    result.fold(
      (failure) => emit(ProfileError(failure.errMessage)),
      (_) {
        _clearLocalData();
        emit(ProfileLoggedOut());
      },
    );
  }

  Future<void> toggleHosterMode(bool isHosterMode) async {
    debugPrint('🔄 ProfileCubit: toggleHosterMode() called with isHosterMode: $isHosterMode');
    debugPrint('🔄 ProfileCubit: Current notifier value before toggle: ${isHosterModeNotifier.value}');
    debugPrint('🔄 ProfileCubit: Current state type: ${state.runtimeType}');

    if (state is ProfileLoaded || state is ProfileHosterModeToggled) {
      final currentUser = state is ProfileLoaded
          ? (state as ProfileLoaded).user
          : (state as ProfileHosterModeToggled).user;
      debugPrint('🔄 ProfileCubit: Current user isHosterMode: ${currentUser.isHosterMode}');

      emit(ProfileLoading());

      debugPrint('🔄 ProfileCubit: About to call toggle API with isHosterMode: $isHosterMode');

      final result = await toggleHosterModeUseCase(
        ToggleHosterModeParams(isHosterMode: isHosterMode),
      );

      debugPrint('🔄 ProfileCubit: API call completed, processing result...');

      result.fold(
        (failure) {
          debugPrint('❌ ProfileCubit: API call failed with error: ${failure.errMessage}');
          // Revert to previous state on error
          emit(ProfileLoaded(currentUser));
          emit(ProfileError(failure.errMessage));
        },
        (user) {
          debugPrint('✅ ProfileCubit: API call succeeded! Received user: ${user.id}, isHosterMode: ${user.isHosterMode}');

          // Preserve existing token since toggle API doesn't return it
          final preservedUser = _preserveTokenAndAuthState(user);
          _saveUserToLocal(preservedUser);

          // Debug logging for navigation update
          debugPrint('ProfileCubit: Before updating notifier - current value: ${isHosterModeNotifier.value}');
          debugPrint('ProfileCubit: About to set notifier to: ${preservedUser.isHosterMode}');
          debugPrint('ProfileCubit: User data - id: ${preservedUser.id}, isHosterMode: ${preservedUser.isHosterMode}');

          // Update the global navigation notifier to refresh bottom navigation
          final oldValue = isHosterModeNotifier.value;
          isHosterModeNotifier.value = preservedUser.isHosterMode;

          // Verify the update with more details
          debugPrint('ProfileCubit: After updating notifier - old: $oldValue, new: ${isHosterModeNotifier.value}');

          // Check if the value actually changed
          if (oldValue != preservedUser.isHosterMode) {
            debugPrint('ProfileCubit: ✅ Value changed successfully from $oldValue to ${preservedUser.isHosterMode}');
          } else {
            debugPrint('ProfileCubit: ⚠️ WARNING - Value did not change! Both old and new are: ${preservedUser.isHosterMode}');
          }

          emit(ProfileHosterModeToggled(preservedUser));

          // Refetch profile data to ensure we have the latest information
          debugPrint('🔄 ProfileCubit: Refetching profile data after mode toggle...');
          loadUserInfo();
        },
      );
    } else {
      debugPrint('❌ ProfileCubit: Cannot toggle hoster mode - state is not ProfileLoaded, current state: ${state.runtimeType}');
    }
  }

  Future<void> deleteAccount() async {
    emit(ProfileLoading());

    final result = await deleteAccountUseCase(const NoParams());

    result.fold(
      (failure) => emit(ProfileError(failure.errMessage)),
      (_) {
        _clearLocalData();
        emit(ProfileLoggedOut());
      },
    );
  }

  void _saveUserToLocal(UserEntity user) {
    final box = Hive.box<UserEntity>(AppConstants.kMyProfileBoxName);
    box.put(AppConstants.kMyProfileKey, user);
  }

  void _clearLocalData() {
    final box = Hive.box<UserEntity>(AppConstants.kMyProfileBoxName);
    box.clear();
  }

  void loadLocalUser() {
    final box = Hive.box<UserEntity>(AppConstants.kMyProfileBoxName);
    final user = box.get(AppConstants.kMyProfileKey);

    if (user != null) {
      // Update the global navigation notifier to ensure bottom navigation is in sync
      isHosterModeNotifier.value = user.isHosterMode;
      emit(ProfileLoaded(user));
    } else {
      emit(ProfileInitial());
    }
  }

  /// Preserves the existing token and authentication state when updating user data
  /// This is necessary because some APIs (like getUserInfo, editProfile, toggleHosterMode)
  /// don't return the access_token in their response, which would clear the user's token
  UserEntity _preserveTokenAndAuthState(UserEntity newUserData) {
    final box = Hive.box<UserEntity>(AppConstants.kMyProfileBoxName);
    final existingUser = box.get(AppConstants.kMyProfileKey);

    if (existingUser != null) {
      // Preserve critical authentication data from the existing user
      return newUserData.copyWith(
        token: existingUser.token, // Preserve the original token
        id: newUserData.id == 0 ? existingUser.id : newUserData.id, // Preserve ID if API doesn't return it
        // Preserve authentication state - if user had a valid token, they're not a guest
        isGuest: existingUser.token.isNotEmpty ? false : newUserData.isGuest,
        otpApproved: existingUser.otpApproved, // Preserve OTP status
        referral: newUserData.referral.isEmpty ? existingUser.referral : newUserData.referral, // Preserve referral if not in response
      );
    }

    // If no existing user, return the new data as-is
    return newUserData;
  }
}
