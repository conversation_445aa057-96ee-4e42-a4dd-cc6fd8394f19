import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/core/widgets/enhanced_page_layouts.dart';
import 'package:gather_point/core/widgets/enhanced_ui_components.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:gather_point/feature/profile/presentation/cubit/profile_cubit.dart';
import 'package:gather_point/feature/profile/presentation/cubit/profile_state.dart';
import 'package:gather_point/generated/l10n.dart';

import 'package:go_router/go_router.dart';

class ViewProfileScreen extends StatelessWidget {
  const ViewProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ProfileCubit, ProfileState>(
      builder: (context, state) {
        UserEntity? user;

        if (state is ProfileLoaded || state is ProfileUpdated) {
          if (state is ProfileLoaded) user = state.user;
          if (state is ProfileUpdated) user = state.user;
        }

        if (user == null || user.isGuest) {
          return EnhancedPageLayout(
            title: 'عرض الملف الشخصي',
            showBackButton: true,
            body: const Center(
              child: Text('يجب تسجيل الدخول لعرض الملف الشخصي'),
            ),
          );
        }

        // Show different layouts based on user mode
        if (user.isHosterMode) {
          return _HosterProfileView(user: user);
        } else {
          return _ClientProfileView(user: user);
        }
      },
    );
  }
}

class _HosterProfileView extends StatelessWidget {
  final UserEntity user;

  const _HosterProfileView({required this.user});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.backgroundColor,
      appBar: AppBar(
        backgroundColor: context.backgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: context.primaryTextColor),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          'القائمة',
          style: AppTextStyles.font18Bold.copyWith(
            color: context.primaryTextColor,
          ),
        ),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Profile Header with Stats
            _HosterProfileHeader(user: user),
            const SizedBox(height: 24),

            // Menu Items
            const _HosterMenuItems(),

            const SizedBox(height: 32),

            // Switch to Travel Mode Button
            const _SwitchToTravelButton(),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }
}

class _ClientProfileView extends StatelessWidget {
  final UserEntity user;

  const _ClientProfileView({required this.user});

  @override
  Widget build(BuildContext context) {
    return EnhancedPageLayout(
      title: 'عرض الملف الشخصي',
      showBackButton: true,
      actions: [
        IconButton(
          icon: const Icon(Icons.edit),
          onPressed: () {
            context.push('/edit-profile');
          },
        ),
      ],
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Profile Header
            _ProfileHeader(user: user),
            const SizedBox(height: 24),

            // Profile Stats
            _ProfileStats(user: user),
            const SizedBox(height: 24),

            // About Section
            _AboutSection(user: user),
            const SizedBox(height: 24),

            // Verification Section
            _VerificationSection(user: user),
          ],
        ),
      ),
    );
  }
}

class _HosterProfileHeader extends StatelessWidget {
  final UserEntity user;

  const _HosterProfileHeader({required this.user});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // Profile Image and Name
          Row(
            children: [
              // Profile Image
              CircleAvatar(
                radius: 30,
                backgroundImage: user.image.isNotEmpty
                    ? CachedNetworkImageProvider(user.image)
                    : null,
                child: user.image.isEmpty
                    ? Icon(Icons.person, size: 30, color: Colors.grey.shade400)
                    : null,
              ),
              const SizedBox(width: 16),

              // Name and notification icon
              Expanded(
                child: Text(
                  user.fullName,
                  style: AppTextStyles.font18Bold.copyWith(
                    color: context.primaryTextColor,
                  ),
                ),
              ),

              // Notification icon
              Icon(
                Icons.notifications_outlined,
                color: context.primaryTextColor,
                size: 24,
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Stats Cards
          Row(
            children: [
              // Earnings Card
              Expanded(
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: const Color(0xFFE91E63).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'الأرباح',
                        style: AppTextStyles.font14Regular.copyWith(
                          color: context.primaryTextColor,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '${S.of(context).priceWithCurrency('0')} خلال هذا الشهر',
                        style: AppTextStyles.font12Regular.copyWith(
                          color: context.secondaryTextColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(width: 12),

              // Reviews Card
              Expanded(
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: const Color(0xFFE91E63).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'الرؤى والأفكار',
                        style: AppTextStyles.font14Regular.copyWith(
                          color: context.primaryTextColor,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Icon(
                            Icons.star,
                            color: Colors.amber,
                            size: 16,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '5.0 • تقييم 0',
                            style: AppTextStyles.font12Regular.copyWith(
                              color: context.secondaryTextColor,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class _HosterMenuItems extends StatelessWidget {
  const _HosterMenuItems();

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: context.cardColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          _HosterMenuItem(
            icon: Icons.flash_on,
            title: 'مميزات برنامج الوصول المبكر',
            subtitle: 'الجديد',
            hasNew: true,
            onTap: () {},
          ),
          _HosterMenuItem(
            icon: Icons.settings,
            title: 'إعدادات الحساب',
            onTap: () {},
          ),
          _HosterMenuItem(
            icon: Icons.receipt_long,
            title: 'موارد الاستضافة',
            onTap: () {},
          ),
          _HosterMenuItem(
            icon: Icons.help_outline,
            title: 'اطلب المساعدة',
            onTap: () {},
          ),
          _HosterMenuItem(
            icon: Icons.share,
            title: 'العثور على مضيف مشارك',
            onTap: () {},
          ),
          _HosterMenuItem(
            icon: Icons.add_home,
            title: 'إنشاء إعلان جديد',
            onTap: () {},
          ),
          _HosterMenuItem(
            icon: Icons.business,
            title: 'إحالة مضيف',
            onTap: () {},
          ),
          _HosterMenuItem(
            icon: Icons.description,
            title: 'قانوني',
            onTap: () {},
          ),
          _HosterMenuItem(
            icon: Icons.logout,
            title: 'تسجيل الخروج',
            onTap: () {},
            isLast: true,
          ),
        ],
      ),
    );
  }
}

class _HosterMenuItem extends StatelessWidget {
  final IconData icon;
  final String title;
  final String? subtitle;
  final bool hasNew;
  final VoidCallback onTap;
  final bool isLast;

  const _HosterMenuItem({
    required this.icon,
    required this.title,
    this.subtitle,
    this.hasNew = false,
    required this.onTap,
    this.isLast = false,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        decoration: BoxDecoration(
          border: isLast ? null : Border(
            bottom: BorderSide(
              color: context.secondaryTextColor.withValues(alpha: 0.1),
              width: 0.5,
            ),
          ),
        ),
        child: Row(
          children: [
            Icon(
              Icons.arrow_back_ios,
              size: 16,
              color: context.secondaryTextColor,
            ),
            const SizedBox(width: 12),

            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      if (hasNew && subtitle != null) ...[
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: const Color(0xFFE91E63),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Text(
                            subtitle!,
                            style: AppTextStyles.font10Regular.copyWith(
                              color: Colors.white,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                      ],
                      Text(
                        title,
                        style: AppTextStyles.font16Regular.copyWith(
                          color: context.primaryTextColor,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(width: 12),
            Icon(
              icon,
              color: context.primaryTextColor,
              size: 24,
            ),
          ],
        ),
      ),
    );
  }
}

class _SwitchToTravelButton extends StatelessWidget {
  const _SwitchToTravelButton();

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: ElevatedButton(
        onPressed: () {
          // Toggle to client mode
          context.read<ProfileCubit>().toggleHosterMode(false);
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: context.primaryTextColor,
          foregroundColor: context.backgroundColor,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          minimumSize: const Size(double.infinity, 50),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.flight_takeoff,
              color: context.backgroundColor,
            ),
            const SizedBox(width: 8),
            Text(
              'التبديل إلى السفر',
              style: AppTextStyles.font16SemiBold.copyWith(
                color: context.backgroundColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _ProfileHeader extends StatelessWidget {
  final UserEntity user;
  
  const _ProfileHeader({required this.user});

  @override
  Widget build(BuildContext context) {
    return EnhancedCard(
      child: Column(
        children: [
          // Profile Image
          CircleAvatar(
            radius: 60,
            backgroundImage: user.image.isNotEmpty
                ? CachedNetworkImageProvider(user.image)
                : null,
            child: user.image.isEmpty
                ? Icon(Icons.person, size: 60, color: Colors.grey.shade400)
                : null,
          ),
          const SizedBox(height: 16),
          
          // Name
          Text(
            user.fullName,
            style: AppTextStyles.font24Bold.copyWith(
              color: context.primaryTextColor,
            ),
            textAlign: TextAlign.center,
          ),
          
          // Bio
          if (user.bio.isNotEmpty) ...[
            const SizedBox(height: 8),
            Text(
              user.bio,
              style: AppTextStyles.font16Regular.copyWith(
                color: context.secondaryTextColor,
              ),
              textAlign: TextAlign.center,
            ),
          ],
          
          const SizedBox(height: 16),
          
          // User Type Badge
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: user.isHosterMode 
                  ? context.accentColor.withValues(alpha: 0.1)
                  : Colors.blue.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              user.isHosterMode ? 'مضيف' : 'ضيف',
              style: AppTextStyles.font14SemiBold.copyWith(
                color: user.isHosterMode ? context.accentColor : Colors.blue,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _ProfileStats extends StatelessWidget {
  final UserEntity user;
  
  const _ProfileStats({required this.user});

  @override
  Widget build(BuildContext context) {
    return EnhancedCard(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _StatItem(
            icon: Icons.star_rounded,
            label: 'التقييم',
            value: '4.8',
            color: Colors.amber,
          ),
          Container(
            width: 1,
            height: 40,
            color: context.secondaryTextColor.withValues(alpha: 0.2),
          ),
          _StatItem(
            icon: user.isHosterMode ? Icons.home_rounded : Icons.travel_explore,
            label: user.isHosterMode ? 'العقارات' : 'الرحلات',
            value: user.isHosterMode ? '12' : '5',
            color: context.accentColor,
          ),
          Container(
            width: 1,
            height: 40,
            color: context.secondaryTextColor.withValues(alpha: 0.2),
          ),
          _StatItem(
            icon: Icons.calendar_today_rounded,
            label: 'منذ',
            value: '2023',
            color: Colors.green,
          ),
        ],
      ),
    );
  }
}

class _StatItem extends StatelessWidget {
  final IconData icon;
  final String label;
  final String value;
  final Color color;

  const _StatItem({
    required this.icon,
    required this.label,
    required this.value,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            icon,
            color: color,
            size: 24,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: AppTextStyles.font18Bold.copyWith(
            color: context.primaryTextColor,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: AppTextStyles.font12Regular.copyWith(
            color: context.secondaryTextColor,
          ),
        ),
      ],
    );
  }
}

class _AboutSection extends StatelessWidget {
  final UserEntity user;
  
  const _AboutSection({required this.user});

  @override
  Widget build(BuildContext context) {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'نبذة عني',
            style: AppTextStyles.font18Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),

          if (user.bio.isNotEmpty)
            Text(
              user.bio,
              style: AppTextStyles.font14Regular.copyWith(
                color: context.secondaryTextColor,
              ),
            )
          else
            Text(
              'لم يتم إضافة نبذة شخصية بعد.',
              style: AppTextStyles.font14Regular.copyWith(
                color: context.secondaryTextColor,
              ),
            ),
          
          const SizedBox(height: 16),
          
          // Languages
          _InfoRow(
            icon: Icons.language,
            title: 'اللغات',
            value: 'العربية، الإنجليزية',
          ),
          
          // Location
          _InfoRow(
            icon: Icons.location_on,
            title: 'الموقع',
            value: 'جدة، المملكة العربية السعودية',
          ),
          
          // Member since
          _InfoRow(
            icon: Icons.calendar_today,
            title: 'عضو منذ',
            value: 'يناير 2023',
          ),
        ],
      ),
    );
  }
}

class _InfoRow extends StatelessWidget {
  final IconData icon;
  final String title;
  final String value;

  const _InfoRow({
    required this.icon,
    required this.title,
    required this.value,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(
            icon,
            size: 20,
            color: context.secondaryTextColor,
          ),
          const SizedBox(width: 12),
          Text(
            title,
            style: AppTextStyles.font14SemiBold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              value,
              style: AppTextStyles.font14Regular.copyWith(
                color: context.secondaryTextColor,
              ),
            ),
          ),
        ],
      ),
    );
  }
}





class _VerificationSection extends StatelessWidget {
  final UserEntity user;
  
  const _VerificationSection({required this.user});

  @override
  Widget build(BuildContext context) {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'التحقق',
            style: AppTextStyles.font18Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          
          _VerificationItem(
            icon: Icons.email,
            title: 'البريد الإلكتروني',
            isVerified: user.email.isNotEmpty,
          ),
          
          _VerificationItem(
            icon: Icons.phone,
            title: 'رقم الهاتف',
            isVerified: user.phone.isNotEmpty,
          ),
          
          const _VerificationItem(
            icon: Icons.badge,
            title: 'الهوية الشخصية',
            isVerified: false,
          ),
        ],
      ),
    );
  }
}

class _VerificationItem extends StatelessWidget {
  final IconData icon;
  final String title;
  final bool isVerified;

  const _VerificationItem({
    required this.icon,
    required this.title,
    required this.isVerified,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(
            icon,
            size: 20,
            color: context.secondaryTextColor,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              title,
              style: AppTextStyles.font14Regular.copyWith(
                color: context.primaryTextColor,
              ),
            ),
          ),
          Icon(
            isVerified ? Icons.check_circle : Icons.cancel,
            size: 20,
            color: isVerified ? Colors.green : Colors.red,
          ),
        ],
      ),
    );
  }
}
