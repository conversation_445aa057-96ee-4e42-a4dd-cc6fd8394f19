import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/core/widgets/enhanced_page_layouts.dart';
import 'package:gather_point/core/widgets/enhanced_ui_components.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:share_plus/share_plus.dart';

class ReferHostScreen extends StatelessWidget {
  const ReferHostScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    
    return EnhancedPageLayout(
      title: 'إحالة مضيف',
      showBackButton: true,
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Header Section
            _HeaderSection(),
            const SizedBox(height: 24),
            
            // Benefits Section
            _BenefitsSection(),
            const SizedBox(height: 24),
            
            // Referral Code Section
            _ReferralCodeSection(),
            const SizedBox(height: 24),
            
            // Share Options Section
            _ShareOptionsSection(),
            const SizedBox(height: 24),
            
            // How it Works Section
            _HowItWorksSection(),
          ],
        ),
      ),
    );
  }
}

class _HeaderSection extends StatelessWidget {
  const _HeaderSection();

  @override
  Widget build(BuildContext context) {
    return EnhancedCard(
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: context.accentColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(50),
            ),
            child: Icon(
              Icons.people_outline,
              size: 60,
              color: context.accentColor,
            ),
          ),
          const SizedBox(height: 16),
          
          Text(
            'ادع أصدقاءك ليصبحوا مضيفين',
            style: AppTextStyles.font20Bold.copyWith(
              color: context.primaryTextColor,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 12),
          
          Text(
            'احصل على مكافآت رائعة عندما ينضم أصدقاؤك كمضيفين ويحققون حجوزاتهم الأولى',
            style: AppTextStyles.font14Regular.copyWith(
              color: context.secondaryTextColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

class _BenefitsSection extends StatelessWidget {
  const _BenefitsSection();

  @override
  Widget build(BuildContext context) {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'المكافآت',
            style: AppTextStyles.font18Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          
          _BenefitItem(
            icon: Icons.monetization_on,
            title: '500 ريال',
            subtitle: 'لك عند انضمام صديقك كمضيف',
            color: Colors.green,
          ),
          
          _BenefitItem(
            icon: Icons.card_giftcard,
            title: '300 ريال',
            subtitle: 'لصديقك عند أول حجز ناجح',
            color: context.accentColor,
          ),
          
          _BenefitItem(
            icon: Icons.trending_up,
            title: 'عمولة إضافية',
            subtitle: '5% عمولة إضافية لأول 6 أشهر',
            color: Colors.blue,
          ),
        ],
      ),
    );
  }
}

class _BenefitItem extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subtitle;
  final Color color;

  const _BenefitItem({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              color: color,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTextStyles.font16Bold.copyWith(
                    color: context.primaryTextColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: AppTextStyles.font12Regular.copyWith(
                    color: context.secondaryTextColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _ReferralCodeSection extends StatelessWidget {
  const _ReferralCodeSection();

  @override
  Widget build(BuildContext context) {
    const referralCode = 'GATHER2024';
    
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'كود الإحالة الخاص بك',
            style: AppTextStyles.font18Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: context.accentColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: context.accentColor.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    referralCode,
                    style: AppTextStyles.font20Bold.copyWith(
                      color: context.accentColor,
                      letterSpacing: 2,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () {
                    Clipboard.setData(const ClipboardData(text: referralCode));
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('تم نسخ الكود'),
                        duration: Duration(seconds: 2),
                      ),
                    );
                  },
                  icon: Icon(
                    Icons.copy,
                    color: context.accentColor,
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 12),
          
          Text(
            'شارك هذا الكود مع أصدقائك ليستخدموه عند التسجيل كمضيفين',
            style: AppTextStyles.font12Regular.copyWith(
              color: context.secondaryTextColor,
            ),
          ),
        ],
      ),
    );
  }
}

class _ShareOptionsSection extends StatelessWidget {
  const _ShareOptionsSection();

  @override
  Widget build(BuildContext context) {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'شارك مع أصدقائك',
            style: AppTextStyles.font18Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          
          Row(
            children: [
              Expanded(
                child: _ShareButton(
                  icon: Icons.message,
                  title: 'رسالة نصية',
                  onTap: () => _shareViaMessage(context),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _ShareButton(
                  icon: Icons.email,
                  title: 'بريد إلكتروني',
                  onTap: () => _shareViaEmail(context),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          SizedBox(
            width: double.infinity,
            child: EnhancedButton(
              text: 'مشاركة عامة',
              icon: Icons.share,
              onPressed: () => _shareGeneral(context),
              backgroundColor: context.accentColor,
            ),
          ),
        ],
      ),
    );
  }

  void _shareViaMessage(BuildContext context) {
    const message = '''
انضم إلى Gather Point كمضيف واحصل على دخل إضافي!

استخدم كود الإحالة: GATHER2024
واحصل على 300 ريال مكافأة عند أول حجز ناجح.

حمل التطبيق الآن:
https://gatherpoint.sa/download
''';
    
    Share.share(message);
  }

  void _shareViaEmail(BuildContext context) {
    const subject = 'انضم إلى Gather Point كمضيف';
    const body = '''
مرحباً،

أدعوك للانضمام إلى Gather Point كمضيف وتحقيق دخل إضافي من خلال تأجير عقارك.

استخدم كود الإحالة الخاص بي: GATHER2024
واحصل على 300 ريال مكافأة عند أول حجز ناجح.

المزايا:
• دخل إضافي مضمون
• منصة آمنة وموثوقة
• دعم فني على مدار الساعة
• عمولة إضافية 5% لأول 6 أشهر

حمل التطبيق الآن:
https://gatherpoint.sa/download

مع تحياتي
''';
    
    Share.share('$subject\n\n$body');
  }

  void _shareGeneral(BuildContext context) {
    const message = '''
انضم إلى Gather Point كمضيف! 🏠

استخدم كود الإحالة: GATHER2024
احصل على 300 ريال مكافأة 💰

حمل التطبيق: https://gatherpoint.sa/download
''';
    
    Share.share(message);
  }
}

class _ShareButton extends StatelessWidget {
  final IconData icon;
  final String title;
  final VoidCallback onTap;

  const _ShareButton({
    required this.icon,
    required this.title,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return EnhancedCard(
      onTap: onTap,
      child: Column(
        children: [
          Icon(
            icon,
            size: 32,
            color: context.accentColor,
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: AppTextStyles.font12SemiBold.copyWith(
              color: context.primaryTextColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

class _HowItWorksSection extends StatelessWidget {
  const _HowItWorksSection();

  @override
  Widget build(BuildContext context) {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'كيف يعمل البرنامج؟',
            style: AppTextStyles.font18Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          
          _StepItem(
            step: '1',
            title: 'شارك كود الإحالة',
            description: 'أرسل كود الإحالة الخاص بك لأصدقائك',
          ),
          
          _StepItem(
            step: '2',
            title: 'صديقك ينضم كمضيف',
            description: 'يستخدم صديقك الكود عند التسجيل كمضيف',
          ),
          
          _StepItem(
            step: '3',
            title: 'يحقق أول حجز',
            description: 'عندما يحقق صديقك أول حجز ناجح',
          ),
          
          _StepItem(
            step: '4',
            title: 'تحصلان على المكافآت',
            description: '500 ريال لك و 300 ريال لصديقك',
            isLast: true,
          ),
        ],
      ),
    );
  }
}

class _StepItem extends StatelessWidget {
  final String step;
  final String title;
  final String description;
  final bool isLast;

  const _StepItem({
    required this.step,
    required this.title,
    required this.description,
    this.isLast = false,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          children: [
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: context.accentColor,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Center(
                child: Text(
                  step,
                  style: AppTextStyles.font14Bold.copyWith(
                    color: Colors.white,
                  ),
                ),
              ),
            ),
            if (!isLast)
              Container(
                width: 2,
                height: 40,
                color: context.accentColor.withValues(alpha: 0.3),
                margin: const EdgeInsets.symmetric(vertical: 8),
              ),
          ],
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: AppTextStyles.font16SemiBold.copyWith(
                  color: context.primaryTextColor,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: AppTextStyles.font12Regular.copyWith(
                  color: context.secondaryTextColor,
                ),
              ),
              if (!isLast) const SizedBox(height: 16),
            ],
          ),
        ),
      ],
    );
  }
}
