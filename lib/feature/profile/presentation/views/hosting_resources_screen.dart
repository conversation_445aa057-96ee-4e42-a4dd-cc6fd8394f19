import 'package:flutter/material.dart';
import 'package:gather_point/core/widgets/enhanced_page_layouts.dart';
import 'package:gather_point/core/widgets/enhanced_ui_components.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:url_launcher/url_launcher.dart';

class HostingResourcesScreen extends StatefulWidget {
  const HostingResourcesScreen({super.key});

  @override
  State<HostingResourcesScreen> createState() => _HostingResourcesScreenState();
}

class _HostingResourcesScreenState extends State<HostingResourcesScreen> with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return EnhancedPageLayout(
      title: 'موارد الاستضافة',
      showBackButton: true,
      body: Column(
        children: [
          // Tab Bar
          Container(
            margin: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: context.cardColor,
              borderRadius: BorderRadius.circular(25),
              border: Border.all(color: context.secondaryTextColor.withValues(alpha: 0.2)),
            ),
            child: TabBar(
              controller: _tabController,
              indicator: BoxDecoration(
                color: context.accentColor,
                borderRadius: BorderRadius.circular(25),
              ),
              labelColor: Colors.black,
              unselectedLabelColor: context.primaryTextColor,
              labelStyle: AppTextStyles.font12SemiBold,
              unselectedLabelStyle: AppTextStyles.font12Regular,
              dividerColor: Colors.transparent,
              isScrollable: true,
              tabs: const [
                Tab(text: 'البداية'),
                Tab(text: 'الأمان'),
                Tab(text: 'التسويق'),
                Tab(text: 'القوانين'),
              ],
            ),
          ),
          
          // Tab Views
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildGettingStartedTab(),
                _buildSafetyTab(),
                _buildMarketingTab(),
                _buildLegalTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGettingStartedTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWelcomeCard(),
          const SizedBox(height: 16),
          _buildQuickStartGuide(),
          const SizedBox(height: 16),
          _buildHostingTips(),
        ],
      ),
    );
  }

  Widget _buildSafetyTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSafetyOverview(),
          const SizedBox(height: 16),
          _buildSafetyChecklist(),
          const SizedBox(height: 16),
          _buildEmergencyContacts(),
        ],
      ),
    );
  }

  Widget _buildMarketingTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildMarketingTips(),
          const SizedBox(height: 16),
          _buildPhotographyGuide(),
          const SizedBox(height: 16),
          _buildPricingStrategies(),
        ],
      ),
    );
  }

  Widget _buildLegalTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildLegalRequirements(),
          const SizedBox(height: 16),
          _buildTaxInformation(),
          const SizedBox(height: 16),
          _buildInsuranceInfo(),
        ],
      ),
    );
  }

  Widget _buildWelcomeCard() {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: context.accentColor.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(30),
                ),
                child: Icon(
                  Icons.home_outlined,
                  size: 30,
                  color: context.accentColor,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'مرحباً بك في عالم الاستضافة!',
                      style: AppTextStyles.font18Bold.copyWith(
                        color: context.primaryTextColor,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'دليلك الشامل لتصبح مضيفاً ناجحاً',
                      style: AppTextStyles.font14Regular.copyWith(
                        color: context.secondaryTextColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            'نحن هنا لمساعدتك في كل خطوة من رحلة الاستضافة. من إعداد أول إعلان إلى استقبال الضيوف وتحقيق أقصى استفادة من عقارك.',
            style: AppTextStyles.font14Regular.copyWith(
              color: context.primaryTextColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStartGuide() {
    final steps = [
      {
        'title': 'أضف عقارك',
        'description': 'قم بإنشاء إعلان جذاب مع صور عالية الجودة',
        'icon': Icons.add_home_outlined,
      },
      {
        'title': 'حدد الأسعار',
        'description': 'اختر أسعاراً تنافسية تناسب السوق المحلي',
        'icon': Icons.attach_money_outlined,
      },
      {
        'title': 'استقبل الضيوف',
        'description': 'قدم تجربة استضافة مميزة لضيوفك',
        'icon': Icons.people_outline,
      },
      {
        'title': 'احصل على التقييمات',
        'description': 'اجمع تقييمات إيجابية لزيادة حجوزاتك',
        'icon': Icons.star_outline,
      },
    ];

    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'دليل البداية السريعة',
            style: AppTextStyles.font16SemiBold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          ...steps.asMap().entries.map((entry) {
            final index = entry.key;
            final step = entry.value;
            return Padding(
              padding: EdgeInsets.only(bottom: index < steps.length - 1 ? 16 : 0),
              child: Row(
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: context.accentColor,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Center(
                      child: Text(
                        '${index + 1}',
                        style: AppTextStyles.font14SemiBold.copyWith(
                          color: Colors.black,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          step['title'] as String,
                          style: AppTextStyles.font14SemiBold.copyWith(
                            color: context.primaryTextColor,
                          ),
                        ),
                        Text(
                          step['description'] as String,
                          style: AppTextStyles.font12Regular.copyWith(
                            color: context.secondaryTextColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    step['icon'] as IconData,
                    color: context.accentColor,
                    size: 24,
                  ),
                ],
              ),
            );
          }).toList(),
        ],
      ),
    );
  }

  Widget _buildHostingTips() {
    final tips = [
      'استخدم صوراً عالية الجودة تُظهر أفضل ما في عقارك',
      'اكتب وصفاً مفصلاً وصادقاً لعقارك',
      'حدد قواعد واضحة للضيوف',
      'رد على الاستفسارات بسرعة',
      'حافظ على نظافة وترتيب المكان',
      'قدم لمسات شخصية تجعل الإقامة مميزة',
    ];

    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.lightbulb_outline,
                color: context.accentColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'نصائح للاستضافة الناجحة',
                style: AppTextStyles.font16SemiBold.copyWith(
                  color: context.primaryTextColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...tips.map((tip) => Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: 6,
                  height: 6,
                  margin: const EdgeInsets.only(top: 6),
                  decoration: BoxDecoration(
                    color: context.accentColor,
                    borderRadius: BorderRadius.circular(3),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    tip,
                    style: AppTextStyles.font14Regular.copyWith(
                      color: context.primaryTextColor,
                    ),
                  ),
                ),
              ],
            ),
          )).toList(),
        ],
      ),
    );
  }

  Widget _buildSafetyOverview() {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.security_outlined,
                color: Colors.green,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'الأمان والحماية',
                style: AppTextStyles.font16SemiBold.copyWith(
                  color: context.primaryTextColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            'سلامتك وسلامة ضيوفك هي أولويتنا القصوى. نوفر لك الأدوات والموارد اللازمة لضمان تجربة آمنة للجميع.',
            style: AppTextStyles.font14Regular.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: () => _launchUrl('https://help.gatherpoint.com/safety'),
            icon: const Icon(Icons.open_in_new),
            label: const Text('دليل الأمان الشامل'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSafetyChecklist() {
    final safetyItems = [
      {'title': 'أجهزة إنذار الحريق', 'checked': true},
      {'title': 'طفايات الحريق', 'checked': true},
      {'title': 'أقفال آمنة', 'checked': false},
      {'title': 'إضاءة خارجية', 'checked': true},
      {'title': 'كاميرات مراقبة خارجية', 'checked': false},
      {'title': 'صندوق إسعافات أولية', 'checked': true},
    ];

    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'قائمة فحص الأمان',
            style: AppTextStyles.font16SemiBold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          ...safetyItems.map((item) => Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              children: [
                Icon(
                  item['checked'] as bool ? Icons.check_circle : Icons.radio_button_unchecked,
                  color: item['checked'] as bool ? Colors.green : Colors.grey,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    item['title'] as String,
                    style: AppTextStyles.font14Regular.copyWith(
                      color: context.primaryTextColor,
                    ),
                  ),
                ),
              ],
            ),
          )).toList(),
        ],
      ),
    );
  }

  Widget _buildEmergencyContacts() {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'أرقام الطوارئ',
            style: AppTextStyles.font16SemiBold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          _buildEmergencyContact('الشرطة', '999', Icons.local_police),
          _buildEmergencyContact('الإسعاف', '997', Icons.local_hospital),
          _buildEmergencyContact('الدفاع المدني', '998', Icons.local_fire_department),
          _buildEmergencyContact('دعم جاذر بوينت', '+966501234567', Icons.support_agent),
        ],
      ),
    );
  }

  Widget _buildEmergencyContact(String title, String number, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(icon, color: Colors.red, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              title,
              style: AppTextStyles.font14Regular.copyWith(
                color: context.primaryTextColor,
              ),
            ),
          ),
          GestureDetector(
            onTap: () => _launchUrl('tel:$number'),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
              ),
              child: Text(
                number,
                style: AppTextStyles.font12SemiBold.copyWith(
                  color: Colors.red,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMarketingTips() {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.trending_up,
                color: context.accentColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'نصائح التسويق',
                style: AppTextStyles.font16SemiBold.copyWith(
                  color: context.primaryTextColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            'اجعل إعلانك يبرز من بين المنافسين واجذب المزيد من الضيوف.',
            style: AppTextStyles.font14Regular.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          _buildMarketingTip('العنوان الجذاب', 'اختر عنواناً يصف عقارك بطريقة مميزة'),
          _buildMarketingTip('الكلمات المفتاحية', 'استخدم كلمات يبحث عنها الضيوف'),
          _buildMarketingTip('العروض الخاصة', 'قدم خصومات للحجوزات الطويلة'),
          _buildMarketingTip('التحديث المستمر', 'حدث إعلانك بانتظام'),
        ],
      ),
    );
  }

  Widget _buildMarketingTip(String title, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 8,
            height: 8,
            margin: const EdgeInsets.only(top: 6),
            decoration: BoxDecoration(
              color: context.accentColor,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTextStyles.font14SemiBold.copyWith(
                    color: context.primaryTextColor,
                  ),
                ),
                Text(
                  description,
                  style: AppTextStyles.font12Regular.copyWith(
                    color: context.secondaryTextColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPhotographyGuide() {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'دليل التصوير',
            style: AppTextStyles.font16SemiBold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'الصور هي أول ما يراه الضيوف. تعلم كيفية التقاط صور احترافية لعقارك.',
            style: AppTextStyles.font14Regular.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: () => _launchUrl('https://help.gatherpoint.com/photography'),
            icon: const Icon(Icons.camera_alt_outlined),
            label: const Text('دليل التصوير المفصل'),
          ),
        ],
      ),
    );
  }

  Widget _buildPricingStrategies() {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'استراتيجيات التسعير',
            style: AppTextStyles.font16SemiBold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'تعلم كيفية تحديد أسعار تنافسية تحقق لك أقصى ربح.',
            style: AppTextStyles.font14Regular.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _showPricingCalculator(),
                  icon: const Icon(Icons.calculate_outlined),
                  label: const Text('حاسبة الأسعار'),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () => _launchUrl('https://help.gatherpoint.com/pricing'),
                  icon: const Icon(Icons.trending_up),
                  label: const Text('تحليل السوق'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLegalRequirements() {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.gavel_outlined,
                color: Colors.blue,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'المتطلبات القانونية',
                style: AppTextStyles.font16SemiBold.copyWith(
                  color: context.primaryTextColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            'تأكد من امتثالك لجميع القوانين واللوائح المحلية.',
            style: AppTextStyles.font14Regular.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          _buildLegalItem('رخصة الاستضافة', 'مطلوبة في معظم المناطق'),
          _buildLegalItem('تصريح البلدية', 'تحقق من متطلبات منطقتك'),
          _buildLegalItem('شهادة السلامة', 'فحص دوري للمرافق'),
        ],
      ),
    );
  }

  Widget _buildLegalItem(String title, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(Icons.check_circle_outline, color: Colors.blue, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTextStyles.font14SemiBold.copyWith(
                    color: context.primaryTextColor,
                  ),
                ),
                Text(
                  description,
                  style: AppTextStyles.font12Regular.copyWith(
                    color: context.secondaryTextColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTaxInformation() {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'المعلومات الضريبية',
            style: AppTextStyles.font16SemiBold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'فهم التزاماتك الضريبية كمضيف.',
            style: AppTextStyles.font14Regular.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: () => _launchUrl('https://help.gatherpoint.com/taxes'),
            icon: const Icon(Icons.receipt_long_outlined),
            label: const Text('دليل الضرائب'),
          ),
        ],
      ),
    );
  }

  Widget _buildInsuranceInfo() {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'معلومات التأمين',
            style: AppTextStyles.font16SemiBold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'احم نفسك وعقارك بالتأمين المناسب.',
            style: AppTextStyles.font14Regular.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: () => _launchUrl('https://help.gatherpoint.com/insurance'),
            icon: const Icon(Icons.shield_outlined),
            label: const Text('خيارات التأمين'),
          ),
        ],
      ),
    );
  }

  void _launchUrl(String url) async {
    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('لا يمكن فتح الرابط')),
        );
      }
    }
  }

  void _showPricingCalculator() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حاسبة الأسعار'),
        content: const Text('ميزة حاسبة الأسعار قيد التطوير وستكون متاحة قريباً.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }
}
