import 'package:flutter/material.dart';
import 'package:gather_point/core/widgets/enhanced_page_layouts.dart';
import 'package:gather_point/core/widgets/enhanced_ui_components.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/feature/profile/data/services/co_host_api_service.dart';
import 'package:gather_point/feature/profile/data/models/co_host_model.dart';
import 'package:gather_point/core/services/service_locator.dart';
import 'package:cached_network_image/cached_network_image.dart';

class CoHostScreen extends StatefulWidget {
  const CoHostScreen({super.key});

  @override
  State<CoHostScreen> createState() => _CoHostScreenState();
}

class _CoHostScreenState extends State<CoHostScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  final CoHostApiService _coHostApiService = getIt<CoHostApiService>();
  
  List<CoHostModel> _coHosts = [];
  List<CoHostInvitationModel> _pendingInvitations = [];
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final coHostsResult = await _coHostApiService.getCoHosts();
      final invitationsResult = await _coHostApiService.getPendingInvitations();
      
      if (mounted) {
        setState(() {
          _coHosts = coHostsResult;
          _pendingInvitations = invitationsResult;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return EnhancedPageLayout(
      title: 'المضيفين المشاركين',
      showBackButton: true,
      actions: [
        IconButton(
          icon: const Icon(Icons.person_add_outlined),
          onPressed: () => _showSearchDialog(),
          tooltip: 'إضافة مضيف مشارك',
        ),
      ],
      body: Column(
        children: [
          // Tab Bar
          Container(
            margin: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: context.cardColor,
              borderRadius: BorderRadius.circular(25),
              border: Border.all(color: context.secondaryTextColor.withValues(alpha: 0.2)),
            ),
            child: TabBar(
              controller: _tabController,
              indicator: BoxDecoration(
                color: context.accentColor,
                borderRadius: BorderRadius.circular(25),
              ),
              labelColor: Colors.black,
              unselectedLabelColor: context.primaryTextColor,
              labelStyle: AppTextStyles.font14SemiBold,
              unselectedLabelStyle: AppTextStyles.font14Regular,
              dividerColor: Colors.transparent,
              tabs: [
                Tab(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Text('المضيفين'),
                      if (_coHosts.isNotEmpty) ...[
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: Colors.black.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Text(
                            '${_coHosts.length}',
                            style: AppTextStyles.font12Bold.copyWith(
                              color: Colors.black,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                Tab(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Text('الدعوات'),
                      if (_pendingInvitations.isNotEmpty) ...[
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: Colors.orange,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Text(
                            '${_pendingInvitations.length}',
                            style: AppTextStyles.font12Bold.copyWith(
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                const Tab(text: 'البحث'),
              ],
            ),
          ),
          
          // Tab Views
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildCoHostsTab(),
                _buildInvitationsTab(),
                _buildSearchTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCoHostsTab() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return _buildErrorWidget();
    }

    if (_coHosts.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.people_outline,
              size: 64,
              color: context.secondaryTextColor.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد مضيفين مشاركين',
              style: AppTextStyles.font18Bold.copyWith(
                color: context.primaryTextColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'ادع مضيفين آخرين للمساعدة في إدارة عقاراتك',
              style: AppTextStyles.font14Regular.copyWith(
                color: context.secondaryTextColor,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () => _showSearchDialog(),
              icon: const Icon(Icons.person_add),
              label: const Text('إضافة مضيف مشارك'),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _coHosts.length,
        itemBuilder: (context, index) {
          final coHost = _coHosts[index];
          return _buildCoHostCard(coHost);
        },
      ),
    );
  }

  Widget _buildInvitationsTab() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_pendingInvitations.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.mail_outline,
              size: 64,
              color: context.secondaryTextColor.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد دعوات معلقة',
              style: AppTextStyles.font18Bold.copyWith(
                color: context.primaryTextColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'ستظهر هنا الدعوات المرسلة للمضيفين المشاركين',
              style: AppTextStyles.font14Regular.copyWith(
                color: context.secondaryTextColor,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _pendingInvitations.length,
        itemBuilder: (context, index) {
          final invitation = _pendingInvitations[index];
          return _buildInvitationCard(invitation);
        },
      ),
    );
  }

  Widget _buildSearchTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          TextField(
            decoration: InputDecoration(
              hintText: 'ابحث عن مضيفين بالاسم أو البريد الإلكتروني',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            onSubmitted: (query) => _searchCoHosts(query),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.search,
                    size: 64,
                    color: context.secondaryTextColor.withValues(alpha: 0.5),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'ابحث عن مضيفين مشاركين',
                    style: AppTextStyles.font18Bold.copyWith(
                      color: context.primaryTextColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'اكتب اسم أو بريد إلكتروني للبحث عن مضيفين للتعاون معهم',
                    style: AppTextStyles.font14Regular.copyWith(
                      color: context.secondaryTextColor,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCoHostCard(CoHostModel coHost) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: EnhancedCard(
        child: Column(
          children: [
            Row(
              children: [
                // Profile Image
                CircleAvatar(
                  radius: 30,
                  backgroundImage: coHost.coHost.profilePhotoUrl != null
                      ? CachedNetworkImageProvider(coHost.coHost.profilePhotoUrl!)
                      : null,
                  child: coHost.coHost.profilePhotoUrl == null
                      ? Icon(Icons.person, size: 30, color: Colors.grey.shade400)
                      : null,
                ),
                const SizedBox(width: 12),
                
                // Co-Host Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        coHost.coHost.fullName,
                        style: AppTextStyles.font16SemiBold.copyWith(
                          color: context.primaryTextColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      if (coHost.property != null)
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: context.accentColor.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            coHost.property!.title,
                            style: AppTextStyles.font12SemiBold.copyWith(
                              color: context.accentColor,
                            ),
                          ),
                        )
                      else
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: Colors.blue.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            'جميع العقارات',
                            style: AppTextStyles.font12SemiBold.copyWith(
                              color: Colors.blue,
                            ),
                          ),
                        ),
                      const SizedBox(height: 4),
                      Text(
                        'عمولة: ${coHost.commissionPercentage}%',
                        style: AppTextStyles.font12Regular.copyWith(
                          color: context.secondaryTextColor,
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Actions
                PopupMenuButton<String>(
                  onSelected: (value) => _handleCoHostAction(value, coHost),
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'permissions',
                      child: Row(
                        children: [
                          Icon(Icons.admin_panel_settings_outlined),
                          SizedBox(width: 8),
                          Text('الصلاحيات'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'remove',
                      child: Row(
                        children: [
                          Icon(Icons.person_remove_outlined, color: Colors.red),
                          SizedBox(width: 8),
                          Text('إزالة', style: TextStyle(color: Colors.red)),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            // Permissions
            Wrap(
              spacing: 8,
              runSpacing: 4,
              children: coHost.permissions.map((permission) => Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: context.cardColor,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: context.secondaryTextColor.withValues(alpha: 0.3)),
                ),
                child: Text(
                  _getPermissionName(permission),
                  style: AppTextStyles.font12Regular.copyWith(
                    color: context.primaryTextColor,
                  ),
                ),
              )).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInvitationCard(CoHostInvitationModel invitation) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: EnhancedCard(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // Profile Image
                CircleAvatar(
                  radius: 25,
                  backgroundImage: invitation.host.profilePhotoUrl != null
                      ? CachedNetworkImageProvider(invitation.host.profilePhotoUrl!)
                      : null,
                  child: invitation.host.profilePhotoUrl == null
                      ? Icon(Icons.person, size: 25, color: Colors.grey.shade400)
                      : null,
                ),
                const SizedBox(width: 12),
                
                // Host Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        invitation.host.fullName,
                        style: AppTextStyles.font16SemiBold.copyWith(
                          color: context.primaryTextColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'دعوة مضيف مشارك',
                        style: AppTextStyles.font12Regular.copyWith(
                          color: context.secondaryTextColor,
                        ),
                      ),
                    ],
                  ),
                ),
                
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.orange.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'معلقة',
                    style: AppTextStyles.font12SemiBold.copyWith(
                      color: Colors.orange,
                    ),
                  ),
                ),
              ],
            ),
            
            if (invitation.message != null) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: context.cardColor.withValues(alpha: 0.5),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  invitation.message!,
                  style: AppTextStyles.font14Regular.copyWith(
                    color: context.primaryTextColor,
                  ),
                ),
              ),
            ],
            
            const SizedBox(height: 12),
            
            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _acceptInvitation(invitation),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: context.accentColor,
                      foregroundColor: Colors.black,
                    ),
                    child: const Text('قبول'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => _declineInvitation(invitation),
                    child: const Text('رفض'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red.shade300,
          ),
          const SizedBox(height: 16),
          Text(
            'حدث خطأ في تحميل البيانات',
            style: AppTextStyles.font16SemiBold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _error!,
            style: AppTextStyles.font14Regular.copyWith(
              color: context.secondaryTextColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadData,
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  void _showSearchDialog() {
    // TODO: Implement search dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('ميزة البحث قيد التطوير')),
    );
  }

  void _searchCoHosts(String query) {
    // TODO: Implement co-host search
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('ميزة البحث قيد التطوير')),
    );
  }

  void _handleCoHostAction(String action, CoHostModel coHost) {
    switch (action) {
      case 'permissions':
        _showPermissionsDialog(coHost);
        break;
      case 'remove':
        _removeCoHost(coHost);
        break;
    }
  }

  void _showPermissionsDialog(CoHostModel coHost) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('صلاحيات ${coHost.coHost.fullName}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: coHost.permissions.map((permission) => ListTile(
            leading: const Icon(Icons.check_circle, color: Colors.green),
            title: Text(_getPermissionName(permission)),
            dense: true,
          )).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Future<void> _acceptInvitation(CoHostInvitationModel invitation) async {
    try {
      await _coHostApiService.acceptInvitation(invitation.id);
      _loadData(); // Refresh data
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم قبول دعوة المضيف المشارك'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ: ${e.toString()}')),
        );
      }
    }
  }

  Future<void> _declineInvitation(CoHostInvitationModel invitation) async {
    try {
      await _coHostApiService.declineInvitation(invitation.id);
      _loadData(); // Refresh data
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم رفض دعوة المضيف المشارك')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ: ${e.toString()}')),
        );
      }
    }
  }

  Future<void> _removeCoHost(CoHostModel coHost) async {
    try {
      await _coHostApiService.removeCoHost(coHost.id);
      _loadData(); // Refresh data
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم حذف المضيف المشارك')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ: ${e.toString()}')),
        );
      }
    }
  }

  String _getPermissionName(String permission) {
    const permissions = {
      'manage_bookings': 'إدارة الحجوزات',
      'communicate_guests': 'التواصل مع الضيوف',
      'update_calendar': 'تحديث التقويم',
      'view_earnings': 'عرض الأرباح',
      'edit_listing': 'تعديل الإعلان',
      'manage_pricing': 'إدارة الأسعار',
      'access_reviews': 'الوصول للتقييمات',
      'manage_amenities': 'إدارة المرافق',
    };
    
    return permissions[permission] ?? permission;
  }
}
