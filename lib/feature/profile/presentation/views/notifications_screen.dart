import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/theme/theme_extensions.dart';
import 'package:gather_point/core/widgets/enhanced_card.dart';

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> {
  bool _pushNotifications = true;
  bool _emailNotifications = false;
  bool _smsNotifications = true;
  bool _reservationUpdates = true;
  bool _promotionalOffers = false;
  bool _systemUpdates = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.backgroundColor,
      appBar: AppBar(
        title: Text(
          'الإشعارات',
          style: AppTextStyles.font18Bold.copyWith(
            color: context.primaryTextColor,
          ),
        ),
        backgroundColor: context.backgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios,
            color: context.primaryTextColor,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إعدادات الإشعارات',
              style: AppTextStyles.font20Bold.copyWith(
                color: context.primaryTextColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'اختر كيفية تلقي الإشعارات والتحديثات',
              style: AppTextStyles.font14Regular.copyWith(
                color: context.secondaryTextColor,
              ),
            ),
            const SizedBox(height: 24),
            
            // Notification Methods
            EnhancedCard(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'طرق الإشعار',
                    style: AppTextStyles.font16SemiBold.copyWith(
                      color: context.primaryTextColor,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildNotificationToggle(
                    'الإشعارات المباشرة',
                    'تلقي الإشعارات على الجهاز',
                    _pushNotifications,
                    (value) => setState(() => _pushNotifications = value),
                  ),
                  _buildNotificationToggle(
                    'البريد الإلكتروني',
                    'تلقي الإشعارات عبر البريد الإلكتروني',
                    _emailNotifications,
                    (value) => setState(() => _emailNotifications = value),
                  ),
                  _buildNotificationToggle(
                    'الرسائل النصية',
                    'تلقي الإشعارات عبر الرسائل النصية',
                    _smsNotifications,
                    (value) => setState(() => _smsNotifications = value),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Notification Types
            EnhancedCard(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'أنواع الإشعارات',
                    style: AppTextStyles.font16SemiBold.copyWith(
                      color: context.primaryTextColor,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildNotificationToggle(
                    'تحديثات الحجوزات',
                    'إشعارات حول حالة الحجوزات والتأكيدات',
                    _reservationUpdates,
                    (value) => setState(() => _reservationUpdates = value),
                  ),
                  _buildNotificationToggle(
                    'العروض الترويجية',
                    'إشعارات حول العروض والخصومات الخاصة',
                    _promotionalOffers,
                    (value) => setState(() => _promotionalOffers = value),
                  ),
                  _buildNotificationToggle(
                    'تحديثات النظام',
                    'إشعارات حول تحديثات التطبيق والنظام',
                    _systemUpdates,
                    (value) => setState(() => _systemUpdates = value),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Save Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _saveNotificationSettings,
                style: ElevatedButton.styleFrom(
                  backgroundColor: context.primaryColor,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(
                  'حفظ الإعدادات',
                  style: AppTextStyles.font16SemiBold.copyWith(
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationToggle(
    String title,
    String subtitle,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTextStyles.font14SemiBold.copyWith(
                    color: context.primaryTextColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: AppTextStyles.font12Regular.copyWith(
                    color: context.secondaryTextColor,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: context.primaryColor,
            activeTrackColor: context.primaryColor.withValues(alpha: 0.3),
          ),
        ],
      ),
    );
  }

  void _saveNotificationSettings() {
    // TODO: Implement saving notification settings to backend
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'تم حفظ إعدادات الإشعارات بنجاح',
          style: AppTextStyles.font14Regular.copyWith(
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
}
