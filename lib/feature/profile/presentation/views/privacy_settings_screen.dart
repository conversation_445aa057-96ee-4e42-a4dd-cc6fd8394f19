import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/core/widgets/enhanced_page_layouts.dart';
import 'package:gather_point/core/widgets/enhanced_ui_components.dart';
import 'package:gather_point/generated/l10n.dart';

class PrivacySettingsScreen extends StatefulWidget {
  const PrivacySettingsScreen({super.key});

  @override
  State<PrivacySettingsScreen> createState() => _PrivacySettingsScreenState();
}

class _PrivacySettingsScreenState extends State<PrivacySettingsScreen> {
  bool _profileVisibility = true;
  bool _showEmail = false;
  bool _showPhone = false;
  bool _allowMessages = true;
  bool _shareLocation = true;
  bool _dataCollection = true;
  bool _marketingEmails = false;
  bool _pushNotifications = true;

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    
    return EnhancedPageLayout(
      title: 'إعدادات الخصوصية',
      showBackButton: true,
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Profile Privacy Section
            _ProfilePrivacySection(),
            const SizedBox(height: 16),
            
            // Data & Location Section
            _DataLocationSection(),
            const SizedBox(height: 16),
            
            // Communication Section
            _CommunicationSection(),
            const SizedBox(height: 16),
            
            // Data Management Section
            _DataManagementSection(),
          ],
        ),
      ),
    );
  }

  Widget _ProfilePrivacySection() {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'خصوصية الملف الشخصي',
            style: AppTextStyles.font18Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          
          _PrivacyToggleItem(
            title: 'إظهار الملف الشخصي',
            subtitle: 'السماح للآخرين برؤية ملفك الشخصي',
            value: _profileVisibility,
            onChanged: (value) {
              setState(() {
                _profileVisibility = value;
              });
            },
          ),
          
          _PrivacyToggleItem(
            title: 'إظهار البريد الإلكتروني',
            subtitle: 'عرض بريدك الإلكتروني في الملف الشخصي',
            value: _showEmail,
            onChanged: (value) {
              setState(() {
                _showEmail = value;
              });
            },
          ),
          
          _PrivacyToggleItem(
            title: 'إظهار رقم الهاتف',
            subtitle: 'عرض رقم هاتفك في الملف الشخصي',
            value: _showPhone,
            onChanged: (value) {
              setState(() {
                _showPhone = value;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _DataLocationSection() {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'البيانات والموقع',
            style: AppTextStyles.font18Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          
          _PrivacyToggleItem(
            title: 'مشاركة الموقع',
            subtitle: 'السماح للتطبيق بالوصول إلى موقعك',
            value: _shareLocation,
            onChanged: (value) {
              setState(() {
                _shareLocation = value;
              });
            },
          ),
          
          _PrivacyToggleItem(
            title: 'جمع البيانات التحليلية',
            subtitle: 'مساعدتنا في تحسين التطبيق من خلال البيانات المجهولة',
            value: _dataCollection,
            onChanged: (value) {
              setState(() {
                _dataCollection = value;
              });
            },
          ),
          
          _PrivacyActionItem(
            icon: Icons.download_outlined,
            title: 'تحميل بياناتي',
            subtitle: 'احصل على نسخة من جميع بياناتك',
            onTap: () {
              _showDataDownloadDialog();
            },
          ),
        ],
      ),
    );
  }

  Widget _CommunicationSection() {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'التواصل',
            style: AppTextStyles.font18Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          
          _PrivacyToggleItem(
            title: 'السماح بالرسائل',
            subtitle: 'السماح للمضيفين والضيوف بإرسال رسائل لك',
            value: _allowMessages,
            onChanged: (value) {
              setState(() {
                _allowMessages = value;
              });
            },
          ),
          
          _PrivacyToggleItem(
            title: 'الإشعارات الفورية',
            subtitle: 'تلقي إشعارات فورية للرسائل والحجوزات',
            value: _pushNotifications,
            onChanged: (value) {
              setState(() {
                _pushNotifications = value;
              });
            },
          ),
          
          _PrivacyToggleItem(
            title: 'رسائل التسويق',
            subtitle: 'تلقي رسائل بريد إلكتروني حول العروض والأخبار',
            value: _marketingEmails,
            onChanged: (value) {
              setState(() {
                _marketingEmails = value;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _DataManagementSection() {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إدارة البيانات',
            style: AppTextStyles.font18Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          
          _PrivacyActionItem(
            icon: Icons.history_outlined,
            title: 'مسح سجل البحث',
            subtitle: 'حذف جميع عمليات البحث السابقة',
            onTap: () {
              _showClearSearchHistoryDialog();
            },
          ),
          
          _PrivacyActionItem(
            icon: Icons.cached_outlined,
            title: 'مسح البيانات المؤقتة',
            subtitle: 'حذف الملفات المؤقتة والصور المحفوظة',
            onTap: () {
              _showClearCacheDialog();
            },
          ),
          
          _PrivacyActionItem(
            icon: Icons.block_outlined,
            title: 'المستخدمون المحظورون',
            subtitle: 'إدارة قائمة المستخدمين المحظورين',
            onTap: () {
              // Navigate to blocked users
            },
          ),
        ],
      ),
    );
  }

  Widget _PrivacyToggleItem({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTextStyles.font16Regular.copyWith(
                    color: context.primaryTextColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: AppTextStyles.font12Regular.copyWith(
                    color: context.secondaryTextColor,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: context.accentColor,
          ),
        ],
      ),
    );
  }

  Widget _PrivacyActionItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 4),
        child: Row(
          children: [
            Icon(
              icon,
              color: context.secondaryTextColor,
              size: 24,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTextStyles.font16Regular.copyWith(
                      color: context.primaryTextColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: AppTextStyles.font12Regular.copyWith(
                      color: context.secondaryTextColor,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: context.secondaryTextColor,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  void _showDataDownloadDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تحميل البيانات'),
        content: const Text(
          'سيتم إرسال نسخة من جميع بياناتك إلى بريدك الإلكتروني خلال 24 ساعة.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // Implement data download request
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم طلب تحميل البيانات بنجاح'),
                ),
              );
            },
            child: const Text('تأكيد'),
          ),
        ],
      ),
    );
  }

  void _showClearSearchHistoryDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مسح سجل البحث'),
        content: const Text('هل تريد حذف جميع عمليات البحث السابقة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // Implement clear search history
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم مسح سجل البحث'),
                ),
              );
            },
            child: const Text('مسح'),
          ),
        ],
      ),
    );
  }

  void _showClearCacheDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مسح البيانات المؤقتة'),
        content: const Text('هل تريد حذف جميع الملفات المؤقتة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // Implement clear cache
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم مسح البيانات المؤقتة'),
                ),
              );
            },
            child: const Text('مسح'),
          ),
        ],
      ),
    );
  }
}
