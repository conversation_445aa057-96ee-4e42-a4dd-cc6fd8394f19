import 'package:flutter/material.dart';
import 'package:gather_point/core/widgets/enhanced_page_layouts.dart';
import 'package:gather_point/core/widgets/enhanced_ui_components.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/feature/reservations/data/services/reservations_api_service.dart';
import 'package:gather_point/feature/host/data/models/reservation_model.dart';
import 'package:gather_point/core/services/service_locator.dart';
import 'package:intl/intl.dart';
import 'package:cached_network_image/cached_network_image.dart';

class PreviousTripsScreen extends StatefulWidget {
  const PreviousTripsScreen({super.key});

  @override
  State<PreviousTripsScreen> createState() => _PreviousTripsScreenState();
}

class _PreviousTripsScreenState extends State<PreviousTripsScreen> {
  final ReservationsApiService _reservationsApiService = getIt<ReservationsApiService>();
  List<ReservationModel> _reservations = [];
  bool _isLoading = true;
  String? _error;
  String _selectedFilter = 'all';

  @override
  void initState() {
    super.initState();
    _loadReservations();
  }

  Future<void> _loadReservations() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final reservations = await _reservationsApiService.getUserReservations(
        status: _selectedFilter == 'all' ? null : _selectedFilter,
      );
      
      if (mounted) {
        setState(() {
          _reservations = reservations;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return EnhancedPageLayout(
      title: 'الرحلات السابقة',
      showBackButton: true,
      body: Column(
        children: [
          // Filter Tabs
          _buildFilterTabs(),
          
          // Content
          Expanded(
            child: _buildContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterTabs() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          _buildFilterTab('all', 'الكل'),
          const SizedBox(width: 8),
          _buildFilterTab('confirmed', 'مؤكدة'),
          const SizedBox(width: 8),
          _buildFilterTab('pending', 'في الانتظار'),
          const SizedBox(width: 8),
          _buildFilterTab('cancelled', 'ملغية'),
        ],
      ),
    );
  }

  Widget _buildFilterTab(String value, String label) {
    final isSelected = _selectedFilter == value;
    
    return Expanded(
      child: GestureDetector(
        onTap: () {
          setState(() {
            _selectedFilter = value;
          });
          _loadReservations();
        },
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
          decoration: BoxDecoration(
            color: isSelected ? context.accentColor : Colors.transparent,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: isSelected ? context.accentColor : context.secondaryTextColor.withValues(alpha: 0.3),
            ),
          ),
          child: Text(
            label,
            textAlign: TextAlign.center,
            style: AppTextStyles.font12SemiBold.copyWith(
              color: isSelected ? Colors.black : context.primaryTextColor,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red.shade300,
            ),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ في تحميل الرحلات',
              style: AppTextStyles.font16SemiBold.copyWith(
                color: context.primaryTextColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              style: AppTextStyles.font14Regular.copyWith(
                color: context.secondaryTextColor,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadReservations,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (_reservations.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.travel_explore_outlined,
              size: 64,
              color: context.secondaryTextColor.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد رحلات سابقة',
              style: AppTextStyles.font18Bold.copyWith(
                color: context.primaryTextColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'ابدأ رحلتك الأولى واستكشف الأماكن المميزة',
              style: AppTextStyles.font14Regular.copyWith(
                color: context.secondaryTextColor,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadReservations,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _reservations.length,
        itemBuilder: (context, index) {
          final reservation = _reservations[index];
          return _buildReservationCard(reservation);
        },
      ),
    );
  }

  Widget _buildReservationCard(ReservationModel reservation) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: EnhancedCard(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Property Image and Basic Info
            Row(
              children: [
                // Property Image
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Container(
                    width: 80,
                    height: 80,
                    color: Colors.grey.shade200,
                    child: reservation.item?.image != null
                        ? CachedNetworkImage(
                            imageUrl: reservation.item!.image!,
                            fit: BoxFit.cover,
                            placeholder: (context, url) => Container(
                              color: Colors.grey.shade300,
                              child: const Icon(Icons.image),
                            ),
                            errorWidget: (context, url, error) => Container(
                              color: Colors.grey.shade300,
                              child: const Icon(Icons.broken_image),
                            ),
                          )
                        : Icon(
                            Icons.home_outlined,
                            size: 32,
                            color: Colors.grey.shade400,
                          ),
                  ),
                ),
                const SizedBox(width: 12),
                
                // Property Details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        reservation.item?.title ?? 'عقار غير محدد',
                        style: AppTextStyles.font16SemiBold.copyWith(
                          color: context.primaryTextColor,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _formatDateRange(reservation.reservationFrom, reservation.reservationTo),
                        style: AppTextStyles.font14Regular.copyWith(
                          color: context.secondaryTextColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      _buildStatusChip(reservation.confirmed),
                    ],
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            // Reservation Details
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: context.cardColor.withValues(alpha: 0.5),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  _buildDetailRow('تاريخ الحجز', _formatDate(reservation.createdAt)),
                  const SizedBox(height: 8),
                  _buildDetailRow('مدة الإقامة', _calculateDuration(reservation.reservationFrom, reservation.reservationTo)),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(bool confirmed) {
    final color = confirmed ? Colors.green : Colors.orange;
    final text = confirmed ? 'مؤكدة' : 'في الانتظار';
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        text,
        style: AppTextStyles.font12SemiBold.copyWith(color: color),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: AppTextStyles.font12Regular.copyWith(
            color: context.secondaryTextColor,
          ),
        ),
        Text(
          value,
          style: AppTextStyles.font12SemiBold.copyWith(
            color: context.primaryTextColor,
          ),
        ),
      ],
    );
  }

  String _formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return DateFormat('dd/MM/yyyy').format(date);
    } catch (e) {
      return dateString;
    }
  }

  String _formatDateRange(String fromDate, String toDate) {
    try {
      final from = DateTime.parse(fromDate);
      final to = DateTime.parse(toDate);
      return '${DateFormat('dd/MM').format(from)} - ${DateFormat('dd/MM/yyyy').format(to)}';
    } catch (e) {
      return '$fromDate - $toDate';
    }
  }

  String _calculateDuration(String fromDate, String toDate) {
    try {
      final from = DateTime.parse(fromDate);
      final to = DateTime.parse(toDate);
      final duration = to.difference(from).inDays;
      return duration == 1 ? 'يوم واحد' : '$duration أيام';
    } catch (e) {
      return 'غير محدد';
    }
  }
}
