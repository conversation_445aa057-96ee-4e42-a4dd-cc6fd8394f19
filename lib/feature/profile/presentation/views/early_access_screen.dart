import 'package:flutter/material.dart';
import 'package:gather_point/core/widgets/enhanced_page_layouts.dart';
import 'package:gather_point/core/widgets/enhanced_ui_components.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/feature/profile/data/services/early_access_api_service.dart';
import 'package:gather_point/feature/profile/data/models/early_access_model.dart';
import 'package:gather_point/core/services/service_locator.dart';

class EarlyAccessScreen extends StatefulWidget {
  const EarlyAccessScreen({super.key});

  @override
  State<EarlyAccessScreen> createState() => _EarlyAccessScreenState();
}

class _EarlyAccessScreenState extends State<EarlyAccessScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  final EarlyAccessApiService _earlyAccessApiService = getIt<EarlyAccessApiService>();
  
  List<EarlyAccessFeatureModel> _allFeatures = [];
  List<MyEarlyAccessFeatureModel> _myFeatures = [];
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final allFeaturesResult = await _earlyAccessApiService.getAllFeatures();
      final myFeaturesResult = await _earlyAccessApiService.getMyFeatures();
      
      if (mounted) {
        setState(() {
          _allFeatures = allFeaturesResult;
          _myFeatures = myFeaturesResult;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return EnhancedPageLayout(
      title: 'مميزات برنامج الوصول المبكر',
      showBackButton: true,
      body: Column(
        children: [
          // Header Info
          Container(
            margin: const EdgeInsets.all(16),
            child: EnhancedCard(
              child: Column(
                children: [
                  Row(
                    children: [
                      Container(
                        width: 50,
                        height: 50,
                        decoration: BoxDecoration(
                          color: context.accentColor.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(25),
                        ),
                        child: Icon(
                          Icons.science_outlined,
                          color: context.accentColor,
                          size: 28,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'برنامج الوصول المبكر',
                              style: AppTextStyles.font16SemiBold.copyWith(
                                color: context.primaryTextColor,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'جرب الميزات الجديدة قبل الجميع',
                              style: AppTextStyles.font12Regular.copyWith(
                                color: context.secondaryTextColor,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'انضم إلى برنامج الوصول المبكر لتجربة أحدث الميزات والمساعدة في تطوير التطبيق من خلال ملاحظاتك.',
                    style: AppTextStyles.font14Regular.copyWith(
                      color: context.primaryTextColor,
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // Tab Bar
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: context.cardColor,
              borderRadius: BorderRadius.circular(25),
              border: Border.all(color: context.secondaryTextColor.withValues(alpha: 0.2)),
            ),
            child: TabBar(
              controller: _tabController,
              indicator: BoxDecoration(
                color: context.accentColor,
                borderRadius: BorderRadius.circular(25),
              ),
              labelColor: Colors.black,
              unselectedLabelColor: context.primaryTextColor,
              labelStyle: AppTextStyles.font14SemiBold,
              unselectedLabelStyle: AppTextStyles.font14Regular,
              dividerColor: Colors.transparent,
              tabs: [
                Tab(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Text('جميع الميزات'),
                      if (_allFeatures.isNotEmpty) ...[
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: Colors.black.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Text(
                            '${_allFeatures.length}',
                            style: AppTextStyles.font12Bold.copyWith(
                              color: Colors.black,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                Tab(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Text('ميزاتي'),
                      if (_myFeatures.isNotEmpty) ...[
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: context.accentColor,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Text(
                            '${_myFeatures.length}',
                            style: AppTextStyles.font12Bold.copyWith(
                              color: Colors.black,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Tab Views
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildAllFeaturesTab(),
                _buildMyFeaturesTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAllFeaturesTab() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return _buildErrorWidget();
    }

    if (_allFeatures.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.science_outlined,
              size: 64,
              color: context.secondaryTextColor.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد ميزات متاحة حالياً',
              style: AppTextStyles.font18Bold.copyWith(
                color: context.primaryTextColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'تحقق مرة أخرى لاحقاً للحصول على ميزات جديدة',
              style: AppTextStyles.font14Regular.copyWith(
                color: context.secondaryTextColor,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _allFeatures.length,
        itemBuilder: (context, index) {
          final feature = _allFeatures[index];
          return _buildFeatureCard(feature, showEnrollButton: true);
        },
      ),
    );
  }

  Widget _buildMyFeaturesTab() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_myFeatures.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inbox_outlined,
              size: 64,
              color: context.secondaryTextColor.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'لم تنضم لأي ميزات بعد',
              style: AppTextStyles.font18Bold.copyWith(
                color: context.primaryTextColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'انتقل إلى تبويب "جميع الميزات" للانضمام إلى الميزات المتاحة',
              style: AppTextStyles.font14Regular.copyWith(
                color: context.secondaryTextColor,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => _tabController.animateTo(0),
              child: const Text('استكشف الميزات'),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _myFeatures.length,
        itemBuilder: (context, index) {
          final feature = _myFeatures[index];
          return _buildMyFeatureCard(feature);
        },
      ),
    );
  }

  Widget _buildMyFeatureCard(MyEarlyAccessFeatureModel feature) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: EnhancedCard(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                // Icon
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: _getStatusColor(feature.status).withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(25),
                  ),
                  child: Icon(
                    _getFeatureIcon(feature.icon),
                    color: _getStatusColor(feature.status),
                    size: 28,
                  ),
                ),
                const SizedBox(width: 16),

                // Feature Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        feature.name,
                        style: AppTextStyles.font16SemiBold.copyWith(
                          color: context.primaryTextColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: _getStatusColor(feature.status),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          feature.statusDisplay,
                          style: AppTextStyles.font12SemiBold.copyWith(
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Description
            Text(
              feature.description,
              style: AppTextStyles.font14Regular.copyWith(
                color: context.primaryTextColor,
              ),
            ),

            const SizedBox(height: 12),

            // Progress Bar (if applicable)
            if (feature.progressPercentage > 0) ...[
              Row(
                children: [
                  Expanded(
                    child: LinearProgressIndicator(
                      value: feature.progressPercentage / 100,
                      backgroundColor: context.secondaryTextColor.withValues(alpha: 0.2),
                      valueColor: AlwaysStoppedAnimation(_getStatusColor(feature.status)),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '${feature.progressPercentage}%',
                    style: AppTextStyles.font12SemiBold.copyWith(
                      color: context.secondaryTextColor,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
            ],

            // Enrollment info
            if (feature.enrolledAt != null) ...[
              Row(
                children: [
                  Icon(Icons.access_time, size: 16, color: context.secondaryTextColor),
                  const SizedBox(width: 4),
                  Text(
                    'انضممت في ${feature.enrolledAt}',
                    style: AppTextStyles.font12Regular.copyWith(
                      color: context.secondaryTextColor,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
            ],

            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => _showMyFeatureFeedbackDialog(feature),
                    child: const Text('تقييم'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => _unenrollFromMyFeature(feature),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.red,
                      side: const BorderSide(color: Colors.red),
                    ),
                    child: const Text('إلغاء الاشتراك'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureCard(EarlyAccessFeatureModel feature, {required bool showEnrollButton}) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: EnhancedCard(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                // Icon
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: _getStatusColor(feature.status).withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(25),
                  ),
                  child: Icon(
                    _getFeatureIcon(feature.icon),
                    color: _getStatusColor(feature.status),
                    size: 28,
                  ),
                ),
                const SizedBox(width: 16),
                
                // Feature Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              feature.name,
                              style: AppTextStyles.font16SemiBold.copyWith(
                                color: context.primaryTextColor,
                              ),
                            ),
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: _getStatusColor(feature.status),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              feature.statusDisplay,
                              style: AppTextStyles.font12SemiBold.copyWith(
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      if (feature.isPremium)
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: Colors.amber.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            'مميز',
                            style: AppTextStyles.font10Bold.copyWith(
                              color: Colors.amber.shade700,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            // Description
            Text(
              feature.description,
              style: AppTextStyles.font14Regular.copyWith(
                color: context.primaryTextColor,
              ),
            ),
            
            const SizedBox(height: 12),
            
            // Progress Bar (if applicable)
            if (feature.progressPercentage > 0) ...[
              Row(
                children: [
                  Expanded(
                    child: LinearProgressIndicator(
                      value: feature.progressPercentage / 100,
                      backgroundColor: context.secondaryTextColor.withValues(alpha: 0.2),
                      valueColor: AlwaysStoppedAnimation(_getStatusColor(feature.status)),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '${feature.progressPercentage}%',
                    style: AppTextStyles.font12SemiBold.copyWith(
                      color: context.secondaryTextColor,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
            ],
            
            // Stats
            Row(
              children: [
                if (feature.enrollmentCount > 0) ...[
                  Icon(Icons.people_outline, size: 16, color: context.secondaryTextColor),
                  const SizedBox(width: 4),
                  Text(
                    '${feature.enrollmentCount} مشترك',
                    style: AppTextStyles.font12Regular.copyWith(
                      color: context.secondaryTextColor,
                    ),
                  ),
                  const SizedBox(width: 16),
                ],
                if (feature.averageRating > 0) ...[
                  Icon(Icons.star_outline, size: 16, color: Colors.amber),
                  const SizedBox(width: 4),
                  Text(
                    '${feature.averageRating}',
                    style: AppTextStyles.font12Regular.copyWith(
                      color: context.secondaryTextColor,
                    ),
                  ),
                  const SizedBox(width: 16),
                ],
                if (feature.estimatedReleaseDate != null) ...[
                  Icon(Icons.schedule_outlined, size: 16, color: context.secondaryTextColor),
                  const SizedBox(width: 4),
                  Text(
                    feature.estimatedReleaseDate!,
                    style: AppTextStyles.font12Regular.copyWith(
                      color: context.secondaryTextColor,
                    ),
                  ),
                ],
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Action Buttons
            if (showEnrollButton) ...[
              if (feature.userEnrollment != null) ...[
                // Already enrolled
                Row(
                  children: [
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        decoration: BoxDecoration(
                          color: Colors.green.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.check_circle, color: Colors.green, size: 20),
                            const SizedBox(width: 8),
                            Text(
                              'مشترك',
                              style: AppTextStyles.font14SemiBold.copyWith(
                                color: Colors.green,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    OutlinedButton(
                      onPressed: () => _showFeedbackDialog(feature),
                      child: const Text('تقييم'),
                    ),
                  ],
                ),
              ] else if (feature.canEnroll) ...[
                // Can enroll
                ElevatedButton(
                  onPressed: () => _enrollInFeature(feature),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: context.accentColor,
                    foregroundColor: Colors.black,
                    minimumSize: const Size(double.infinity, 48),
                  ),
                  child: const Text('انضم الآن'),
                ),
              ] else ...[
                // Cannot enroll
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                    color: context.secondaryTextColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    'غير متاح للانضمام',
                    style: AppTextStyles.font14Regular.copyWith(
                      color: context.secondaryTextColor,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ] else ...[
              // My features tab - show feedback and unenroll options
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => _showFeedbackDialog(feature),
                      child: const Text('تقييم'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => _unenrollFromFeature(feature),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.red,
                        side: const BorderSide(color: Colors.red),
                      ),
                      child: const Text('إلغاء الاشتراك'),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red.shade300,
          ),
          const SizedBox(height: 16),
          Text(
            'حدث خطأ في تحميل البيانات',
            style: AppTextStyles.font16SemiBold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _error!,
            style: AppTextStyles.font14Regular.copyWith(
              color: context.secondaryTextColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadData,
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'development':
        return Colors.orange;
      case 'beta':
        return Colors.blue;
      case 'coming_soon':
        return Colors.green;
      case 'released':
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }

  IconData _getFeatureIcon(String? icon) {
    switch (icon) {
      case 'ai':
        return Icons.psychology_outlined;
      case 'analytics':
        return Icons.analytics_outlined;
      case 'automation':
        return Icons.auto_awesome_outlined;
      case 'integration':
        return Icons.integration_instructions_outlined;
      case 'security':
        return Icons.security_outlined;
      default:
        return Icons.science_outlined;
    }
  }

  Future<void> _enrollInFeature(EarlyAccessFeatureModel feature) async {
    try {
      await _earlyAccessApiService.enrollInFeature(feature.id);
      _loadData(); // Refresh data
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم الانضمام إلى ${feature.name} بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ: ${e.toString()}')),
        );
      }
    }
  }

  Future<void> _unenrollFromFeature(EarlyAccessFeatureModel feature) async {
    try {
      await _earlyAccessApiService.unenrollFromFeature(feature.id);
      _loadData(); // Refresh data
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تم إلغاء الاشتراك من ${feature.name}')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ: ${e.toString()}')),
        );
      }
    }
  }

  Future<void> _unenrollFromMyFeature(MyEarlyAccessFeatureModel feature) async {
    try {
      await _earlyAccessApiService.unenrollFromFeature(feature.id);
      _loadData(); // Refresh data

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تم إلغاء الاشتراك من ${feature.name}')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ: ${e.toString()}')),
        );
      }
    }
  }

  void _showMyFeatureFeedbackDialog(MyEarlyAccessFeatureModel feature) {
    final feedbackController = TextEditingController(
      text: feature.feedback ?? '',
    );
    int selectedRating = feature.rating ?? 0;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text('تقييم ${feature.name}'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Rating
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(5, (index) {
                  return IconButton(
                    onPressed: () => setState(() => selectedRating = index + 1),
                    icon: Icon(
                      index < selectedRating ? Icons.star : Icons.star_border,
                      color: Colors.amber,
                    ),
                  );
                }),
              ),
              const SizedBox(height: 16),

              // Feedback
              TextField(
                controller: feedbackController,
                maxLines: 4,
                decoration: const InputDecoration(
                  hintText: 'شاركنا رأيك في هذه الميزة...',
                  border: OutlineInputBorder(),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                try {
                  await _earlyAccessApiService.submitFeedback(
                    feature.id,
                    feedbackController.text,
                    selectedRating > 0 ? selectedRating : null,
                  );

                  if (mounted) {
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('تم إرسال التقييم بنجاح'),
                        backgroundColor: Colors.green,
                      ),
                    );
                    _loadData(); // Refresh data
                  }
                } catch (e) {
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('خطأ: ${e.toString()}')),
                    );
                  }
                }
              },
              child: const Text('إرسال'),
            ),
          ],
        ),
      ),
    );
  }

  void _showFeedbackDialog(EarlyAccessFeatureModel feature) {
    final feedbackController = TextEditingController(
      text: feature.userEnrollment?.feedback ?? '',
    );
    int selectedRating = feature.userEnrollment?.rating ?? 0;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text('تقييم ${feature.name}'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Rating
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(5, (index) {
                  return IconButton(
                    onPressed: () => setState(() => selectedRating = index + 1),
                    icon: Icon(
                      index < selectedRating ? Icons.star : Icons.star_border,
                      color: Colors.amber,
                    ),
                  );
                }),
              ),
              const SizedBox(height: 16),
              
              // Feedback
              TextField(
                controller: feedbackController,
                maxLines: 4,
                decoration: const InputDecoration(
                  hintText: 'شاركنا رأيك في هذه الميزة...',
                  border: OutlineInputBorder(),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                try {
                  await _earlyAccessApiService.submitFeedback(
                    feature.id,
                    feedbackController.text,
                    selectedRating > 0 ? selectedRating : null,
                  );
                  
                  if (mounted) {
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('تم إرسال التقييم بنجاح'),
                        backgroundColor: Colors.green,
                      ),
                    );
                    _loadData(); // Refresh data
                  }
                } catch (e) {
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('خطأ: ${e.toString()}')),
                    );
                  }
                }
              },
              child: const Text('إرسال'),
            ),
          ],
        ),
      ),
    );
  }
}
