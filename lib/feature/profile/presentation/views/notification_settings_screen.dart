import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/core/widgets/enhanced_page_layouts.dart';
import 'package:gather_point/core/widgets/enhanced_ui_components.dart';
import 'package:gather_point/generated/l10n.dart';

class NotificationSettingsScreen extends StatefulWidget {
  const NotificationSettingsScreen({super.key});

  @override
  State<NotificationSettingsScreen> createState() => _NotificationSettingsScreenState();
}

class _NotificationSettingsScreenState extends State<NotificationSettingsScreen> {
  bool _pushNotifications = true;
  bool _emailNotifications = false;
  bool _smsNotifications = true;
  bool _bookingNotifications = true;
  bool _promotionalNotifications = false;
  bool _reviewNotifications = true;
  bool _messageNotifications = true;
  bool _soundEnabled = true;
  bool _vibrationEnabled = true;

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    
    return EnhancedPageLayout(
      title: 'إعدادات الإشعارات',
      showBackButton: true,
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // General Notifications Section
            _GeneralNotificationsSection(),
            const SizedBox(height: 16),
            
            // Notification Types Section
            _NotificationTypesSection(),
            const SizedBox(height: 16),
            
            // Notification Behavior Section
            _NotificationBehaviorSection(),
          ],
        ),
      ),
    );
  }

  Widget _GeneralNotificationsSection() {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الإشعارات العامة',
            style: AppTextStyles.font18Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          
          _NotificationToggleItem(
            title: 'الإشعارات الفورية',
            subtitle: 'تلقي إشعارات فورية على الجهاز',
            value: _pushNotifications,
            onChanged: (value) {
              setState(() {
                _pushNotifications = value;
              });
            },
          ),
          
          _NotificationToggleItem(
            title: 'إشعارات البريد الإلكتروني',
            subtitle: 'تلقي إشعارات عبر البريد الإلكتروني',
            value: _emailNotifications,
            onChanged: (value) {
              setState(() {
                _emailNotifications = value;
              });
            },
          ),
          
          _NotificationToggleItem(
            title: 'الرسائل النصية',
            subtitle: 'تلقي إشعارات عبر الرسائل النصية',
            value: _smsNotifications,
            onChanged: (value) {
              setState(() {
                _smsNotifications = value;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _NotificationTypesSection() {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'أنواع الإشعارات',
            style: AppTextStyles.font18Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          
          _NotificationToggleItem(
            title: 'إشعارات الحجوزات',
            subtitle: 'تأكيد الحجز، الإلغاء، والتذكيرات',
            value: _bookingNotifications,
            onChanged: (value) {
              setState(() {
                _bookingNotifications = value;
              });
            },
          ),
          
          _NotificationToggleItem(
            title: 'إشعارات الرسائل',
            subtitle: 'رسائل جديدة من المضيفين والضيوف',
            value: _messageNotifications,
            onChanged: (value) {
              setState(() {
                _messageNotifications = value;
              });
            },
          ),
          
          _NotificationToggleItem(
            title: 'إشعارات التقييمات',
            subtitle: 'تقييمات جديدة وطلبات التقييم',
            value: _reviewNotifications,
            onChanged: (value) {
              setState(() {
                _reviewNotifications = value;
              });
            },
          ),
          
          _NotificationToggleItem(
            title: 'الإشعارات الترويجية',
            subtitle: 'عروض خاصة وأخبار التطبيق',
            value: _promotionalNotifications,
            onChanged: (value) {
              setState(() {
                _promotionalNotifications = value;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _NotificationBehaviorSection() {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'سلوك الإشعارات',
            style: AppTextStyles.font18Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          
          _NotificationToggleItem(
            title: 'الصوت',
            subtitle: 'تشغيل صوت عند وصول الإشعارات',
            value: _soundEnabled,
            onChanged: (value) {
              setState(() {
                _soundEnabled = value;
              });
            },
          ),
          
          _NotificationToggleItem(
            title: 'الاهتزاز',
            subtitle: 'اهتزاز الجهاز عند وصول الإشعارات',
            value: _vibrationEnabled,
            onChanged: (value) {
              setState(() {
                _vibrationEnabled = value;
              });
            },
          ),
          
          const SizedBox(height: 16),
          
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Colors.blue,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'يمكنك أيضاً إدارة إعدادات الإشعارات من إعدادات النظام',
                    style: AppTextStyles.font12Regular.copyWith(
                      color: Colors.blue,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _NotificationToggleItem({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTextStyles.font16Regular.copyWith(
                    color: context.primaryTextColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: AppTextStyles.font12Regular.copyWith(
                    color: context.secondaryTextColor,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: context.accentColor,
          ),
        ],
      ),
    );
  }
}
