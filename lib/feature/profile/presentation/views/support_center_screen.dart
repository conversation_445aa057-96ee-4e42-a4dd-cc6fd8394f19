import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/theme/theme_extensions.dart';
import 'package:gather_point/core/widgets/enhanced_card.dart';
import 'package:gather_point/core/services/service_locator.dart';
import 'package:gather_point/feature/profile/data/services/support_api_service.dart';
import 'package:gather_point/feature/profile/data/models/support_models.dart';

class SupportCenterScreen extends StatefulWidget {
  const SupportCenterScreen({super.key});

  @override
  State<SupportCenterScreen> createState() => _SupportCenterScreenState();
}

class _SupportCenterScreenState extends State<SupportCenterScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final SupportApiService _supportApiService = getIt<SupportApiService>();

  List<SupportFaq> _faqs = [];
  List<SupportTicket> _tickets = [];
  List<SupportCategory> _categories = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    
    try {
      final results = await Future.wait([
        _supportApiService.getFaqs(),
        _supportApiService.getTickets(),
        _supportApiService.getCategories(),
      ]);
      
      setState(() {
        _faqs = results[0] as List<SupportFaq>;
        _tickets = results[1] as List<SupportTicket>;
        _categories = results[2] as List<SupportCategory>;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ: ${e.toString()}')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('مركز الدعم'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'الأسئلة الشائعة'),
            Tab(text: 'تذاكر الدعم'),
            Tab(text: 'تذكرة جديدة'),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildFaqsTab(),
                _buildTicketsTab(),
                _buildNewTicketTab(),
              ],
            ),
    );
  }

  Widget _buildFaqsTab() {
    if (_faqs.isEmpty) {
      return const Center(
        child: Text('لا توجد أسئلة شائعة متاحة'),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _faqs.length,
      itemBuilder: (context, index) {
        final faq = _faqs[index];
        return _buildFaqCard(faq);
      },
    );
  }

  Widget _buildFaqCard(SupportFaq faq) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: EnhancedCard(
        child: ExpansionTile(
          title: Text(
            faq.question,
            style: AppTextStyles.font16SemiBold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          children: [
            Padding(
              padding: const EdgeInsets.all(16),
              child: Text(
                faq.answer,
                style: AppTextStyles.font14Regular.copyWith(
                  color: context.secondaryTextColor,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTicketsTab() {
    if (_tickets.isEmpty) {
      return const Center(
        child: Text('لا توجد تذاكر دعم'),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _tickets.length,
      itemBuilder: (context, index) {
        final ticket = _tickets[index];
        return _buildTicketCard(ticket);
      },
    );
  }

  Widget _buildTicketCard(SupportTicket ticket) {
    Color statusColor = _getStatusColor(ticket.status);
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: EnhancedCard(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      ticket.subject,
                      style: AppTextStyles.font16SemiBold.copyWith(
                        color: context.primaryTextColor,
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: statusColor,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      ticket.statusDisplay,
                      style: AppTextStyles.font12SemiBold.copyWith(
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                ticket.description,
                style: AppTextStyles.font14Regular.copyWith(
                  color: context.secondaryTextColor,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(Icons.category, size: 16, color: context.secondaryTextColor),
                  const SizedBox(width: 4),
                  Text(
                    ticket.categoryDisplay,
                    style: AppTextStyles.font12Regular.copyWith(
                      color: context.secondaryTextColor,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Icon(Icons.access_time, size: 16, color: context.secondaryTextColor),
                  const SizedBox(width: 4),
                  Text(
                    ticket.createdAt,
                    style: AppTextStyles.font12Regular.copyWith(
                      color: context.secondaryTextColor,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNewTicketTab() {
    return _NewTicketForm(
      categories: _categories,
      onTicketCreated: () {
        _loadData();
        _tabController.animateTo(1); // Switch to tickets tab
      },
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'open':
        return Colors.blue;
      case 'in_progress':
        return Colors.orange;
      case 'resolved':
        return Colors.green;
      case 'closed':
        return Colors.grey;
      default:
        return Colors.blue;
    }
  }
}

class _NewTicketForm extends StatefulWidget {
  final List<SupportCategory> categories;
  final VoidCallback onTicketCreated;

  const _NewTicketForm({
    required this.categories,
    required this.onTicketCreated,
  });

  @override
  State<_NewTicketForm> createState() => _NewTicketFormState();
}

class _NewTicketFormState extends State<_NewTicketForm> {
  final _formKey = GlobalKey<FormState>();
  final _subjectController = TextEditingController();
  final _descriptionController = TextEditingController();
  final SupportApiService _supportApiService = getIt<SupportApiService>();
  
  String? _selectedCategory;
  String _selectedPriority = 'medium';
  bool _isSubmitting = false;

  @override
  void dispose() {
    _subjectController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _submitTicket() async {
    if (!_formKey.currentState!.validate() || _selectedCategory == null) {
      return;
    }

    setState(() => _isSubmitting = true);

    try {
      await _supportApiService.createTicket(
        subject: _subjectController.text,
        description: _descriptionController.text,
        category: _selectedCategory!,
        priority: _selectedPriority,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إنشاء التذكرة بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
        
        _subjectController.clear();
        _descriptionController.clear();
        setState(() {
          _selectedCategory = null;
          _selectedPriority = 'medium';
        });
        
        widget.onTicketCreated();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ: ${e.toString()}')),
        );
      }
    } finally {
      setState(() => _isSubmitting = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إنشاء تذكرة دعم جديدة',
              style: AppTextStyles.font20Bold.copyWith(
                color: context.primaryTextColor,
              ),
            ),
            const SizedBox(height: 24),
            
            // Category Dropdown
            DropdownButtonFormField<String>(
              value: _selectedCategory,
              decoration: const InputDecoration(
                labelText: 'الفئة',
                border: OutlineInputBorder(),
              ),
              items: widget.categories.map((category) {
                return DropdownMenuItem(
                  value: category.value,
                  child: Text(category.label),
                );
              }).toList(),
              onChanged: (value) {
                setState(() => _selectedCategory = value);
              },
              validator: (value) {
                if (value == null) {
                  return 'يرجى اختيار الفئة';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            
            // Subject Field
            TextFormField(
              controller: _subjectController,
              decoration: const InputDecoration(
                labelText: 'الموضوع',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى إدخال الموضوع';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            
            // Description Field
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'الوصف',
                border: OutlineInputBorder(),
              ),
              maxLines: 5,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى إدخال الوصف';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            
            // Priority Dropdown
            DropdownButtonFormField<String>(
              value: _selectedPriority,
              decoration: const InputDecoration(
                labelText: 'الأولوية',
                border: OutlineInputBorder(),
              ),
              items: const [
                DropdownMenuItem(value: 'low', child: Text('منخفض')),
                DropdownMenuItem(value: 'medium', child: Text('متوسط')),
                DropdownMenuItem(value: 'high', child: Text('عالي')),
                DropdownMenuItem(value: 'urgent', child: Text('عاجل')),
              ],
              onChanged: (value) {
                setState(() => _selectedPriority = value!);
              },
            ),
            const SizedBox(height: 24),
            
            // Submit Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isSubmitting ? null : _submitTicket,
                child: _isSubmitting
                    ? const CircularProgressIndicator()
                    : const Text('إرسال التذكرة'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
