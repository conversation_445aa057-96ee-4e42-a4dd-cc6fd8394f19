import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/core/widgets/enhanced_page_layouts.dart';
import 'package:gather_point/core/widgets/enhanced_ui_components.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:url_launcher/url_launcher.dart';

class LegalScreen extends StatelessWidget {
  const LegalScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    
    return EnhancedPageLayout(
      title: 'الشروط القانونية',
      showBackButton: true,
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Legal Documents Section
            _LegalDocumentsSection(),
            const SizedBox(height: 16),
            
            // Company Information Section
            _CompanyInfoSection(),
            const SizedBox(height: 16),
            
            // Licenses Section
            _LicensesSection(),
            const SizedBox(height: 16),
            
            // Contact Legal Section
            _ContactLegalSection(),
          ],
        ),
      ),
    );
  }
}

class _LegalDocumentsSection extends StatelessWidget {
  const _LegalDocumentsSection();

  @override
  Widget build(BuildContext context) {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الوثائق القانونية',
            style: AppTextStyles.font18Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          
          _LegalItem(
            icon: Icons.description_outlined,
            title: 'شروط الاستخدام',
            subtitle: 'الشروط والأحكام الخاصة باستخدام التطبيق',
            onTap: () {
              _openDocument(context, 'terms');
            },
          ),
          
          _LegalItem(
            icon: Icons.privacy_tip_outlined,
            title: 'سياسة الخصوصية',
            subtitle: 'كيف نجمع ونستخدم ونحمي بياناتك',
            onTap: () {
              _openDocument(context, 'privacy');
            },
          ),
          
          _LegalItem(
            icon: Icons.cookie_outlined,
            title: 'سياسة ملفات تعريف الارتباط',
            subtitle: 'معلومات حول استخدام ملفات تعريف الارتباط',
            onTap: () {
              _openDocument(context, 'cookies');
            },
          ),
          
          _LegalItem(
            icon: Icons.gavel_outlined,
            title: 'اتفاقية المضيف',
            subtitle: 'الشروط الخاصة بالمضيفين وتأجير العقارات',
            onTap: () {
              _openDocument(context, 'host-agreement');
            },
          ),
          
          _LegalItem(
            icon: Icons.security_outlined,
            title: 'سياسة الأمان',
            subtitle: 'إرشادات الأمان والحماية للمستخدمين',
            onTap: () {
              _openDocument(context, 'safety');
            },
          ),
        ],
      ),
    );
  }

  void _openDocument(BuildContext context, String documentType) {
    // Navigate to document viewer or open web page
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => _DocumentViewerScreen(
          title: _getDocumentTitle(documentType),
          documentType: documentType,
        ),
      ),
    );
  }

  String _getDocumentTitle(String documentType) {
    switch (documentType) {
      case 'terms':
        return 'شروط الاستخدام';
      case 'privacy':
        return 'سياسة الخصوصية';
      case 'cookies':
        return 'سياسة ملفات تعريف الارتباط';
      case 'host-agreement':
        return 'اتفاقية المضيف';
      case 'safety':
        return 'سياسة الأمان';
      default:
        return 'وثيقة قانونية';
    }
  }
}

class _CompanyInfoSection extends StatelessWidget {
  const _CompanyInfoSection();

  @override
  Widget build(BuildContext context) {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'معلومات الشركة',
            style: AppTextStyles.font18Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          
          _InfoRow(
            title: 'اسم الشركة',
            value: 'شركة نقطة التجمع للتقنية',
          ),
          
          _InfoRow(
            title: 'رقم السجل التجاري',
            value: '1010123456',
          ),
          
          _InfoRow(
            title: 'الرقم الضريبي',
            value: '300123456789003',
          ),
          
          _InfoRow(
            title: 'العنوان',
            value: 'جدة، المملكة العربية السعودية',
          ),
          
          _InfoRow(
            title: 'البريد الإلكتروني',
            value: '<EMAIL>',
          ),
        ],
      ),
    );
  }
}

class _InfoRow extends StatelessWidget {
  final String title;
  final String value;

  const _InfoRow({
    required this.title,
    required this.value,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              title,
              style: AppTextStyles.font14SemiBold.copyWith(
                color: context.primaryTextColor,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTextStyles.font14Regular.copyWith(
                color: context.secondaryTextColor,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _LicensesSection extends StatelessWidget {
  const _LicensesSection();

  @override
  Widget build(BuildContext context) {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'التراخيص والشهادات',
            style: AppTextStyles.font18Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          
          _LegalItem(
            icon: Icons.verified_outlined,
            title: 'ترخيص وزارة التجارة',
            subtitle: 'مرخص من وزارة التجارة والاستثمار',
            onTap: () {
              // Show license details
            },
          ),
          
          _LegalItem(
            icon: Icons.security_outlined,
            title: 'شهادة الأمان السيبراني',
            subtitle: 'معتمد من الهيئة الوطنية للأمن السيبراني',
            onTap: () {
              // Show certificate details
            },
          ),
          
          _LegalItem(
            icon: Icons.account_balance_outlined,
            title: 'ترخيص البنك المركزي',
            subtitle: 'مرخص لتقديم خدمات الدفع الإلكتروني',
            onTap: () {
              // Show license details
            },
          ),
        ],
      ),
    );
  }
}

class _ContactLegalSection extends StatelessWidget {
  const _ContactLegalSection();

  @override
  Widget build(BuildContext context) {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'التواصل القانوني',
            style: AppTextStyles.font18Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          
          Text(
            'للاستفسارات القانونية أو الشكاوى، يمكنك التواصل معنا:',
            style: AppTextStyles.font14Regular.copyWith(
              color: context.secondaryTextColor,
            ),
          ),
          
          const SizedBox(height: 16),
          
          _ContactItem(
            icon: Icons.email_outlined,
            title: 'البريد الإلكتروني القانوني',
            value: '<EMAIL>',
            onTap: () async {
              final Uri emailUri = Uri(
                scheme: 'mailto',
                path: '<EMAIL>',
                query: 'subject=استفسار قانوني',
              );
              if (await canLaunchUrl(emailUri)) {
                await launchUrl(emailUri);
              }
            },
          ),
          
          _ContactItem(
            icon: Icons.phone_outlined,
            title: 'الخط الساخن القانوني',
            value: '+966 11 234 5679',
            onTap: () async {
              final Uri phoneUri = Uri(scheme: 'tel', path: '+966112345679');
              if (await canLaunchUrl(phoneUri)) {
                await launchUrl(phoneUri);
              }
            },
          ),
          
          const SizedBox(height: 16),
          
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Colors.blue,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'جميع الوثائق القانونية متوفرة باللغتين العربية والإنجليزية',
                    style: AppTextStyles.font12Regular.copyWith(
                      color: Colors.blue,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _ContactItem extends StatelessWidget {
  final IconData icon;
  final String title;
  final String value;
  final VoidCallback onTap;

  const _ContactItem({
    required this.icon,
    required this.title,
    required this.value,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Row(
          children: [
            Icon(
              icon,
              color: context.accentColor,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTextStyles.font14SemiBold.copyWith(
                      color: context.primaryTextColor,
                    ),
                  ),
                  Text(
                    value,
                    style: AppTextStyles.font12Regular.copyWith(
                      color: context.secondaryTextColor,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _LegalItem extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subtitle;
  final VoidCallback onTap;

  const _LegalItem({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 4),
        child: Row(
          children: [
            Icon(
              icon,
              color: context.secondaryTextColor,
              size: 24,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTextStyles.font16Regular.copyWith(
                      color: context.primaryTextColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: AppTextStyles.font12Regular.copyWith(
                      color: context.secondaryTextColor,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: context.secondaryTextColor,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }
}

class _DocumentViewerScreen extends StatelessWidget {
  final String title;
  final String documentType;

  const _DocumentViewerScreen({
    required this.title,
    required this.documentType,
  });

  @override
  Widget build(BuildContext context) {
    return EnhancedPageLayout(
      title: title,
      showBackButton: true,
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              _getDocumentContent(documentType),
              style: AppTextStyles.font14Regular.copyWith(
                color: context.primaryTextColor,
                height: 1.6,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getDocumentContent(String documentType) {
    switch (documentType) {
      case 'terms':
        return '''
شروط الاستخدام

آخر تحديث: يناير 2024

مرحباً بك في تطبيق Gather Point. باستخدامك لهذا التطبيق، فإنك توافق على الالتزام بهذه الشروط والأحكام.

1. قبول الشروط
بالوصول إلى التطبيق أو استخدامه، فإنك توافق على الالتزام بهذه الشروط والأحكام وجميع القوانين واللوائح المعمول بها.

2. استخدام التطبيق
يمكنك استخدام التطبيق للأغراض المشروعة فقط ووفقاً لهذه الشروط.

3. حساب المستخدم
أنت مسؤول عن الحفاظ على سرية معلومات حسابك وكلمة المرور.

4. المحتوى
أنت مسؤول عن أي محتوى تنشره على التطبيق.

5. الخصوصية
نحن نحترم خصوصيتك ونحمي بياناتك الشخصية وفقاً لسياسة الخصوصية الخاصة بنا.

للمزيد من التفاصيل، يرجى زيارة موقعنا الإلكتروني.
''';
      case 'privacy':
        return '''
سياسة الخصوصية

آخر تحديث: يناير 2024

نحن في Gather Point نقدر خصوصيتك ونلتزم بحماية بياناتك الشخصية.

1. المعلومات التي نجمعها
- معلومات الحساب (الاسم، البريد الإلكتروني، رقم الهاتف)
- معلومات الاستخدام
- معلومات الموقع (بإذنك)

2. كيف نستخدم المعلومات
- تقديم وتحسين خدماتنا
- التواصل معك
- ضمان الأمان

3. مشاركة المعلومات
لا نبيع أو نؤجر معلوماتك الشخصية لأطراف ثالثة.

4. أمان البيانات
نستخدم تدابير أمنية متقدمة لحماية بياناتك.

5. حقوقك
يمكنك الوصول إلى بياناتك وتعديلها أو حذفها في أي وقت.

للمزيد من التفاصيل، يرجى التواصل معنا.
''';
      default:
        return 'محتوى الوثيقة غير متوفر حالياً. يرجى المحاولة لاحقاً أو التواصل مع الدعم الفني.';
    }
  }
}
