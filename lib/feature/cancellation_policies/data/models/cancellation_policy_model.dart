class CancellationPolicyModel {
  final int id;
  final String nameEn;
  final String nameAr;
  final String name;
  final String descriptionEn;
  final String descriptionAr;
  final String description;
  final String policyType;
  final String durationType;
  final int cancellationWindowHours;
  final double refundPercentage;
  final int? bookingWindowHours;
  final int? minimumNoticeHours;
  final bool serviceFeeRefundable;
  final bool cleaningFeeRefundable;
  final bool isActive;
  final int order;
  final String createdAt;
  final String updatedAt;

  const CancellationPolicyModel({
    required this.id,
    required this.nameEn,
    required this.nameAr,
    required this.name,
    required this.descriptionEn,
    required this.descriptionAr,
    required this.description,
    required this.policyType,
    required this.durationType,
    required this.cancellationWindowHours,
    required this.refundPercentage,
    this.bookingWindowHours,
    this.minimumNoticeHours,
    required this.serviceFeeRefundable,
    required this.cleaningFeeRefundable,
    required this.isActive,
    required this.order,
    required this.createdAt,
    required this.updatedAt,
  });

  factory CancellationPolicyModel.fromJson(Map<String, dynamic> json) {
    return CancellationPolicyModel(
      id: json['id'] ?? 0,
      nameEn: json['name_en'] ?? '',
      nameAr: json['name_ar'] ?? '',
      name: json['name'] ?? '',
      descriptionEn: json['description_en'] ?? '',
      descriptionAr: json['description_ar'] ?? '',
      description: json['description'] ?? '',
      policyType: json['policy_type'] ?? '',
      durationType: json['duration_type'] ?? '',
      cancellationWindowHours: json['cancellation_window_hours'] ?? 0,
      refundPercentage: double.tryParse(json['refund_percentage']?.toString() ?? '0') ?? 0.0,
      bookingWindowHours: json['booking_window_hours'],
      minimumNoticeHours: json['minimum_notice_hours'],
      serviceFeeRefundable: json['service_fee_refundable'] ?? false,
      cleaningFeeRefundable: json['cleaning_fee_refundable'] ?? false,
      isActive: json['is_active'] ?? false,
      order: json['order'] ?? 0,
      createdAt: json['created_at'] ?? '',
      updatedAt: json['updated_at'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name_en': nameEn,
      'name_ar': nameAr,
      'name': name,
      'description_en': descriptionEn,
      'description_ar': descriptionAr,
      'description': description,
      'policy_type': policyType,
      'duration_type': durationType,
      'cancellation_window_hours': cancellationWindowHours,
      'refund_percentage': refundPercentage,
      'booking_window_hours': bookingWindowHours,
      'minimum_notice_hours': minimumNoticeHours,
      'service_fee_refundable': serviceFeeRefundable,
      'cleaning_fee_refundable': cleaningFeeRefundable,
      'is_active': isActive,
      'order': order,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }

  CancellationPolicyModel copyWith({
    int? id,
    String? nameEn,
    String? nameAr,
    String? name,
    String? descriptionEn,
    String? descriptionAr,
    String? description,
    String? policyType,
    String? durationType,
    int? cancellationWindowHours,
    double? refundPercentage,
    int? bookingWindowHours,
    int? minimumNoticeHours,
    bool? serviceFeeRefundable,
    bool? cleaningFeeRefundable,
    bool? isActive,
    int? order,
    String? createdAt,
    String? updatedAt,
  }) {
    return CancellationPolicyModel(
      id: id ?? this.id,
      nameEn: nameEn ?? this.nameEn,
      nameAr: nameAr ?? this.nameAr,
      name: name ?? this.name,
      descriptionEn: descriptionEn ?? this.descriptionEn,
      descriptionAr: descriptionAr ?? this.descriptionAr,
      description: description ?? this.description,
      policyType: policyType ?? this.policyType,
      durationType: durationType ?? this.durationType,
      cancellationWindowHours: cancellationWindowHours ?? this.cancellationWindowHours,
      refundPercentage: refundPercentage ?? this.refundPercentage,
      bookingWindowHours: bookingWindowHours ?? this.bookingWindowHours,
      minimumNoticeHours: minimumNoticeHours ?? this.minimumNoticeHours,
      serviceFeeRefundable: serviceFeeRefundable ?? this.serviceFeeRefundable,
      cleaningFeeRefundable: cleaningFeeRefundable ?? this.cleaningFeeRefundable,
      isActive: isActive ?? this.isActive,
      order: order ?? this.order,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Get formatted cancellation window text
  String get formattedCancellationWindow {
    if (cancellationWindowHours < 24) {
      return '$cancellationWindowHours hours';
    } else {
      final days = (cancellationWindowHours / 24).round();
      return '$days day${days > 1 ? 's' : ''}';
    }
  }

  /// Get policy type display name
  String get policyTypeDisplayName {
    switch (policyType) {
      case 'flexible_short':
        return 'مرنة - قصيرة المدى';
      case 'moderate_short':
        return 'معتدلة - قصيرة المدى';
      case 'strict_short':
        return 'مشددة - قصيرة المدى';
      case 'moderate_long':
        return 'معتدلة - طويلة المدى';
      case 'strict_long':
        return 'مشددة - طويلة المدى';
      case 'custom':
        return 'مخصصة';
      default:
        return 'غير معروف';
    }
  }

  /// Check if policy applies to given booking duration
  bool appliesTo(int bookingDurationDays) {
    switch (durationType) {
      case 'short':
        return bookingDurationDays <= 28;
      case 'long':
        return bookingDurationDays > 28;
      case 'both':
        return true;
      default:
        return true;
    }
  }

  /// Get formatted policy description based on type
  String get formattedDescription {
    switch (policyType) {
      case 'flexible_short':
        return 'استرداد كامل قبل يوم واحد من الوصول';
      case 'moderate_short':
        return 'استرداد كامل قبل يومين من الوصول';
      case 'strict_short':
        return 'استرداد كامل قبل 5 أيام من الوصول';
      case 'moderate_long':
        return 'استرداد 50% من المبلغ في خلال 7 أيام من تاريخ الوصول';
      case 'strict_long':
        return 'استرداد كامل إذا تم الإلغاء خلال 3 أيام من الحجز وقبل 14 يوم من الوصول';
      default:
        return description;
    }
  }
}
