import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:dio/dio.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/core/services/service_locator.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:hive/hive.dart';

class CategoryView extends StatefulWidget {
  final int serviceCategoryId;
  final String categoryTitle;

  const CategoryView({
    super.key,
    required this.serviceCategoryId,
    required this.categoryTitle,
  });

  @override
  State<CategoryView> createState() => _CategoryViewState();
}

class _CategoryViewState extends State<CategoryView> {
  List<Map<String, dynamic>> _items = [];
  List<Map<String, dynamic>> _subCategories = [];
  String _selectedTitle = '';
  int _selectedCategoryId = 0;
  bool _isLoading = true;
  bool _loadingSubcats = true;
  String? _errorMessage;
  late final DioConsumer _dioConsumer;

  @override
  void initState() {
    super.initState();
    _dioConsumer = DioConsumer(
      dio: getIt<Dio>(),
      profileBox: getIt<Box<UserEntity>>(),
    );
    _selectedTitle = widget.categoryTitle;
    _selectedCategoryId = widget.serviceCategoryId;
    _fetchSubCategories();
    _fetchItems();
  }

  Future<void> _fetchSubCategories() async {
    try {
      final response = await _dioConsumer.get(
        '/api/service_categories/list',
        queryParameters: {'service_category_id': widget.serviceCategoryId},
      );

      if (response['data'] != null) {
        setState(() {
          _subCategories = List<Map<String, dynamic>>.from(response['data']);
          _loadingSubcats = false;
        });
      }
    } catch (e) {
      debugPrint("Error fetching subcategories: $e");
    }
  }

  Future<void> _fetchItems() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final response = await _dioConsumer.get(
        '/api/items/list',
        queryParameters: {'service_category_id': _selectedCategoryId},
      );

      if (response['data'] != null) {
        setState(() {
          _items = List<Map<String, dynamic>>.from(response['data']);
          _isLoading = false;
        });
      } else {
        _handleError('No data available');
      }
    } catch (e) {
      _handleError('Failed to load items: ${e.toString()}');
    }
  }

  void _handleError(String message) {
    setState(() {
      _errorMessage = message;
      _isLoading = false;
    });
  }

  Widget _buildSubCategoryChip(Map<String, dynamic> category) {
    final isSelected = _selectedCategoryId == category['id'];
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 6),
      child: ChoiceChip(
        label: Text(category['title']),
        selected: isSelected,
        labelStyle: TextStyle(
          color: isSelected ? Colors.black : Colors.white,
        ),
        backgroundColor: Colors.grey[800],
        selectedColor: Colors.white,
        onSelected: (selected) {
          if (selected) {
            setState(() {
              _selectedCategoryId = category['id'];
              _selectedTitle = category['title'];
            });
            _fetchItems();
          }
        },
      ),
    );
  }

  Widget _buildSubCategoryList() {
    if (_loadingSubcats) {
      return const SizedBox(
        height: 50,
        child: Center(child: CircularProgressIndicator()),
      );
    }

    return SizedBox(
      height: 50,
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        children: [
          // Main category chip
          _buildSubCategoryChip({
            'id': widget.serviceCategoryId,
            'title': widget.categoryTitle,
          }),
          // Subcategories
          ..._subCategories.map((cat) => _buildSubCategoryChip(cat)),
        ],
      ),
    );
  }

  Widget _buildCategoryAppBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.black,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.4),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
        child: Row(
          children: [
            IconButton(
              icon: const Icon(Icons.arrow_back, color: Colors.white),
              onPressed: () => Navigator.of(context).pop(),
            ),
            Expanded(
              child: Text(
                _selectedTitle,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFacilityItem(Map<String, dynamic> facility) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        CachedNetworkImage(
          imageUrl: facility['icon'],
          width: 32,
          height: 32,
          placeholder: (context, url) => const CircularProgressIndicator(),
          errorWidget: (context, url, error) => const Icon(Icons.error),
        ),
        const SizedBox(height: 4),
        Text(
          '${facility['count']}',
          style: const TextStyle(color: Colors.white, fontSize: 12),
        ),
        Text(
          facility['title'],
          style: const TextStyle(color: Colors.white54, fontSize: 10),
        ),
      ],
    );
  }

  Widget _buildImageGallery(List<dynamic> gallery) {
    return SizedBox(
      height: 180,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        itemCount: gallery.length,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        separatorBuilder: (context, index) => const SizedBox(width: 12),
        itemBuilder: (context, index) {
          final item = gallery[index];
          return ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: CachedNetworkImage(
              imageUrl: item['image'],
              width: 240,
              fit: BoxFit.cover,
              placeholder: (context, url) => Container(
                color: Colors.grey[800],
                width: 240,
              ),
              errorWidget: (context, url, error) => const Icon(Icons.error),
            ),
          );
        },
      ),
    );
  }

  Widget _buildListItem(Map<String, dynamic> item) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (item['gallery']?.isNotEmpty ?? false)
            _buildImageGallery(item['gallery']),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item['title'] ?? 'Untitled',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 12),
                if (item['facilities']?.isNotEmpty ?? false)
                  Wrap(
                    spacing: 24,
                    runSpacing: 16,
                    children: (item['facilities'] as List)
                        .map((f) => _buildFacilityItem(f))
                        .toList(),
                  ),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '${item['price']?.toStringAsFixed(2) ?? '0.00'} ر.س',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    IconButton(
                      icon: Icon(
                        item['favorite'] ?? false
                            ? Icons.favorite
                            : Icons.favorite_border,
                        color: item['favorite'] ?? false
                            ? Colors.red
                            : Colors.white54,
                      ),
                      onPressed: () => _toggleFavorite(item),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _toggleFavorite(Map<String, dynamic> item) async {
    try {
      final response = await _dioConsumer.post(
        '/api/favorite/set',
        data: {'service_category_item_id': item['id']},
      );

      if (response['status'] == true) {
        setState(() {
          item['favorite'] = !(item['favorite'] ?? false);
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to update favorite: ${e.toString()}')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Stack(
          children: [
            Column(
              children: [
                _buildCategoryAppBar(),
                _buildSubCategoryList(),
                Expanded(
                  child: RefreshIndicator(
                    onRefresh: _fetchItems,
                    child: _buildContent(),
                  ),
                ),
              ],
            ),
            if (_isLoading)
              const Positioned.fill(
                child: ColoredBox(
                  color: Colors.black54,
                  child: Center(
                    child: CircularProgressIndicator(),
                  ),
                ),
              ),
            if (_errorMessage != null && !_isLoading)
              Positioned.fill(
                child: ColoredBox(
                  color: Colors.black54,
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.error_outline,
                            color: Colors.white, size: 48),
                        const SizedBox(height: 16),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 24),
                          child: Text(
                            _errorMessage!,
                            textAlign: TextAlign.center,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                            ),
                          ),
                        ),
                        const SizedBox(height: 24),
                        ElevatedButton(
                          onPressed: _fetchItems,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.white,
                            foregroundColor: Colors.black,
                          ),
                          child: const Text('Try Again'),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildContent() {
    if (_errorMessage != null) {
      return Center(
        child: Text(
          _errorMessage!,
          style: const TextStyle(color: Colors.white),
        ),
      );
    }

    return ListView.builder(
      physics: const AlwaysScrollableScrollPhysics(),
      itemCount: _items.length,
      itemBuilder: (context, index) => _buildListItem(_items[index]),
    );
  }
}
