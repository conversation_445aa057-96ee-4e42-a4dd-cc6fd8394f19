import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/feature/host/data/services/my_listings_api_service.dart';
import 'package:gather_point/feature/host/data/models/my_listing_model.dart';
import 'package:gather_point/feature/host/data/models/listing_stats_model.dart';
import 'package:gather_point/feature/host/data/models/listing_filter_model.dart';

part 'my_listings_state.dart';

class MyListingsCubit extends Cubit<MyListingsState> {
  final MyListingsApiService _apiService;

  MyListingsCubit(this._apiService) : super(MyListingsInitial());

  // Current data
  List<MyListingModel> _allListings = [];
  List<MyListingModel> _filteredListings = [];
  ListingStatsModel? _stats;
  ListingFilterModel _currentFilter = ListingFilterModel.allListings;
  int _currentPage = 1;
  bool _hasMoreData = true;
  final int _pageSize = 20;

  // Getters
  List<MyListingModel> get allListings => _allListings;
  List<MyListingModel> get filteredListings => _filteredListings;
  ListingStatsModel? get stats => _stats;
  ListingFilterModel get currentFilter => _currentFilter;
  bool get hasMoreData => _hasMoreData;

  /// Safe emit that checks if cubit is not closed
  void _safeEmit(MyListingsState state) {
    if (!isClosed) {
      emit(state);
    }
  }

  /// Load initial data (listings and stats)
  Future<void> loadInitialData() async {
    _safeEmit(MyListingsLoading());

    try {
      // Load stats and initial listings in parallel
      final futures = await Future.wait([
        _apiService.getListingStats(),
        _apiService.getMyListings(
          page: 1,
          limit: _pageSize,
          status: _currentFilter.status,
          category: _currentFilter.category,
          sortBy: _currentFilter.sortBy,
          sortOrder: _currentFilter.sortOrder,
          search: _currentFilter.search,
        ),
      ]);

      _stats = futures[0] as ListingStatsModel;
      final listings = futures[1] as List<MyListingModel>;

      _allListings = listings;
      _filteredListings = listings;
      _currentPage = 1;
      _hasMoreData = listings.length >= _pageSize;

      _safeEmit(MyListingsLoaded(
        listings: _filteredListings,
        stats: _stats!,
        filter: _currentFilter,
        hasMoreData: _hasMoreData,
      ));
    } catch (e) {
      _safeEmit(MyListingsError(e.toString()));
    }
  }

  /// Refresh listings data
  Future<void> refreshListings() async {
    try {
      _safeEmit(MyListingsRefreshing(_filteredListings, _stats, _currentFilter));

      final futures = await Future.wait([
        _apiService.getListingStats(),
        _apiService.getMyListings(
          page: 1,
          limit: _pageSize,
          status: _currentFilter.status,
          category: _currentFilter.category,
          sortBy: _currentFilter.sortBy,
          sortOrder: _currentFilter.sortOrder,
          search: _currentFilter.search,
        ),
      ]);

      _stats = futures[0] as ListingStatsModel;
      final listings = futures[1] as List<MyListingModel>;

      _allListings = listings;
      _filteredListings = listings;
      _currentPage = 1;
      _hasMoreData = listings.length >= _pageSize;

      _safeEmit(MyListingsLoaded(
        listings: _filteredListings,
        stats: _stats!,
        filter: _currentFilter,
        hasMoreData: _hasMoreData,
      ));
    } catch (e) {
      _safeEmit(MyListingsError(e.toString()));
    }
  }

  /// Load more listings (pagination)
  Future<void> loadMoreListings() async {
    if (!_hasMoreData || state is MyListingsLoadingMore) return;

    try {
      _safeEmit(MyListingsLoadingMore(_filteredListings, _stats, _currentFilter));

      final newListings = await _apiService.getMyListings(
        page: _currentPage + 1,
        limit: _pageSize,
        status: _currentFilter.status,
        category: _currentFilter.category,
        sortBy: _currentFilter.sortBy,
        sortOrder: _currentFilter.sortOrder,
        search: _currentFilter.search,
      );

      if (newListings.isNotEmpty) {
        _allListings.addAll(newListings);
        _filteredListings.addAll(newListings);
        _currentPage++;
        _hasMoreData = newListings.length >= _pageSize;
      } else {
        _hasMoreData = false;
      }

      _safeEmit(MyListingsLoaded(
        listings: _filteredListings,
        stats: _stats!,
        filter: _currentFilter,
        hasMoreData: _hasMoreData,
      ));
    } catch (e) {
      _safeEmit(MyListingsError(e.toString()));
    }
  }

  /// Apply filters to listings
  Future<void> applyFilter(ListingFilterModel filter) async {
    if (_currentFilter == filter) return;

    _currentFilter = filter;

    try {
      _safeEmit(MyListingsFiltering(_filteredListings, _stats, _currentFilter));

      final filteredListings = await _apiService.getMyListings(
        page: 1,
        limit: _pageSize,
        status: filter.status,
        category: filter.category,
        sortBy: filter.sortBy,
        sortOrder: filter.sortOrder,
        search: filter.search,
      );

      _filteredListings = filteredListings;
      _currentPage = 1;
      _hasMoreData = filteredListings.length >= _pageSize;

      _safeEmit(MyListingsLoaded(
        listings: _filteredListings,
        stats: _stats!,
        filter: _currentFilter,
        hasMoreData: _hasMoreData,
      ));
    } catch (e) {
      _safeEmit(MyListingsError(e.toString()));
    }
  }

  /// Search listings
  Future<void> searchListings(String query) async {
    final searchFilter = _currentFilter.copyWith(search: query.trim().isEmpty ? null : query);
    await applyFilter(searchFilter);
  }

  /// Clear all filters
  Future<void> clearFilters() async {
    await applyFilter(ListingFilterModel.allListings);
  }

  /// Toggle listing status
  Future<void> toggleListingStatus(int listingId) async {
    try {
      final success = await _apiService.toggleListingStatus(listingId);
      
      if (success) {
        // Update local data
        final listingIndex = _filteredListings.indexWhere((listing) => listing.id == listingId);
        if (listingIndex != -1) {
          final listing = _filteredListings[listingIndex];
          final newStatus = listing.status == 'active' ? 'inactive' : 'active';
          final updatedListing = listing.copyWith(status: newStatus);
          
          _filteredListings[listingIndex] = updatedListing;
          
          // Update in all listings as well
          final allIndex = _allListings.indexWhere((listing) => listing.id == listingId);
          if (allIndex != -1) {
            _allListings[allIndex] = updatedListing;
          }

          _safeEmit(MyListingsLoaded(
            listings: _filteredListings,
            stats: _stats!,
            filter: _currentFilter,
            hasMoreData: _hasMoreData,
          ));

          // Refresh stats
          _refreshStats();
        }
      }
    } catch (e) {
      _safeEmit(MyListingsError(e.toString()));
    }
  }

  /// Delete listing
  Future<void> deleteListing(int listingId) async {
    try {
      final success = await _apiService.deleteListing(listingId);
      
      if (success) {
        // Remove from local data
        _filteredListings.removeWhere((listing) => listing.id == listingId);
        _allListings.removeWhere((listing) => listing.id == listingId);

        _safeEmit(MyListingsLoaded(
          listings: _filteredListings,
          stats: _stats!,
          filter: _currentFilter,
          hasMoreData: _hasMoreData,
        ));

        // Refresh stats
        _refreshStats();
      }
    } catch (e) {
      _safeEmit(MyListingsError(e.toString()));
    }
  }

  /// Duplicate listing
  Future<void> duplicateListing(int listingId) async {
    try {
      final duplicatedListing = await _apiService.duplicateListing(listingId);
      
      // Add to local data
      _allListings.insert(0, duplicatedListing);
      
      // Add to filtered listings if it matches current filter
      if (_shouldIncludeInFilter(duplicatedListing, _currentFilter)) {
        _filteredListings.insert(0, duplicatedListing);
      }

      _safeEmit(MyListingsLoaded(
        listings: _filteredListings,
        stats: _stats!,
        filter: _currentFilter,
        hasMoreData: _hasMoreData,
      ));

      // Refresh stats
      _refreshStats();
    } catch (e) {
      _safeEmit(MyListingsError(e.toString()));
    }
  }

  /// Bulk update status
  Future<void> bulkUpdateStatus(List<int> listingIds, String status) async {
    try {
      final success = await _apiService.bulkUpdateStatus(listingIds, status);
      
      if (success) {
        // Update local data
        for (final id in listingIds) {
          final filteredIndex = _filteredListings.indexWhere((listing) => listing.id == id);
          if (filteredIndex != -1) {
            _filteredListings[filteredIndex] = _filteredListings[filteredIndex].copyWith(status: status);
          }
          
          final allIndex = _allListings.indexWhere((listing) => listing.id == id);
          if (allIndex != -1) {
            _allListings[allIndex] = _allListings[allIndex].copyWith(status: status);
          }
        }

        _safeEmit(MyListingsLoaded(
          listings: _filteredListings,
          stats: _stats!,
          filter: _currentFilter,
          hasMoreData: _hasMoreData,
        ));

        // Refresh stats
        _refreshStats();
      }
    } catch (e) {
      _safeEmit(MyListingsError(e.toString()));
    }
  }

  /// Bulk delete listings
  Future<void> bulkDeleteListings(List<int> listingIds) async {
    try {
      final success = await _apiService.bulkDeleteListings(listingIds);
      
      if (success) {
        // Remove from local data
        _filteredListings.removeWhere((listing) => listingIds.contains(listing.id));
        _allListings.removeWhere((listing) => listingIds.contains(listing.id));

        _safeEmit(MyListingsLoaded(
          listings: _filteredListings,
          stats: _stats!,
          filter: _currentFilter,
          hasMoreData: _hasMoreData,
        ));

        // Refresh stats
        _refreshStats();
      }
    } catch (e) {
      _safeEmit(MyListingsError(e.toString()));
    }
  }

  /// Refresh stats only
  Future<void> _refreshStats() async {
    try {
      _stats = await _apiService.getListingStats();
      
      if (state is MyListingsLoaded) {
        _safeEmit(MyListingsLoaded(
          listings: _filteredListings,
          stats: _stats!,
          filter: _currentFilter,
          hasMoreData: _hasMoreData,
        ));
      }
    } catch (e) {
      // Silently fail stats refresh
    }
  }

  /// Check if listing should be included in current filter
  bool _shouldIncludeInFilter(MyListingModel listing, ListingFilterModel filter) {
    if (filter.status != null && listing.status != filter.status) {
      return false;
    }
    
    if (filter.category != null && listing.category.id.toString() != filter.category) {
      return false;
    }
    
    if (filter.search != null && filter.search!.isNotEmpty) {
      final searchLower = filter.search!.toLowerCase();
      if (!listing.title.toLowerCase().contains(searchLower) &&
          !listing.content.toLowerCase().contains(searchLower)) {
        return false;
      }
    }
    
    return true;
  }

  /// Get listing by ID
  MyListingModel? getListingById(int id) {
    try {
      return _allListings.firstWhere((listing) => listing.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Update listing in local data (after edit)
  void updateListingLocally(MyListingModel updatedListing) {
    final filteredIndex = _filteredListings.indexWhere((listing) => listing.id == updatedListing.id);
    if (filteredIndex != -1) {
      _filteredListings[filteredIndex] = updatedListing;
    }
    
    final allIndex = _allListings.indexWhere((listing) => listing.id == updatedListing.id);
    if (allIndex != -1) {
      _allListings[allIndex] = updatedListing;
    }

    if (state is MyListingsLoaded) {
      _safeEmit(MyListingsLoaded(
        listings: _filteredListings,
        stats: _stats!,
        filter: _currentFilter,
        hasMoreData: _hasMoreData,
      ));
    }
  }

  /// Add new listing to local data (after creation)
  void addListingLocally(MyListingModel newListing) {
    _allListings.insert(0, newListing);
    
    if (_shouldIncludeInFilter(newListing, _currentFilter)) {
      _filteredListings.insert(0, newListing);
    }

    if (state is MyListingsLoaded) {
      _safeEmit(MyListingsLoaded(
        listings: _filteredListings,
        stats: _stats!,
        filter: _currentFilter,
        hasMoreData: _hasMoreData,
      ));
    }

    // Refresh stats
    _refreshStats();
  }
}
