part of 'property_creation_cubit.dart';

abstract class PropertyCreationState {}

class PropertyCreationInitial extends PropertyCreationState {}

class PropertyCreationLoading extends PropertyCreationState {}

class PropertyCreationDataLoaded extends PropertyCreationState {
  final List<ServiceCategory> categories;
  final List<FacilityModel> facilities;
  final List<PropertyTypeModel> propertyTypes;
  final List<CancellationPolicyModel> cancellationPolicies;
  final bool isEditMode;
  final PropertyItemModel? existingProperty;

  PropertyCreationDataLoaded({
    required this.categories,
    required this.facilities,
    required this.propertyTypes,
    required this.cancellationPolicies,
    this.isEditMode = false,
    this.existingProperty,
  });

  PropertyCreationDataLoaded copyWith({
    List<ServiceCategory>? categories,
    List<FacilityModel>? facilities,
    List<PropertyTypeModel>? propertyTypes,
    List<CancellationPolicyModel>? cancellationPolicies,
    bool? isEditMode,
    PropertyItemModel? existingProperty,
  }) {
    return PropertyCreationDataLoaded(
      categories: categories ?? this.categories,
      facilities: facilities ?? this.facilities,
      propertyTypes: propertyTypes ?? this.propertyTypes,
      cancellationPolicies: cancellationPolicies ?? this.cancellationPolicies,
      isEditMode: isEditMode ?? this.isEditMode,
      existingProperty: existingProperty ?? this.existingProperty,
    );
  }
}

class PropertyCreationSuccess extends PropertyCreationState {
  final PropertyItemModel property;

  PropertyCreationSuccess(this.property);
}

class PropertyCreationError extends PropertyCreationState {
  final String message;

  PropertyCreationError(this.message);
}

class PropertyGalleryUploaded extends PropertyCreationState {}

class PropertyEditDataLoaded extends PropertyCreationState {
  final PropertyItemModel property;
  final List<ServiceCategory> categories;
  final List<FacilityModel> facilities;
  final List<PropertyTypeModel> propertyTypes;
  final List<CancellationPolicyModel> cancellationPolicies;

  PropertyEditDataLoaded({
    required this.property,
    required this.categories,
    required this.facilities,
    required this.propertyTypes,
    required this.cancellationPolicies,
  });
}

class PropertyUpdateSuccess extends PropertyCreationState {
  final PropertyItemModel property;
  final String message;

  PropertyUpdateSuccess(this.property, {this.message = 'Property updated successfully'});
}

class PropertyDraftSaved extends PropertyCreationState {
  final String message;

  PropertyDraftSaved({this.message = 'Draft saved successfully'});
}
