part of 'my_listings_cubit.dart';

abstract class MyListingsState {}

class MyListingsInitial extends MyListingsState {}

class MyListingsLoading extends MyListingsState {}

class MyListingsLoaded extends MyListingsState {
  final List<MyListingModel> listings;
  final ListingStatsModel stats;
  final ListingFilterModel filter;
  final bool hasMoreData;

  MyListingsLoaded({
    required this.listings,
    required this.stats,
    required this.filter,
    required this.hasMoreData,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MyListingsLoaded &&
        other.listings.length == listings.length &&
        other.filter == filter &&
        other.hasMoreData == hasMoreData;
  }

  @override
  int get hashCode => Object.hash(listings.length, filter, hasMoreData);
}

class MyListingsRefreshing extends MyListingsState {
  final List<MyListingModel> currentListings;
  final ListingStatsModel? currentStats;
  final ListingFilterModel currentFilter;

  MyListingsRefreshing(this.currentListings, this.currentStats, this.currentFilter);
}

class MyListingsLoadingMore extends MyListingsState {
  final List<MyListingModel> currentListings;
  final ListingStatsModel? currentStats;
  final ListingFilterModel currentFilter;

  MyListingsLoadingMore(this.currentListings, this.currentStats, this.currentFilter);
}

class MyListingsFiltering extends MyListingsState {
  final List<MyListingModel> currentListings;
  final ListingStatsModel? currentStats;
  final ListingFilterModel newFilter;

  MyListingsFiltering(this.currentListings, this.currentStats, this.newFilter);
}

class MyListingsError extends MyListingsState {
  final String message;

  MyListingsError(this.message);

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MyListingsError && other.message == message;
  }

  @override
  int get hashCode => message.hashCode;
}
