import 'package:flutter/material.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/core/widgets/enhanced_page_layouts.dart';
import 'package:gather_point/feature/host/data/models/listing_analytics_model.dart';
import 'package:gather_point/feature/host/services/listing_analytics_service.dart';
import 'package:gather_point/feature/host/widgets/listing_analytics_dashboard.dart';

class ListingAnalyticsPage extends StatefulWidget {
  final int listingId;
  final String? listingTitle;

  const ListingAnalyticsPage({
    super.key,
    required this.listingId,
    this.listingTitle,
  });

  @override
  State<ListingAnalyticsPage> createState() => _ListingAnalyticsPageState();
}

class _ListingAnalyticsPageState extends State<ListingAnalyticsPage> {
  ListingAnalyticsModel? _analytics;
  bool _isLoading = true;
  String? _error;
  String _selectedPeriod = '30d';

  @override
  void initState() {
    super.initState();
    _loadAnalytics();
  }

  Future<void> _loadAnalytics() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // For now, use mock data
      // In production, you would use:
      // final service = ListingAnalyticsService(apiConsumer);
      // final analytics = await service.getListingAnalytics(widget.listingId, period: _selectedPeriod);
      
      final analytics = ListingAnalyticsService.generateMockAnalytics(widget.listingId);
      
      setState(() {
        _analytics = analytics;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _exportAnalytics() async {
    try {
      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('جاري تصدير البيانات...'),
            ],
          ),
        ),
      );

      // Simulate export delay
      await Future.delayed(const Duration(seconds: 2));

      if (mounted) {
        Navigator.pop(context); // Close loading dialog
        
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تصدير البيانات بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        Navigator.pop(context); // Close loading dialog
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تصدير البيانات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return EnhancedPageLayout(
      title: widget.listingTitle != null 
          ? '${s.analytics} - ${widget.listingTitle}'
          : s.analytics,
      body: _buildBody(s),
    );
  }

  Widget _buildBody(S s) {
    if (_isLoading) {
      return _buildLoadingState();
    }

    if (_error != null) {
      return _buildErrorState(s);
    }

    if (_analytics == null) {
      return _buildEmptyState(s);
    }

    return ListingAnalyticsDashboard(
      analytics: _analytics!,
      onPeriodChanged: () {
        _loadAnalytics();
      },
      onExport: _exportAnalytics,
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('جاري تحميل التحليلات...'),
        ],
      ),
    );
  }

  Widget _buildErrorState(S s) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red[300],
          ),
          const SizedBox(height: 16),
          Text(
            'خطأ في تحميل التحليلات',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            _error!,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _loadAnalytics,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFFEC53A),
              foregroundColor: Colors.black,
            ),
            icon: const Icon(Icons.refresh),
            label: Text(s.tryAgain),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(S s) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.analytics_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد بيانات تحليلية',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            'لم يتم العثور على بيانات تحليلية لهذا العقار',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _loadAnalytics,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFFEC53A),
              foregroundColor: Colors.black,
            ),
            icon: const Icon(Icons.refresh),
            label: Text(s.tryAgain),
          ),
        ],
      ),
    );
  }
}
