import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/widgets/enhanced_page_layouts.dart';
import 'package:gather_point/core/widgets/enhanced_ui_components.dart';
import 'package:gather_point/feature/host/presentation/cubit/property_creation_cubit.dart';
import 'package:gather_point/feature/dashboard/data/services/properties_api_service.dart';
import 'package:gather_point/feature/home/<USER>/Data%20Sources/Models/ServiceCategory.dart';
import 'package:gather_point/feature/host/data/models/property_type_model.dart';
import 'package:gather_point/feature/cancellation_policies/data/models/cancellation_policy_model.dart';
import 'package:gather_point/feature/host/utils/property_validation_helper.dart';
import 'package:gather_point/feature/host/utils/property_form_auto_save.dart';
import 'package:gather_point/feature/host/widgets/location_map_picker.dart';
import 'package:gather_point/feature/host/widgets/property_preview_dialog.dart';
import 'package:gather_point/feature/host/widgets/enhanced_image_picker.dart';
import 'package:gather_point/feature/host/services/property_edit_service.dart';
import 'package:gather_point/core/services/service_locator.dart';

class CreatePropertyPage extends StatelessWidget {
  const CreatePropertyPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) {
        try {
          return PropertyCreationCubit(
            getIt<PropertiesApiService>(),
            getIt.isRegistered<PropertyEditService>() ? getIt<PropertyEditService>() : null,
          )..loadInitialData();
        } catch (e) {
          // Fallback if services are not available
          debugPrint('Error creating PropertyCreationCubit: $e');
          return PropertyCreationCubit(
            getIt<PropertiesApiService>(),
          );
        }
      },
      child: const _CreatePropertyPageContent(),
    );
  }
}

class _CreatePropertyPageContent extends StatefulWidget {
  const _CreatePropertyPageContent();

  @override
  State<_CreatePropertyPageContent> createState() => _CreatePropertyPageContentState();
}

class _CreatePropertyPageContentState extends State<_CreatePropertyPageContent> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _weekendPriceController = TextEditingController();
  final _weekPriceController = TextEditingController();
  final _monthPriceController = TextEditingController();
  final _guestsController = TextEditingController();
  final _bedsController = TextEditingController();
  final _bathsController = TextEditingController();
  final _bookingRulesController = TextEditingController();
  final _cancellationRulesController = TextEditingController();

  ServiceCategory? _selectedCategory;
  PropertyTypeModel? _selectedPropertyType;
  CancellationPolicyModel? _selectedCancellationPolicy;
  final List<int> _selectedFacilities = [];
  File? _mainImage;
  File? _video;
  final List<File> _galleryImages = [];
  double? _latitude;
  double? _longitude;

  final ImagePicker _picker = ImagePicker();
  Timer? _autoSaveTimer;
  bool _hasUnsavedChanges = false;
  DateTime? _lastAutoSave;

  @override
  void initState() {
    super.initState();
    _loadSavedData();
    _setupAutoSave();
  }

  @override
  void dispose() {
    _autoSaveTimer?.cancel();
    _titleController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _weekendPriceController.dispose();
    _weekPriceController.dispose();
    _monthPriceController.dispose();
    _guestsController.dispose();
    _bedsController.dispose();
    _bathsController.dispose();
    _bookingRulesController.dispose();
    _cancellationRulesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return EnhancedPageLayout(
      title: s.createProperty,
      body: BlocConsumer<PropertyCreationCubit, PropertyCreationState>(
        listener: (context, state) {
          if (state is PropertyCreationSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(s.propertyCreatedSuccessfully),
                backgroundColor: Colors.green,
              ),
            );
            Navigator.of(context).pop(true); // Return true to indicate success
          } else if (state is PropertyCreationError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        builder: (context, state) {
          if (state is PropertyCreationLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          return Form(
            key: _formKey,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Basic Information Section
                  _buildSectionHeader(s.basicInformation),
                  const SizedBox(height: 16),
                  
                  _buildTextField(
                    controller: _titleController,
                    label: s.propertyTitle,
                    hint: s.enterPropertyTitle,
                    validator: PropertyValidationHelper.validateTitle,
                  ),
                  
                  const SizedBox(height: 16),
                  
                  _buildTextField(
                    controller: _descriptionController,
                    label: s.description,
                    hint: s.enterPropertyDescription,
                    maxLines: 4,
                    validator: PropertyValidationHelper.validateDescription,
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Category Selection
                  _buildCategoryDropdown(state),

                  const SizedBox(height: 16),

                  // Property Type Selection
                  _buildPropertyTypeDropdown(state),

                  const SizedBox(height: 16),

                  // Cancellation Policy Selection
                  _buildCancellationPolicyDropdown(state),

                  const SizedBox(height: 24),
                  
                  // Pricing Section
                  _buildSectionHeader(s.pricing),
                  const SizedBox(height: 16),
                  
                  Row(
                    children: [
                      Expanded(
                        child: _buildTextField(
                          controller: _priceController,
                          label: s.dailyPrice,
                          hint: '50.00',
                          keyboardType: TextInputType.number,
                          validator: PropertyValidationHelper.validateDailyPrice,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildTextField(
                          controller: _weekendPriceController,
                          label: s.weekendPrice,
                          hint: '60.00',
                          keyboardType: TextInputType.number,
                          validator: (value) => PropertyValidationHelper.validateWeekendPrice(
                            value,
                            double.tryParse(_priceController.text),
                          ),
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 16),
                  
                  Row(
                    children: [
                      Expanded(
                        child: _buildTextField(
                          controller: _weekPriceController,
                          label: s.weeklyPrice,
                          hint: '300.00',
                          keyboardType: TextInputType.number,
                          validator: (value) => PropertyValidationHelper.validateWeeklyPrice(
                            value,
                            double.tryParse(_priceController.text),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildTextField(
                          controller: _monthPriceController,
                          label: s.monthlyPrice,
                          hint: '1000.00',
                          keyboardType: TextInputType.number,
                          validator: (value) => PropertyValidationHelper.validateMonthlyPrice(
                            value,
                            double.tryParse(_priceController.text),
                          ),
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Property Details Section
                  _buildSectionHeader(s.propertyDetails),
                  const SizedBox(height: 16),
                  
                  Row(
                    children: [
                      Expanded(
                        child: _buildTextField(
                          controller: _guestsController,
                          label: s.maxGuests,
                          hint: '2',
                          keyboardType: TextInputType.number,
                          validator: (value) {
                            final guestValidation = PropertyValidationHelper.validateGuests(value);
                            if (guestValidation != null) return guestValidation;

                            // Additional validation against bedrooms
                            final guests = int.tryParse(value ?? '');
                            final bedrooms = int.tryParse(_bedsController.text);
                            return PropertyValidationHelper.validateGuestsVsBedrooms(guests, bedrooms);
                          },
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildTextField(
                          controller: _bedsController,
                          label: s.bedrooms,
                          hint: '1',
                          keyboardType: TextInputType.number,
                          validator: PropertyValidationHelper.validateBedrooms,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildTextField(
                          controller: _bathsController,
                          label: s.bathrooms,
                          hint: '1',
                          keyboardType: TextInputType.number,
                          validator: PropertyValidationHelper.validateBathrooms,
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Facilities Section
                  _buildFacilitiesSection(state),
                  
                  const SizedBox(height: 24),
                  
                  // Location Section
                  _buildLocationSection(),

                  const SizedBox(height: 24),

                  // Media Section
                  _buildMediaSection(),

                  const SizedBox(height: 24),

                  // Rules Section
                  _buildRulesSection(),
                  
                  const SizedBox(height: 32),

                  // Action Buttons
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton(
                          onPressed: state is PropertyCreationLoading ? null : _showPreview,
                          style: OutlinedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            side: const BorderSide(color: Color(0xFFFEC53A)),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Icon(Icons.preview, color: Color(0xFFFEC53A)),
                              const SizedBox(width: 8),
                              Text(
                                s.propertyPreview,
                                style: AppTextStyles.font14SemiBold.copyWith(
                                  color: const Color(0xFFFEC53A),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: EnhancedButton(
                          text: s.createProperty,
                          onPressed: state is PropertyCreationLoading ? null : _submitForm,
                          icon: Icons.add_home_rounded,
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 100), // Bottom padding
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: AppTextStyles.font18Bold.copyWith(
        color: Theme.of(context).brightness == Brightness.dark ? Colors.white : Colors.black,
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    int maxLines = 1,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      maxLines: maxLines,
      keyboardType: keyboardType,
      validator: validator,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        border: const OutlineInputBorder(),
        filled: true,
        fillColor: Colors.grey.withValues(alpha: 0.05),
      ),
    );
  }

  Widget _buildCategoryDropdown(PropertyCreationState state) {
    final s = S.of(context);

    if (state is PropertyCreationDataLoaded) {
      return DropdownButtonFormField<ServiceCategory>(
        value: _selectedCategory,
        decoration: InputDecoration(
          labelText: s.category,
          border: const OutlineInputBorder(),
          filled: true,
          fillColor: Colors.grey.withValues(alpha: 0.05),
        ),
        items: state.categories.map((category) {
          return DropdownMenuItem<ServiceCategory>(
            value: category,
            child: Text(category.title),
          );
        }).toList(),
        onChanged: (ServiceCategory? value) {
          setState(() {
            _selectedCategory = value;
          });
        },
        validator: (value) {
          if (value == null) {
            return s.pleaseSelectCategory;
          }
          return null;
        },
      );
    }

    return const CircularProgressIndicator();
  }

  Widget _buildPropertyTypeDropdown(PropertyCreationState state) {
    final s = S.of(context);

    if (state is PropertyCreationDataLoaded) {
      return DropdownButtonFormField<PropertyTypeModel>(
        value: _selectedPropertyType,
        decoration: InputDecoration(
          labelText: s.propertyType,
          border: const OutlineInputBorder(),
          filled: true,
          fillColor: Colors.grey.withValues(alpha: 0.05),
        ),
        items: state.propertyTypes.map((propertyType) {
          return DropdownMenuItem<PropertyTypeModel>(
            value: propertyType,
            child: Text(propertyType.title),
          );
        }).toList(),
        onChanged: (PropertyTypeModel? value) {
          setState(() {
            _selectedPropertyType = value;
          });
        },
        validator: (value) {
          if (value == null) {
            return s.pleaseSelectPropertyType;
          }
          return null;
        },
      );
    }

    return const CircularProgressIndicator();
  }

  Widget _buildCancellationPolicyDropdown(PropertyCreationState state) {
    final s = S.of(context);

    if (state is PropertyCreationDataLoaded) {
      return DropdownButtonFormField<CancellationPolicyModel>(
        value: _selectedCancellationPolicy,
        decoration: InputDecoration(
          labelText: s.cancellationPolicy,
          border: const OutlineInputBorder(),
          filled: true,
          fillColor: Colors.grey.withValues(alpha: 0.05),
        ),
        items: state.cancellationPolicies.map((policy) {
          return DropdownMenuItem<CancellationPolicyModel>(
            value: policy,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  policy.name,
                  style: AppTextStyles.font14SemiBold,
                ),
                Text(
                  policy.description,
                  style: AppTextStyles.font12Regular.copyWith(
                    color: Colors.grey[600],
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          );
        }).toList(),
        onChanged: (CancellationPolicyModel? value) {
          setState(() {
            _selectedCancellationPolicy = value;
          });
        },
        validator: (value) {
          if (value == null) {
            return s.pleaseSelectCancellationPolicy;
          }
          return null;
        },
      );
    }

    return const CircularProgressIndicator();
  }

  Widget _buildFacilitiesSection(PropertyCreationState state) {
    final s = S.of(context);

    if (state is PropertyCreationDataLoaded) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader(s.facilities),
          const SizedBox(height: 16),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: state.facilities.map((facility) {
              final isSelected = _selectedFacilities.contains(facility.id);
              return FilterChip(
                label: Text(facility.title),
                selected: isSelected,
                onSelected: (bool selected) {
                  setState(() {
                    if (selected) {
                      _selectedFacilities.add(facility.id);
                    } else {
                      _selectedFacilities.remove(facility.id);
                    }
                  });
                },
                selectedColor: Colors.blue.withValues(alpha: 0.2),
                checkmarkColor: Colors.blue,
              );
            }).toList(),
          ),
        ],
      );
    }

    return const SizedBox.shrink();
  }

  Widget _buildMediaSection() {
    final s = S.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader(s.media),
        const SizedBox(height: 16),

        // Main Image
        _buildImagePicker(
          title: s.mainImage,
          image: _mainImage,
          onTap: () => _pickMainImage(),
        ),

        const SizedBox(height: 16),

        // Video
        _buildVideoPicker(),

        const SizedBox(height: 16),

        // Gallery Images
        EnhancedImagePicker(
          images: _galleryImages,
          onImagesChanged: (images) {
            setState(() {
              _galleryImages.clear();
              _galleryImages.addAll(images);
            });
          },
          title: s.gallery,
          maxImages: 20,
          allowReordering: true,
          showImageCount: true,
        ),
      ],
    );
  }

  Widget _buildImagePicker({
    required String title,
    required File? image,
    required VoidCallback onTap,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: AppTextStyles.font14SemiBold,
            ),
            if (image != null)
              TextButton.icon(
                onPressed: () {
                  setState(() {
                    if (title.contains('الرئيسية')) {
                      _mainImage = null;
                    }
                  });
                },
                icon: const Icon(Icons.delete_outline, size: 16),
                label: const Text('حذف'),
                style: TextButton.styleFrom(
                  foregroundColor: Colors.red,
                ),
              ),
          ],
        ),
        const SizedBox(height: 8),
        GestureDetector(
          onTap: onTap,
          child: Container(
            height: 160,
            width: double.infinity,
            decoration: BoxDecoration(
              border: Border.all(
                color: image != null ? Colors.blue.withValues(alpha: 0.3) : Colors.grey.withValues(alpha: 0.3),
                width: image != null ? 2 : 1,
              ),
              borderRadius: BorderRadius.circular(12),
              color: Colors.grey.withValues(alpha: 0.05),
            ),
            child: image != null
                ? Stack(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(10),
                        child: Image.file(
                          image,
                          width: double.infinity,
                          height: double.infinity,
                          fit: BoxFit.cover,
                        ),
                      ),
                      Positioned(
                        bottom: 8,
                        right: 8,
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.black.withValues(alpha: 0.7),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(
                                Icons.edit,
                                color: Colors.white,
                                size: 14,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                'تغيير',
                                style: AppTextStyles.font12Regular.copyWith(
                                  color: Colors.white,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  )
                : Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.add_photo_alternate_outlined,
                        size: 48,
                        color: Colors.grey[400],
                      ),
                      const SizedBox(height: 12),
                      Text(
                        'اضغط لإضافة ${title.toLowerCase()}',
                        style: AppTextStyles.font14Regular.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'الحد الأقصى: 5 ميجابايت',
                        style: AppTextStyles.font12Regular.copyWith(
                          color: Colors.grey[500],
                        ),
                      ),
                    ],
                  ),
          ),
        ),
      ],
    );
  }

  Widget _buildVideoPicker() {
    final s = S.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          s.video,
          style: AppTextStyles.font14SemiBold,
        ),
        const SizedBox(height: 8),
        GestureDetector(
          onTap: _pickVideo,
          child: Container(
            height: 80,
            width: double.infinity,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey),
              borderRadius: BorderRadius.circular(8),
              color: Colors.grey.withValues(alpha: 0.1),
            ),
            child: _video != null
                ? Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.video_file, size: 32, color: Colors.blue),
                      const SizedBox(width: 8),
                      Text(_video!.path.split('/').last),
                    ],
                  )
                : const Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.video_call, size: 32, color: Colors.grey),
                      SizedBox(height: 4),
                      Text('اضغط لإضافة فيديو'),
                    ],
                  ),
          ),
        ),
      ],
    );
  }





  Widget _buildLocationSection() {
    final s = S.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader(s.location),
        const SizedBox(height: 16),

        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey),
            borderRadius: BorderRadius.circular(8),
            color: Colors.grey.withValues(alpha: 0.05),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Row(
                children: [
                  Icon(Icons.location_on, color: Colors.blue),
                  SizedBox(width: 8),
                  Text(
                    'موقع العقار',
                    style: AppTextStyles.font14SemiBold,
                  ),
                ],
              ),
              const SizedBox(height: 12),

              if (_latitude != null && _longitude != null)
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.check_circle, color: Colors.green, size: 20),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'تم تحديد الموقع: ${_latitude!.toStringAsFixed(6)}, ${_longitude!.toStringAsFixed(6)}',
                          style: AppTextStyles.font12Regular.copyWith(
                            color: Colors.green[700],
                          ),
                        ),
                      ),
                    ],
                  ),
                )
              else
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.orange.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.warning, color: Colors.orange, size: 20),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'لم يتم تحديد الموقع بعد',
                          style: AppTextStyles.font12Regular.copyWith(
                            color: Colors.orange[700],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

              const SizedBox(height: 16),

              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _pickLocation,
                  icon: const Icon(Icons.map),
                  label: Text(_latitude != null && _longitude != null
                      ? 'تغيير الموقع'
                      : 'اختيار الموقع'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildRulesSection() {
    final s = S.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader(s.rules),
        const SizedBox(height: 16),

        _buildTextField(
          controller: _bookingRulesController,
          label: s.bookingRules,
          hint: s.enterBookingRules,
          maxLines: 3,
        ),

        const SizedBox(height: 16),

        _buildTextField(
          controller: _cancellationRulesController,
          label: s.cancellationRules,
          hint: s.enterCancellationRules,
          maxLines: 3,
        ),
      ],
    );
  }

  Future<void> _pickMainImage() async {
    final XFile? image = await _picker.pickImage(source: ImageSource.gallery);
    if (image != null) {
      setState(() {
        _mainImage = File(image.path);
      });
    }
  }

  Future<void> _pickVideo() async {
    final XFile? video = await _picker.pickVideo(source: ImageSource.gallery);
    if (video != null) {
      setState(() {
        _video = File(video.path);
      });
    }
  }



  Future<void> _pickLocation() async {
    await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => LocationMapPicker(
          initialLatitude: _latitude,
          initialLongitude: _longitude,
          onLocationSelected: (latitude, longitude) {
            setState(() {
              _latitude = latitude;
              _longitude = longitude;
            });

            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم تحديد الموقع بنجاح'),
                backgroundColor: Colors.green,
              ),
            );
          },
        ),
      ),
    );
  }

  void _showValidationError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 4),
      ),
    );
  }

  Future<void> _showPreview() async {
    // Basic validation before showing preview
    if (_titleController.text.trim().isEmpty) {
      _showValidationError('يرجى إدخال عنوان العقار');
      return;
    }

    if (_descriptionController.text.trim().isEmpty) {
      _showValidationError('يرجى إدخال وصف العقار');
      return;
    }

    if (_priceController.text.trim().isEmpty) {
      _showValidationError('يرجى إدخال السعر');
      return;
    }

    if (_selectedCategory == null) {
      _showValidationError('يرجى اختيار فئة العقار');
      return;
    }

    if (_selectedPropertyType == null) {
      _showValidationError('يرجى اختيار نوع العقار');
      return;
    }

    if (_selectedCancellationPolicy == null) {
      _showValidationError('يرجى اختيار سياسة الإلغاء');
      return;
    }

    // Get facility names for preview
    final state = context.read<PropertyCreationCubit>().state;
    List<String> facilityNames = [];
    if (state is PropertyCreationDataLoaded) {
      facilityNames = state.facilities
          .where((facility) => _selectedFacilities.contains(facility.id))
          .map((facility) => facility.title)
          .toList();
    }

    // Show preview dialog
    final shouldSubmit = await showDialog<bool>(
      context: context,
      builder: (context) => PropertyPreviewDialog(
        title: _titleController.text,
        description: _descriptionController.text,
        price: double.parse(_priceController.text),
        weekendPrice: _weekendPriceController.text.isNotEmpty
            ? double.parse(_weekendPriceController.text) : null,
        weeklyPrice: _weekPriceController.text.isNotEmpty
            ? double.parse(_weekPriceController.text) : null,
        monthlyPrice: _monthPriceController.text.isNotEmpty
            ? double.parse(_monthPriceController.text) : null,
        guests: int.parse(_guestsController.text.isNotEmpty ? _guestsController.text : '1'),
        bedrooms: int.parse(_bedsController.text.isNotEmpty ? _bedsController.text : '1'),
        bathrooms: int.parse(_bathsController.text.isNotEmpty ? _bathsController.text : '1'),
        bookingRules: _bookingRulesController.text.isNotEmpty ? _bookingRulesController.text : null,
        cancellationRules: _cancellationRulesController.text.isNotEmpty ? _cancellationRulesController.text : null,
        category: _selectedCategory!,
        propertyType: _selectedPropertyType!,
        cancellationPolicy: _selectedCancellationPolicy!,
        facilityNames: facilityNames,
        mainImage: _mainImage,
        video: _video,
        galleryImages: _galleryImages,
        latitude: _latitude,
        longitude: _longitude,
      ),
    );

    // If user confirmed in preview, submit the form
    if (shouldSubmit == true) {
      _submitForm();
    }
  }

  /// Load saved form data on initialization
  Future<void> _loadSavedData() async {
    final savedData = await PropertyFormAutoSave.loadFormData();
    if (savedData != null && mounted) {
      final shouldRestore = await _showRestoreDialog();
      if (shouldRestore && mounted) {
        _restoreFormData(savedData);
      }
    }
  }

  /// Setup auto-save timer
  void _setupAutoSave() {
    _autoSaveTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      _performAutoSave();
    });

    // Add listeners to text controllers to trigger auto-save
    _titleController.addListener(_onFormChanged);
    _descriptionController.addListener(_onFormChanged);
    _priceController.addListener(_onFormChanged);
    _weekendPriceController.addListener(_onFormChanged);
    _weekPriceController.addListener(_onFormChanged);
    _monthPriceController.addListener(_onFormChanged);
    _guestsController.addListener(_onFormChanged);
    _bedsController.addListener(_onFormChanged);
    _bathsController.addListener(_onFormChanged);
    _bookingRulesController.addListener(_onFormChanged);
    _cancellationRulesController.addListener(_onFormChanged);
  }

  /// Called when form data changes
  void _onFormChanged() {
    setState(() {
      _hasUnsavedChanges = true;
    });
  }

  /// Perform auto-save
  Future<void> _performAutoSave() async {
    if (_hasUnsavedChanges) {
      await PropertyFormAutoSave.autoSaveWithDebounce(
        title: _titleController.text,
        description: _descriptionController.text,
        price: _priceController.text,
        weekendPrice: _weekendPriceController.text.isNotEmpty ? _weekendPriceController.text : null,
        weeklyPrice: _weekPriceController.text.isNotEmpty ? _weekPriceController.text : null,
        monthlyPrice: _monthPriceController.text.isNotEmpty ? _monthPriceController.text : null,
        guests: _guestsController.text,
        beds: _bedsController.text,
        baths: _bathsController.text,
        bookingRules: _bookingRulesController.text.isNotEmpty ? _bookingRulesController.text : null,
        cancellationRules: _cancellationRulesController.text.isNotEmpty ? _cancellationRulesController.text : null,
        selectedCategoryId: _selectedCategory?.id,
        selectedPropertyTypeId: _selectedPropertyType?.id,
        selectedCancellationPolicyId: _selectedCancellationPolicy?.id,
        selectedFacilities: _selectedFacilities,
        latitude: _latitude,
        longitude: _longitude,
      );

      setState(() {
        _hasUnsavedChanges = false;
        _lastAutoSave = DateTime.now();
      });
    }
  }

  /// Show dialog to ask user if they want to restore saved data
  Future<bool> _showRestoreDialog() async {
    final lastSave = await PropertyFormAutoSave.getLastSaveTime();
    final timeText = lastSave != null ? PropertyFormAutoSave.formatLastSaveTime(lastSave) : '';

    if (!mounted) return false;

    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('استعادة البيانات المحفوظة'),
        content: Text(
          'تم العثور على بيانات محفوظة تلقائياً $timeText.\nهل تريد استعادة هذه البيانات؟',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('لا'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('نعم'),
          ),
        ],
      ),
    ) ?? false;
  }

  /// Restore form data from saved data
  void _restoreFormData(Map<String, dynamic> savedData) {
    _titleController.text = savedData['title'] ?? '';
    _descriptionController.text = savedData['description'] ?? '';
    _priceController.text = savedData['price'] ?? '';
    _weekendPriceController.text = savedData['weekend_price'] ?? '';
    _weekPriceController.text = savedData['weekly_price'] ?? '';
    _monthPriceController.text = savedData['monthly_price'] ?? '';
    _guestsController.text = savedData['guests'] ?? '';
    _bedsController.text = savedData['beds'] ?? '';
    _bathsController.text = savedData['baths'] ?? '';
    _bookingRulesController.text = savedData['booking_rules'] ?? '';
    _cancellationRulesController.text = savedData['cancellation_rules'] ?? '';

    // Restore location
    _latitude = savedData['latitude']?.toDouble();
    _longitude = savedData['longitude']?.toDouble();

    // Restore selected facilities
    final facilities = savedData['selected_facilities'] as List<dynamic>?;
    if (facilities != null) {
      _selectedFacilities.clear();
      _selectedFacilities.addAll(facilities.cast<int>());
    }

    setState(() {});
  }

  void _submitForm() async {
    if (_formKey.currentState!.validate()) {
      // Validate required selections
      if (_selectedCategory == null) {
        _showValidationError('يرجى اختيار فئة العقار');
        return;
      }

      if (_selectedPropertyType == null) {
        _showValidationError('يرجى اختيار نوع العقار');
        return;
      }

      if (_selectedCancellationPolicy == null) {
        _showValidationError('يرجى اختيار سياسة الإلغاء');
        return;
      }

      // Validate location
      final locationError = PropertyValidationHelper.validateLocation(_latitude, _longitude);
      if (locationError != null) {
        _showValidationError(locationError);
        return;
      }

      // Validate main image
      if (_mainImage == null) {
        _showValidationError('يرجى إضافة صورة رئيسية للعقار');
        return;
      }

      // Validate image sizes
      final mainImageError = PropertyValidationHelper.validateImageSize(_mainImage);
      if (mainImageError != null) {
        _showValidationError('الصورة الرئيسية: $mainImageError');
        return;
      }

      final videoError = PropertyValidationHelper.validateVideoSize(_video);
      if (videoError != null) {
        _showValidationError('الفيديو: $videoError');
        return;
      }

      final galleryError = PropertyValidationHelper.validateGalleryCount(_galleryImages);
      if (galleryError != null) {
        _showValidationError(galleryError);
        return;
      }

      // Validate gallery image sizes
      for (int i = 0; i < _galleryImages.length; i++) {
        final imageError = PropertyValidationHelper.validateImageSize(_galleryImages[i]);
        if (imageError != null) {
          _showValidationError('صورة المعرض ${i + 1}: $imageError');
          return;
        }
      }

      // Validate facilities selection
      if (_selectedFacilities.isEmpty) {
        _showValidationError('يرجى اختيار مرفق واحد على الأقل');
        return;
      }

      final propertyData = {
        'title': _titleController.text,
        'content': _descriptionController.text,
        'service_category_id': _selectedCategory!.id,
        'property_type_id': _selectedPropertyType!.id,
        'cancellation_policy_id': _selectedCancellationPolicy!.id,
        'price': double.parse(_priceController.text),
        'weekend_price': _weekendPriceController.text.isNotEmpty
            ? double.parse(_weekendPriceController.text) : null,
        'week_price': _weekPriceController.text.isNotEmpty
            ? double.parse(_weekPriceController.text) : null,
        'month_price': _monthPriceController.text.isNotEmpty
            ? double.parse(_monthPriceController.text) : null,
        'no_guests': int.parse(_guestsController.text),
        'beds': int.parse(_bedsController.text),
        'baths': int.parse(_bathsController.text),
        'booking_rules': _bookingRulesController.text.isNotEmpty
            ? _bookingRulesController.text : null,
        'cancelation_rules': _cancellationRulesController.text.isNotEmpty
            ? _cancellationRulesController.text : null,
        'facility_ids': _selectedFacilities,
        'lat': _latitude,
        'lon': _longitude,
      };

      // Clear auto-save data before submission
      await PropertyFormAutoSave.clearSavedData();

      if (!mounted) return;

      context.read<PropertyCreationCubit>().createProperty(
        propertyData: propertyData,
        mainImage: _mainImage,
        video: _video,
        galleryImages: _galleryImages,
      );
    }
  }
}
