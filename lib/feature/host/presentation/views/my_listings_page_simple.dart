import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/core/routing/routes_keys.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/widgets/enhanced_page_layouts.dart';
import 'package:gather_point/feature/host/presentation/cubit/my_listings_cubit.dart';
import 'package:gather_point/feature/host/data/services/my_listings_api_service.dart';
import 'package:gather_point/core/services/service_locator.dart';

/// Simplified My Listings Page with better error handling and fallback UI
class MyListingsPageSimple extends StatelessWidget {
  const MyListingsPageSimple({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) {
        try {
          return MyListingsCubit(getIt<MyListingsApiService>())..loadInitialData();
        } catch (e) {
          // Fallback if service is not available
          debugPrint('Error creating MyListingsCubit: $e');
          return MyListingsCubit(getIt<MyListingsApiService>());
        }
      },
      child: const _MyListingsPageContent(),
    );
  }
}

class _MyListingsPageContent extends StatefulWidget {
  const _MyListingsPageContent();

  @override
  State<_MyListingsPageContent> createState() => _MyListingsPageContentState();
}

class _MyListingsPageContentState extends State<_MyListingsPageContent> {
  bool _isGridView = false;

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return EnhancedPageLayout(
      title: s.myListings,
      actions: [
        // View toggle
        IconButton(
          onPressed: () {
            setState(() {
              _isGridView = !_isGridView;
            });
          },
          icon: Icon(_isGridView ? Icons.view_list : Icons.grid_view),
          tooltip: _isGridView ? 'List View' : 'Grid View',
        ),
        
        // Add new listing
        IconButton(
          onPressed: () {
            try {
              context.go(RoutesKeys.kCreateProperty);
            } catch (e) {
              // Fallback navigation
              context.go('/create-property');
            }
          },
          icon: const Icon(Icons.add),
          tooltip: 'Add New Listing',
        ),
      ],
      body: BlocConsumer<MyListingsCubit, MyListingsState>(
        listener: (context, state) {
          if (state is MyListingsError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
                action: SnackBarAction(
                  label: 'Retry',
                  onPressed: () {
                    context.read<MyListingsCubit>().loadInitialData();
                  },
                ),
              ),
            );
          }
        },
        builder: (context, state) {
          return _buildContent(context, state);
        },
      ),
    );
  }

  Widget _buildContent(BuildContext context, MyListingsState state) {
    if (state is MyListingsLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading your listings...'),
          ],
        ),
      );
    }

    if (state is MyListingsError) {
      return _buildErrorState(context, state.message);
    }

    if (state is MyListingsLoaded) {
      if (state.listings.isEmpty) {
        return _buildEmptyState(context);
      }
      return _buildListingsView(context, state);
    }

    // Fallback for any other state
    return _buildFallbackUI(context);
  }

  Widget _buildErrorState(BuildContext context, String message) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[300],
            ),
            const SizedBox(height: 16),
            Text(
              'Error Loading Listings',
              style: AppTextStyles.font18Bold,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              message,
              style: AppTextStyles.font14Regular.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton.icon(
                  onPressed: () {
                    context.read<MyListingsCubit>().loadInitialData();
                  },
                  icon: const Icon(Icons.refresh),
                  label: const Text('Retry'),
                ),
                const SizedBox(width: 16),
                OutlinedButton.icon(
                  onPressed: () {
                    context.go(RoutesKeys.kCreateProperty);
                  },
                  icon: const Icon(Icons.add),
                  label: const Text('Add Listing'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.home_work_outlined,
              size: 80,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 24),
            Text(
              'No Listings Yet',
              style: AppTextStyles.font20Bold,
            ),
            const SizedBox(height: 8),
            Text(
              'Start by creating your first property listing',
              style: AppTextStyles.font14Regular.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: () {
                context.go(RoutesKeys.kCreateProperty);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFFEC53A),
                foregroundColor: Colors.black,
                padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              ),
              icon: const Icon(Icons.add),
              label: const Text('Create Your First Listing'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildListingsView(BuildContext context, MyListingsLoaded state) {
    return RefreshIndicator(
      onRefresh: () async {
        context.read<MyListingsCubit>().refreshListings();
      },
      child: _isGridView ? _buildGridView(state) : _buildListView(state),
    );
  }

  Widget _buildGridView(MyListingsLoaded state) {
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 0.75,
      ),
      itemCount: state.listings.length,
      itemBuilder: (context, index) {
        final listing = state.listings[index];
        return _buildListingCard(listing, isGrid: true);
      },
    );
  }

  Widget _buildListView(MyListingsLoaded state) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: state.listings.length,
      itemBuilder: (context, index) {
        final listing = state.listings[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: _buildListingCard(listing, isGrid: false),
        );
      },
    );
  }

  Widget _buildListingCard(dynamic listing, {required bool isGrid}) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: () {
          // Navigate to listing details
          _showListingActions(listing);
        },
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Image placeholder
              Container(
                height: isGrid ? 120 : 80,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Center(
                  child: Icon(Icons.image, size: 40, color: Colors.grey),
                ),
              ),
              const SizedBox(height: 8),
              
              // Title
              Text(
                listing.title ?? 'Untitled Listing',
                style: AppTextStyles.font14SemiBold,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              
              // Price
              Text(
                '${listing.price ?? 0} ر.س / night',
                style: AppTextStyles.font12Regular.copyWith(
                  color: const Color(0xFFFEC53A),
                  fontWeight: FontWeight.bold,
                ),
              ),
              
              if (!isGrid) ...[
                const SizedBox(height: 8),
                // Status
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getStatusColor(listing.status),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    listing.status ?? 'Unknown',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Color _getStatusColor(String? status) {
    switch (status?.toLowerCase()) {
      case 'active':
        return Colors.green;
      case 'inactive':
        return Colors.orange;
      case 'draft':
        return Colors.grey;
      case 'pending':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  void _showListingActions(dynamic listing) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.edit),
              title: const Text('Edit Listing'),
              onTap: () {
                Navigator.pop(context);
                context.go(RoutesKeys.kEditProperty, extra: {'propertyId': listing.id});
              },
            ),
            ListTile(
              leading: const Icon(Icons.analytics),
              title: const Text('View Analytics'),
              onTap: () {
                Navigator.pop(context);
                context.go(RoutesKeys.kPropertyAnalytics, extra: {
                  'propertyId': listing.id,
                  'propertyTitle': listing.title,
                });
              },
            ),
            ListTile(
              leading: const Icon(Icons.visibility),
              title: const Text('View Details'),
              onTap: () {
                Navigator.pop(context);
                // Navigate to details page
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFallbackUI(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.home_work, size: 64, color: Colors.grey),
          const SizedBox(height: 16),
          const Text('My Listings'),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              context.go(RoutesKeys.kCreateProperty);
            },
            child: const Text('Create New Listing'),
          ),
        ],
      ),
    );
  }
}
