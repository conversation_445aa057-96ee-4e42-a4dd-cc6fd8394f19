import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/core/widgets/enhanced_page_layouts.dart';
import 'package:gather_point/feature/host/data/models/my_listing_model.dart';
import 'package:gather_point/feature/host/services/property_edit_service.dart';
import 'package:gather_point/feature/host/presentation/cubit/property_creation_cubit.dart';
import 'package:gather_point/feature/host/widgets/edit_property_form.dart';

class EditPropertyPage extends StatefulWidget {
  final int propertyId;
  final MyListingModel? initialData;

  const EditPropertyPage({
    super.key,
    required this.propertyId,
    this.initialData,
  });

  @override
  State<EditPropertyPage> createState() => _EditPropertyPageState();
}

class _EditPropertyPageState extends State<EditPropertyPage> {
  MyListingModel? _propertyData;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadPropertyData();
  }

  Future<void> _loadPropertyData() async {
    if (widget.initialData != null) {
      setState(() {
        _propertyData = widget.initialData;
        _isLoading = false;
      });
      _initializeForm();
      return;
    }

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // In a real implementation, you would fetch the property data from the API
      // For now, we'll use mock data
      final propertyData = PropertyEditService.generateMockPropertyData(widget.propertyId);
      
      setState(() {
        _propertyData = propertyData;
        _isLoading = false;
      });

      _initializeForm();
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  void _initializeForm() {
    if (_propertyData != null) {
      // Initialize the property creation cubit with existing data
      WidgetsBinding.instance.addPostFrameCallback((_) {
        final cubit = context.read<PropertyCreationCubit>();
        cubit.initializeForEdit(_propertyData!);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return EnhancedPageLayout(
      title: s.editProperty,
      body: _buildBody(s),
    );
  }

  Widget _buildBody(S s) {
    if (_isLoading) {
      return _buildLoadingState(s);
    }

    if (_error != null) {
      return _buildErrorState(s);
    }

    if (_propertyData == null) {
      return _buildNotFoundState(s);
    }

    return EditPropertyForm(
      propertyData: _propertyData!,
    );
  }

  Widget _buildLoadingState(S s) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(),
          const SizedBox(height: 16),
          Text(
            s.loadingPropertyData,
            style: Theme.of(context).textTheme.bodyLarge,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(S s) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[300],
            ),
            const SizedBox(height: 16),
            Text(
              s.errorLoadingProperty,
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                OutlinedButton.icon(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.arrow_back),
                  label: Text(s.goBack),
                ),
                const SizedBox(width: 16),
                ElevatedButton.icon(
                  onPressed: _loadPropertyData,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFFEC53A),
                    foregroundColor: Colors.black,
                  ),
                  icon: const Icon(Icons.refresh),
                  label: Text(s.tryAgain),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotFoundState(S s) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              s.propertyNotFound,
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              s.propertyNotFoundDescription,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                OutlinedButton.icon(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.arrow_back),
                  label: Text(s.goBack),
                ),
                const SizedBox(width: 16),
                ElevatedButton.icon(
                  onPressed: () {
                    Navigator.pushReplacementNamed(context, '/create-property');
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFFEC53A),
                    foregroundColor: Colors.black,
                  ),
                  icon: const Icon(Icons.add),
                  label: Text(s.createNewProperty),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

// Note: Form initialization is handled within the EditPropertyForm widget
