import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/core/routing/routes_keys.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/widgets/enhanced_page_layouts.dart';
import 'package:gather_point/feature/host/presentation/cubit/my_listings_cubit.dart';
import 'package:gather_point/feature/host/data/models/listing_filter_model.dart';
import 'package:gather_point/feature/host/data/services/my_listings_api_service.dart';
import 'package:gather_point/feature/host/widgets/listings_stats_card.dart';
import 'package:gather_point/feature/host/widgets/listings_filter_bar.dart';
import 'package:gather_point/feature/host/widgets/listing_card.dart';
import 'package:gather_point/feature/host/widgets/listings_empty_state.dart';
import 'package:gather_point/feature/host/widgets/listings_error_widget.dart';
import 'package:gather_point/core/services/service_locator.dart';

class MyListingsPage extends StatelessWidget {
  const MyListingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => MyListingsCubit(getIt<MyListingsApiService>())..loadInitialData(),
      child: const _MyListingsPageContent(),
    );
  }
}

class _MyListingsPageContent extends StatefulWidget {
  const _MyListingsPageContent();

  @override
  State<_MyListingsPageContent> createState() => _MyListingsPageContentState();
}

class _MyListingsPageContentState extends State<_MyListingsPageContent> {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();
  bool _isGridView = false;
  bool _isSelectionMode = false;
  final Set<int> _selectedListings = {};

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= 
        _scrollController.position.maxScrollExtent - 200) {
      context.read<MyListingsCubit>().loadMoreListings();
    }
  }

  void _toggleSelectionMode() {
    setState(() {
      _isSelectionMode = !_isSelectionMode;
      if (!_isSelectionMode) {
        _selectedListings.clear();
      }
    });
  }

  void _toggleListingSelection(int listingId) {
    setState(() {
      if (_selectedListings.contains(listingId)) {
        _selectedListings.remove(listingId);
      } else {
        _selectedListings.add(listingId);
      }
    });
  }

  void _selectAllListings(List<int> allListingIds) {
    setState(() {
      if (_selectedListings.length == allListingIds.length) {
        _selectedListings.clear();
      } else {
        _selectedListings.addAll(allListingIds);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return EnhancedPageLayout(
      title: s.myListings,
      actions: [
        // View toggle
        IconButton(
          onPressed: () {
            setState(() {
              _isGridView = !_isGridView;
            });
          },
          icon: Icon(_isGridView ? Icons.view_list : Icons.grid_view),
          tooltip: _isGridView ? s.listView : s.gridView,
        ),
        
        // Selection mode toggle
        IconButton(
          onPressed: _toggleSelectionMode,
          icon: Icon(_isSelectionMode ? Icons.close : Icons.checklist),
          tooltip: _isSelectionMode ? s.cancelSelection : s.selectMultiple,
        ),
        
        // Add new listing
        IconButton(
          onPressed: () {
            context.go(RoutesKeys.kCreateProperty);
          },
          icon: const Icon(Icons.add),
          tooltip: s.addNewListing,
        ),
      ],
      body: BlocConsumer<MyListingsCubit, MyListingsState>(
        listener: (context, state) {
          if (state is MyListingsError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        builder: (context, state) {
          if (state is MyListingsLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (state is MyListingsError) {
            return ListingsErrorWidget(
              message: state.message,
              onRetry: () => context.read<MyListingsCubit>().loadInitialData(),
            );
          }

          if (state is MyListingsLoaded) {
            if (state.listings.isEmpty && !state.filter.hasActiveFilters) {
              return ListingsEmptyState(
                onCreateListing: () {
                  context.go(RoutesKeys.kCreateProperty);
                },
              );
            }

            return Column(
              children: [
                // Stats Card
                if (state.stats.hasListings)
                  ListingsStatsCard(stats: state.stats),

                // Filter Bar
                ListingsFilterBar(
                  currentFilter: state.filter,
                  onFilterChanged: (filter) {
                    context.read<MyListingsCubit>().applyFilter(filter);
                  },
                  onSearchChanged: (query) {
                    context.read<MyListingsCubit>().searchListings(query);
                  },
                  searchController: _searchController,
                ),

                // Selection Bar (when in selection mode)
                if (_isSelectionMode)
                  _buildSelectionBar(state),

                // Listings
                Expanded(
                  child: _buildListingsView(state),
                ),
              ],
            );
          }

          // Handle other states (refreshing, loading more, filtering)
          if (state is MyListingsRefreshing ||
              state is MyListingsLoadingMore ||
              state is MyListingsFiltering) {
            final currentListings = _getCurrentListings(state);
            final currentStats = _getCurrentStats(state);
            
            return Column(
              children: [
                if (currentStats?.hasListings == true)
                  ListingsStatsCard(stats: currentStats!),
                
                ListingsFilterBar(
                  currentFilter: _getCurrentFilter(state),
                  onFilterChanged: (filter) {
                    context.read<MyListingsCubit>().applyFilter(filter);
                  },
                  onSearchChanged: (query) {
                    context.read<MyListingsCubit>().searchListings(query);
                  },
                  searchController: _searchController,
                ),

                if (_isSelectionMode)
                  _buildSelectionBar(state),

                Expanded(
                  child: Stack(
                    children: [
                      _buildListingsGrid(currentListings),
                      if (state is MyListingsRefreshing)
                        const Positioned(
                          top: 0,
                          left: 0,
                          right: 0,
                          child: LinearProgressIndicator(),
                        ),
                      if (state is MyListingsFiltering)
                        Container(
                          color: Colors.black.withValues(alpha: 0.1),
                          child: const Center(
                            child: CircularProgressIndicator(),
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            );
          }

          return const SizedBox.shrink();
        },
      ),
    );
  }

  Widget _buildSelectionBar(dynamic state) {
    final allListingIds = _getCurrentListings(state).map((l) => l.id as int).toList();
    final isAllSelected = _selectedListings.length == allListingIds.length && allListingIds.isNotEmpty;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: const Color(0xFFFEC53A).withValues(alpha: 0.1),
        border: Border(
          bottom: BorderSide(color: Colors.grey.withValues(alpha: 0.2)),
        ),
      ),
      child: Row(
        children: [
          Checkbox(
            value: isAllSelected,
            onChanged: (_) => _selectAllListings(allListingIds),
          ),
          Text(
            '${_selectedListings.length} محدد',
            style: AppTextStyles.font14SemiBold,
          ),
          const Spacer(),
          if (_selectedListings.isNotEmpty) ...[
            TextButton.icon(
              onPressed: () => _showBulkActionsDialog(),
              icon: const Icon(Icons.edit),
              label: const Text('إجراءات'),
            ),
            const SizedBox(width: 8),
            TextButton.icon(
              onPressed: () => _showBulkDeleteDialog(),
              icon: const Icon(Icons.delete, color: Colors.red),
              label: const Text('حذف', style: TextStyle(color: Colors.red)),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildListingsView(MyListingsLoaded state) {
    if (state.listings.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد عقارات تطابق البحث',
              style: AppTextStyles.font16Regular.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            TextButton(
              onPressed: () {
                context.read<MyListingsCubit>().clearFilters();
                _searchController.clear();
              },
              child: const Text('مسح الفلاتر'),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () => context.read<MyListingsCubit>().refreshListings(),
      child: _buildListingsGrid(state.listings),
    );
  }

  Widget _buildListingsGrid(List<dynamic> listings) {
    if (_isGridView) {
      return GridView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.all(16),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 0.75,
        ),
        itemCount: listings.length,
        itemBuilder: (context, index) {
          final listing = listings[index];
          return ListingCard(
            listing: listing,
            isGridView: true,
            isSelectionMode: _isSelectionMode,
            isSelected: _selectedListings.contains(listing.id),
            onTap: () => _handleListingTap(listing),
            onSelectionChanged: (selected) {
              if (selected) {
                _selectedListings.add(listing.id);
              } else {
                _selectedListings.remove(listing.id);
              }
              setState(() {});
            },
          );
        },
      );
    } else {
      return ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.all(16),
        itemCount: listings.length,
        itemBuilder: (context, index) {
          final listing = listings[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: ListingCard(
              listing: listing,
              isGridView: false,
              isSelectionMode: _isSelectionMode,
              isSelected: _selectedListings.contains(listing.id),
              onTap: () => _handleListingTap(listing),
              onSelectionChanged: (selected) {
                if (selected) {
                  _selectedListings.add(listing.id);
                } else {
                  _selectedListings.remove(listing.id);
                }
                setState(() {});
              },
            ),
          );
        },
      );
    }
  }

  void _handleListingTap(dynamic listing) {
    if (_isSelectionMode) {
      _toggleListingSelection(listing.id);
    } else {
      // Navigate to listing details or edit
      context.go('/listing-details', extra: listing.id);
    }
  }

  void _showBulkActionsDialog() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.visibility),
              title: const Text('تفعيل'),
              onTap: () {
                Navigator.pop(context);
                context.read<MyListingsCubit>().bulkUpdateStatus(
                  _selectedListings.toList(),
                  'active',
                );
                _toggleSelectionMode();
              },
            ),
            ListTile(
              leading: const Icon(Icons.visibility_off),
              title: const Text('إلغاء تفعيل'),
              onTap: () {
                Navigator.pop(context);
                context.read<MyListingsCubit>().bulkUpdateStatus(
                  _selectedListings.toList(),
                  'inactive',
                );
                _toggleSelectionMode();
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showBulkDeleteDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف العقارات'),
        content: Text('هل أنت متأكد من حذف ${_selectedListings.length} عقار؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<MyListingsCubit>().bulkDeleteListings(
                _selectedListings.toList(),
              );
              _toggleSelectionMode();
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  // Helper methods to extract data from different state types
  List<dynamic> _getCurrentListings(dynamic state) {
    if (state is MyListingsLoaded) return state.listings;
    if (state is MyListingsRefreshing) return state.currentListings;
    if (state is MyListingsLoadingMore) return state.currentListings;
    if (state is MyListingsFiltering) return state.currentListings;
    return [];
  }

  dynamic _getCurrentStats(dynamic state) {
    if (state is MyListingsLoaded) return state.stats;
    if (state is MyListingsRefreshing) return state.currentStats;
    if (state is MyListingsLoadingMore) return state.currentStats;
    if (state is MyListingsFiltering) return state.currentStats;
    return null;
  }

  ListingFilterModel _getCurrentFilter(dynamic state) {
    if (state is MyListingsLoaded) return state.filter;
    if (state is MyListingsRefreshing) return state.currentFilter;
    if (state is MyListingsLoadingMore) return state.currentFilter;
    if (state is MyListingsFiltering) return state.newFilter;
    return ListingFilterModel.allListings;
  }
}
