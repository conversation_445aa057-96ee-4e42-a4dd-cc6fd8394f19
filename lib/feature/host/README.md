# Host Management System

A comprehensive property management system for hosts to manage their listings, view analytics, and handle bulk operations.

## Features

### 📋 My Listings Management
- **Grid and List Views**: Switch between different viewing modes
- **Real-time Status Updates**: Live status indicators and updates
- **Advanced Filtering**: Filter by status, performance, date ranges
- **Search Functionality**: Quick search across all properties
- **Selection Mode**: Multi-select for bulk operations

### 📊 Analytics Dashboard
- **Performance Metrics**: Views, bookings, revenue, conversion rates
- **Interactive Charts**: Time series data visualization
- **Comparison Analytics**: Period-over-period and market comparisons
- **Performance Grading**: Automated scoring system
- **Export Capabilities**: Data export in multiple formats

### ⚡ Bulk Operations
- **Status Management**: Bulk activate, deactivate, publish
- **Pricing Operations**: Bulk price adjustments and discounts
- **Content Management**: Bulk duplicate, export, archive
- **Smart Validation**: Prevent invalid operations
- **Progress Tracking**: Real-time operation monitoring

### ✏️ Property Editing
- **Pre-populated Forms**: Existing data automatically loaded
- **Image Management**: Add, remove, reorder property images
- **Change Detection**: Smart validation with change tracking
- **Draft Saving**: Save work in progress
- **Validation Engine**: Comprehensive business rule validation

### 🧭 Navigation Integration
- **Deep Linking**: Direct links to specific properties
- **Route Guards**: Authentication and authorization
- **Smooth Transitions**: Custom page transitions
- **Context Menus**: Quick action menus

## Installation

### 1. Add Dependencies

```yaml
dependencies:
  flutter_bloc: ^8.1.3
  dio: ^5.3.2
  # Add other required dependencies
```

### 2. Register Routes

```dart
// In your main app router
import 'package:gather_point/feature/host/integration/app_integration.dart';

class AppRouter {
  static Route<dynamic>? generateRoute(RouteSettings settings) {
    // Try host routes first
    final hostRoute = HostAppIntegration.generateRoute(settings);
    if (hostRoute != null) {
      return hostRoute;
    }
    
    // Handle other routes...
    return null;
  }
  
  static Map<String, WidgetBuilder> get routes {
    return HostAppIntegration.integrateWithExistingRoutes({
      // Your existing routes
    });
  }
}
```

### 3. Add Navigation Extensions

```dart
// Use navigation extensions
Navigator.of(context).pushMyListings();
Navigator.of(context).pushEditProperty(propertyId: 123);
Navigator.of(context).pushPropertyAnalytics(propertyId: 123);
```

### 4. Integrate with Existing Screens

```dart
// Add host features to existing screens
class MyExistingScreen extends StatefulWidget with HostNavigationMixin {
  @override
  Widget build(BuildContext context) {
    return HostFeatureIntegration(
      showHostButton: true,
      child: Scaffold(
        // Your existing content
      ),
    );
  }
}
```

## Usage Examples

### Basic Navigation

```dart
// Navigate to my listings
HostNavigationHelpers.goToMyListings(context);

// Edit a property
HostNavigationHelpers.editProperty(
  context,
  propertyId: 123,
  propertyData: myProperty,
);

// View analytics
HostNavigationHelpers.viewPropertyAnalytics(
  context,
  propertyId: 123,
  propertyTitle: 'My Property',
);
```

### Bulk Operations

```dart
// Show bulk actions for selected properties
showModalBottomSheet(
  context: context,
  builder: (context) => BulkActionsToolbar(
    selectedListings: selectedProperties,
    onComplete: () {
      // Refresh data
    },
  ),
);
```

### Property Validation

```dart
// Validate property changes
final validation = PropertyEditValidator.validatePropertyEdit(
  originalProperty,
  updatedData,
);

if (!validation.isValid) {
  // Show errors
  showValidationErrors(validation.errors);
} else if (validation.warnings.isNotEmpty) {
  // Show warnings
  showValidationWarnings(validation.warnings);
}
```

### Analytics Integration

```dart
// Load analytics for a property
final analytics = await ListingAnalyticsService.getListingAnalytics(
  propertyId,
  period: '30d',
);

// Display analytics dashboard
ListingAnalyticsDashboard(
  analytics: analytics,
  onExport: () => exportAnalytics(),
);
```

## API Integration

### Required Endpoints

```
GET    /api/host/listings              - Get host listings
POST   /api/host/listings/bulk/status  - Bulk status update
PUT    /api/host/listings/{id}         - Update property
GET    /api/host/listings/{id}/analytics - Get analytics
DELETE /api/host/listings/bulk/delete  - Bulk delete
```

### Service Configuration

```dart
// Configure services with DI
GetIt.instance.registerLazySingleton<MyListingsApiService>(
  () => MyListingsApiService(GetIt.instance<ApiConsumer>()),
);

GetIt.instance.registerLazySingleton<PropertyEditService>(
  () => PropertyEditService(GetIt.instance<ApiConsumer>()),
);
```

## Customization

### Theming

```dart
// Customize colors
const Color hostPrimaryColor = Color(0xFFFEC53A);
const Color hostSecondaryColor = Color(0xFF2C3E50);

// Apply to widgets
ElevatedButton.styleFrom(
  backgroundColor: hostPrimaryColor,
  foregroundColor: Colors.black,
)
```

### Localization

All text is internationalized using the `intl` package. Add translations in:
- `lib/l10n/intl_en.arb` (English)
- `lib/l10n/intl_ar.arb` (Arabic)

### Custom Validation

```dart
// Extend validation rules
class CustomPropertyValidator extends PropertyEditValidator {
  static ValidationResult validateCustomRules(
    MyListingModel original,
    Map<String, dynamic> updated,
  ) {
    // Add your custom validation logic
    return super.validatePropertyEdit(original, updated);
  }
}
```

## Architecture

### State Management
- **BLoC Pattern**: Using flutter_bloc for state management
- **Cubit Classes**: Simplified state management for forms
- **Event-Driven**: Reactive updates across the system

### Data Layer
- **Repository Pattern**: Clean separation of data sources
- **API Services**: RESTful API integration
- **Model Classes**: Type-safe data models

### Presentation Layer
- **Widget Composition**: Reusable UI components
- **Page Layouts**: Consistent page structures
- **Navigation**: Declarative routing

## Testing

### Unit Tests
```dart
// Test validation logic
test('should validate property changes correctly', () {
  final result = PropertyEditValidator.validatePropertyEdit(
    originalProperty,
    updatedData,
  );
  expect(result.isValid, true);
});
```

### Widget Tests
```dart
// Test UI components
testWidgets('should display property card correctly', (tester) async {
  await tester.pumpWidget(
    MaterialApp(
      home: ListingCard(listing: mockListing),
    ),
  );
  expect(find.text(mockListing.title), findsOneWidget);
});
```

## Performance

### Optimizations
- **Lazy Loading**: Load data on demand
- **Image Caching**: Efficient image loading
- **Pagination**: Handle large datasets
- **Debouncing**: Optimize search and filters

### Memory Management
- **Dispose Controllers**: Proper cleanup of text controllers
- **Stream Subscriptions**: Cancel subscriptions on dispose
- **Image Memory**: Optimize image memory usage

## Contributing

1. Follow the existing code structure
2. Add tests for new features
3. Update documentation
4. Use consistent naming conventions
5. Follow Flutter best practices

## License

This project is part of the Gather Point application.
