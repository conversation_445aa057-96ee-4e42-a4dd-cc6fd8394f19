import 'package:flutter/material.dart';
import 'package:gather_point/feature/host/routes/host_routes.dart';

class HostAppIntegration {
  /// Add host routes to the main app router
  static Map<String, WidgetBuilder> getRoutes() {
    return HostRoutes.getRoutes();
  }

  /// Handle deep links for host features
  static Route<dynamic>? handleDeepLink(RouteSettings settings) {
    return HostRoutes.handleDeepLink(settings);
  }

  /// Add host routes to existing route map
  static Map<String, WidgetBuilder> integrateWithExistingRoutes(
    Map<String, WidgetBuilder> existingRoutes,
  ) {
    final hostRoutes = getRoutes();
    return {
      ...existingRoutes,
      ...hostRoutes,
    };
  }

  /// Generate route handler for main app
  static Route<dynamic>? generateRoute(RouteSettings settings) {
    // First try host-specific deep links
    final hostRoute = handleDeepLink(settings);
    if (hostRoute != null) {
      return hostRoute;
    }

    // Handle standard host routes
    final hostRoutes = getRoutes();
    final routeBuilder = hostRoutes[settings.name];
    if (routeBuilder != null) {
      return MaterialPageRoute(
        builder: routeBuilder,
        settings: settings,
      );
    }

    return null;
  }

  /// Check if route is a host route
  static bool isHostRoute(String routeName) {
    return routeName.startsWith('/host/') || 
           getRoutes().containsKey(routeName);
  }

  /// Get route guard for host features
  static Widget buildRouteGuard(
    BuildContext context,
    Widget child,
    String routeName,
  ) {
    return HostRoutes.buildRouteGuard(context, child, routeName);
  }
}

/// Extension to add host navigation to existing navigation
extension HostNavigationExtension on NavigatorState {
  /// Navigate to host my listings
  Future<void> pushMyListings() {
    return pushNamed(HostRoutes.myListings);
  }

  /// Navigate to edit property
  Future<void> pushEditProperty({
    required int propertyId,
    Map<String, dynamic>? arguments,
  }) {
    return pushNamed(
      HostRoutes.editProperty,
      arguments: {
        'propertyId': propertyId,
        ...?arguments,
      },
    );
  }

  /// Navigate to property analytics
  Future<void> pushPropertyAnalytics({
    required int propertyId,
    String? propertyTitle,
  }) {
    return pushNamed(
      HostRoutes.propertyAnalytics,
      arguments: {
        'propertyId': propertyId,
        'propertyTitle': propertyTitle,
      },
    );
  }
}

/// Widget to integrate host features into existing screens
class HostFeatureIntegration extends StatelessWidget {
  final Widget child;
  final bool showHostButton;
  final VoidCallback? onHostButtonPressed;

  const HostFeatureIntegration({
    super.key,
    required this.child,
    this.showHostButton = false,
    this.onHostButtonPressed,
  });

  @override
  Widget build(BuildContext context) {
    if (!showHostButton) {
      return child;
    }

    return Stack(
      children: [
        child,
        Positioned(
          bottom: 80,
          right: 16,
          child: FloatingActionButton.extended(
            onPressed: onHostButtonPressed ?? () {
              Navigator.of(context).pushMyListings();
            },
            backgroundColor: const Color(0xFFFEC53A),
            foregroundColor: Colors.black,
            icon: const Icon(Icons.home_work),
            label: const Text('إدارة العقارات'),
          ),
        ),
      ],
    );
  }
}

/// Mixin to add host navigation capabilities to any widget
mixin HostNavigationMixin<T extends StatefulWidget> on State<T> {
  /// Navigate to my listings
  Future<void> goToMyListings() {
    return Navigator.of(context).pushMyListings();
  }

  /// Navigate to edit property
  Future<void> editProperty(int propertyId) {
    return Navigator.of(context).pushEditProperty(propertyId: propertyId);
  }

  /// Navigate to property analytics
  Future<void> viewPropertyAnalytics(int propertyId, {String? title}) {
    return Navigator.of(context).pushPropertyAnalytics(
      propertyId: propertyId,
      propertyTitle: title,
    );
  }

  /// Show host features bottom sheet
  void showHostFeatures() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => const HostFeaturesBottomSheet(),
    );
  }
}

/// Bottom sheet showing host features
class HostFeaturesBottomSheet extends StatelessWidget {
  const HostFeaturesBottomSheet({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 16),

          // Title
          const Text(
            'إدارة العقارات',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 24),

          // Features
          _buildFeatureTile(
            icon: Icons.list,
            title: 'عقاراتي',
            subtitle: 'عرض وإدارة جميع عقاراتك',
            onTap: () {
              Navigator.pop(context);
              Navigator.of(context).pushMyListings();
            },
          ),
          _buildFeatureTile(
            icon: Icons.add_home,
            title: 'إضافة عقار جديد',
            subtitle: 'أضف عقار جديد للإيجار',
            onTap: () {
              Navigator.pop(context);
              Navigator.pushNamed(context, '/create-property');
            },
          ),
          _buildFeatureTile(
            icon: Icons.analytics,
            title: 'التحليلات',
            subtitle: 'عرض إحصائيات الأداء',
            onTap: () {
              Navigator.pop(context);
              // Navigate to analytics overview
            },
          ),

          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildFeatureTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: const Color(0xFFFEC53A).withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Icon(
          icon,
          color: const Color(0xFFFEC53A),
        ),
      ),
      title: Text(
        title,
        style: const TextStyle(fontWeight: FontWeight.bold),
      ),
      subtitle: Text(subtitle),
      onTap: onTap,
    );
  }
}
