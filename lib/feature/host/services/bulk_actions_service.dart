import 'package:dio/dio.dart';
import 'package:gather_point/core/databases/api/end_points.dart';
import 'package:gather_point/core/databases/api/api_consumer.dart';

class BulkActionsService {
  final ApiConsumer _apiConsumer;

  BulkActionsService(this._apiConsumer);

  /// Bulk update listing status
  Future<Map<String, dynamic>> bulkUpdateStatus(
    List<int> listingIds,
    String status,
  ) async {
    try {
      final response = await _apiConsumer.post(
        '${EndPoints.hostListings}/bulk/status',
        data: {
          'listing_ids': listingIds,
          'status': status,
        },
      );

      if (response['success'] == true) {
        return response['data'] ?? {};
      }

      throw Exception('Failed to update listing status');
    } catch (e) {
      throw Exception('Failed to bulk update status: $e');
    }
  }

  /// Bulk delete listings
  Future<Map<String, dynamic>> bulkDeleteListings(List<int> listingIds) async {
    try {
      final response = await _apiConsumer.delete(
        '${EndPoints.hostListings}/bulk/delete',
        data: {
          'listing_ids': listingIds,
        },
      );

      if (response['success'] == true) {
        return response['data'] ?? {};
      }

      throw Exception('Failed to delete listings');
    } catch (e) {
      throw Exception('Failed to bulk delete listings: $e');
    }
  }

  /// Bulk update prices
  Future<Map<String, dynamic>> bulkUpdatePrices(
    List<int> listingIds,
    String operation, // 'increase', 'decrease', 'set'
    double value,
  ) async {
    try {
      final response = await _apiConsumer.post(
        '${EndPoints.hostListings}/bulk/prices',
        data: {
          'listing_ids': listingIds,
          'operation': operation,
          'value': value,
        },
      );

      if (response['success'] == true) {
        return response['data'] ?? {};
      }

      throw Exception('Failed to update prices');
    } catch (e) {
      throw Exception('Failed to bulk update prices: $e');
    }
  }

  /// Bulk apply discount
  Future<Map<String, dynamic>> bulkApplyDiscount(
    List<int> listingIds,
    double discountPercentage,
    int durationDays,
  ) async {
    try {
      final response = await _apiConsumer.post(
        '${EndPoints.hostListings}/bulk/discount',
        data: {
          'listing_ids': listingIds,
          'discount_percentage': discountPercentage,
          'duration_days': durationDays,
        },
      );

      if (response['success'] == true) {
        return response['data'] ?? {};
      }

      throw Exception('Failed to apply discount');
    } catch (e) {
      throw Exception('Failed to bulk apply discount: $e');
    }
  }

  /// Bulk duplicate listings
  Future<Map<String, dynamic>> bulkDuplicateListings(List<int> listingIds) async {
    try {
      final response = await _apiConsumer.post(
        '${EndPoints.hostListings}/bulk/duplicate',
        data: {
          'listing_ids': listingIds,
        },
      );

      if (response['success'] == true) {
        return response['data'] ?? {};
      }

      throw Exception('Failed to duplicate listings');
    } catch (e) {
      throw Exception('Failed to bulk duplicate listings: $e');
    }
  }

  /// Bulk archive listings
  Future<Map<String, dynamic>> bulkArchiveListings(List<int> listingIds) async {
    try {
      final response = await _apiConsumer.post(
        '${EndPoints.hostListings}/bulk/archive',
        data: {
          'listing_ids': listingIds,
        },
      );

      if (response['success'] == true) {
        return response['data'] ?? {};
      }

      throw Exception('Failed to archive listings');
    } catch (e) {
      throw Exception('Failed to bulk archive listings: $e');
    }
  }

  /// Bulk export listings
  Future<String> bulkExportListings(
    List<int> listingIds, {
    String format = 'csv', // 'csv', 'excel', 'pdf'
  }) async {
    try {
      final response = await _apiConsumer.post(
        '${EndPoints.hostListings}/bulk/export',
        data: {
          'listing_ids': listingIds,
          'format': format,
        },
      );

      if (response['success'] == true && response['data'] != null) {
        return response['data']['download_url'] ?? '';
      }

      throw Exception('Failed to export listings');
    } catch (e) {
      throw Exception('Failed to bulk export listings: $e');
    }
  }

  /// Bulk update availability
  Future<Map<String, dynamic>> bulkUpdateAvailability(
    List<int> listingIds,
    DateTime startDate,
    DateTime endDate,
    bool isAvailable,
  ) async {
    try {
      final response = await _apiConsumer.post(
        '${EndPoints.hostListings}/bulk/availability',
        data: {
          'listing_ids': listingIds,
          'start_date': startDate.toIso8601String().split('T')[0],
          'end_date': endDate.toIso8601String().split('T')[0],
          'is_available': isAvailable,
        },
      );

      if (response['success'] == true) {
        return response['data'] ?? {};
      }

      throw Exception('Failed to update availability');
    } catch (e) {
      throw Exception('Failed to bulk update availability: $e');
    }
  }

  /// Bulk update amenities
  Future<Map<String, dynamic>> bulkUpdateAmenities(
    List<int> listingIds,
    List<int> amenityIds,
    String operation, // 'add', 'remove', 'replace'
  ) async {
    try {
      final response = await _apiConsumer.post(
        '${EndPoints.hostListings}/bulk/amenities',
        data: {
          'listing_ids': listingIds,
          'amenity_ids': amenityIds,
          'operation': operation,
        },
      );

      if (response['success'] == true) {
        return response['data'] ?? {};
      }

      throw Exception('Failed to update amenities');
    } catch (e) {
      throw Exception('Failed to bulk update amenities: $e');
    }
  }

  /// Get bulk operation status
  Future<Map<String, dynamic>> getBulkOperationStatus(String operationId) async {
    try {
      final response = await _apiConsumer.get(
        '${EndPoints.hostListings}/bulk/status/$operationId',
      );

      if (response['success'] == true) {
        return response['data'] ?? {};
      }

      throw Exception('Failed to get operation status');
    } catch (e) {
      throw Exception('Failed to get bulk operation status: $e');
    }
  }

  /// Validate bulk operation
  Future<Map<String, dynamic>> validateBulkOperation(
    List<int> listingIds,
    String operation,
    Map<String, dynamic>? parameters,
  ) async {
    try {
      final response = await _apiConsumer.post(
        '${EndPoints.hostListings}/bulk/validate',
        data: {
          'listing_ids': listingIds,
          'operation': operation,
          'parameters': parameters ?? {},
        },
      );

      if (response['success'] == true) {
        return response['data'] ?? {};
      }

      return {
        'valid': false,
        'errors': ['Validation failed'],
      };
    } catch (e) {
      return {
        'valid': false,
        'errors': ['Validation error: $e'],
      };
    }
  }

  /// Get bulk operation history
  Future<List<Map<String, dynamic>>> getBulkOperationHistory({
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final response = await _apiConsumer.get(
        '${EndPoints.hostListings}/bulk/history',
        queryParameters: {
          'page': page,
          'limit': limit,
        },
      );

      if (response['success'] == true && response['data'] != null) {
        final List<dynamic> historyData = response['data']['operations'] ?? [];
        return historyData.cast<Map<String, dynamic>>();
      }

      return [];
    } catch (e) {
      throw Exception('Failed to get bulk operation history: $e');
    }
  }

  /// Cancel bulk operation
  Future<bool> cancelBulkOperation(String operationId) async {
    try {
      final response = await _apiConsumer.post(
        '${EndPoints.hostListings}/bulk/cancel/$operationId',
      );

      return response['success'] == true;
    } catch (e) {
      throw Exception('Failed to cancel bulk operation: $e');
    }
  }

  /// Get bulk operation limits
  Future<Map<String, dynamic>> getBulkOperationLimits() async {
    try {
      final response = await _apiConsumer.get(
        '${EndPoints.hostListings}/bulk/limits',
      );

      if (response['success'] == true && response['data'] != null) {
        return response['data'];
      }

      return {
        'max_listings_per_operation': 100,
        'max_operations_per_hour': 10,
        'max_operations_per_day': 50,
      };
    } catch (e) {
      // Return default limits on error
      return {
        'max_listings_per_operation': 100,
        'max_operations_per_hour': 10,
        'max_operations_per_day': 50,
      };
    }
  }
}
