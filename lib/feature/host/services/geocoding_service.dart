import 'package:dio/dio.dart';

class GeocodingService {
  static const String _baseUrl = 'https://nominatim.openstreetmap.org';
  static final Dio _dio = Dio();

  /// Search for places using Nominatim geocoding service
  static Future<List<PlaceSearchResult>> searchPlaces(String query) async {
    if (query.trim().isEmpty) return [];

    try {
      final encodedQuery = Uri.encodeComponent(query);
      final url = '$_baseUrl/search?q=$encodedQuery&format=json&limit=10&addressdetails=1&countrycodes=sa';

      final response = await _dio.get(
        url,
        options: Options(
          headers: {
            'User-Agent': 'GatherPoint/1.0 (<EMAIL>)',
          },
        ),
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data;
        return data.map((item) => PlaceSearchResult.fromJson(item)).toList();
      } else {
        throw Exception('Failed to search places: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error searching places: $e');
    }
  }
  
  /// Reverse geocoding - get address from coordinates
  static Future<String?> getAddressFromCoordinates(double lat, double lon) async {
    try {
      final url = '$_baseUrl/reverse?lat=$lat&lon=$lon&format=json&addressdetails=1';

      final response = await _dio.get(
        url,
        options: Options(
          headers: {
            'User-Agent': 'GatherPoint/1.0 (<EMAIL>)',
          },
        ),
      );

      if (response.statusCode == 200) {
        final data = response.data;
        return data['display_name'] as String?;
      }
    } catch (e) {
      // Silently fail for reverse geocoding
    }

    return null;
  }
}

class PlaceSearchResult {
  final String displayName;
  final double latitude;
  final double longitude;
  final String? city;
  final String? state;
  final String? country;
  final String? postcode;
  final String? road;
  final String? houseNumber;
  final String type;
  final double importance;

  PlaceSearchResult({
    required this.displayName,
    required this.latitude,
    required this.longitude,
    this.city,
    this.state,
    this.country,
    this.postcode,
    this.road,
    this.houseNumber,
    required this.type,
    required this.importance,
  });

  factory PlaceSearchResult.fromJson(Map<String, dynamic> json) {
    final address = json['address'] as Map<String, dynamic>?;
    
    return PlaceSearchResult(
      displayName: json['display_name'] ?? '',
      latitude: double.parse(json['lat'].toString()),
      longitude: double.parse(json['lon'].toString()),
      city: address?['city'] ?? address?['town'] ?? address?['village'],
      state: address?['state'],
      country: address?['country'],
      postcode: address?['postcode'],
      road: address?['road'],
      houseNumber: address?['house_number'],
      type: json['type'] ?? 'unknown',
      importance: double.tryParse(json['importance']?.toString() ?? '0') ?? 0.0,
    );
  }

  String get shortAddress {
    final parts = <String>[];
    
    if (houseNumber != null && road != null) {
      parts.add('$houseNumber $road');
    } else if (road != null) {
      parts.add(road!);
    }
    
    if (city != null) {
      parts.add(city!);
    }
    
    if (state != null && state != city) {
      parts.add(state!);
    }
    
    return parts.isNotEmpty ? parts.join(', ') : displayName;
  }

  String get fullAddress => displayName;

  @override
  String toString() {
    return 'PlaceSearchResult(displayName: $displayName, lat: $latitude, lon: $longitude)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PlaceSearchResult &&
        other.latitude == latitude &&
        other.longitude == longitude;
  }

  @override
  int get hashCode => latitude.hashCode ^ longitude.hashCode;
}
