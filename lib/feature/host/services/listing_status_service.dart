import 'package:gather_point/feature/host/data/models/my_listing_model.dart';

class ListingStatusService {
  /// Check if a status transition is allowed
  static bool canTransitionTo(String currentStatus, String newStatus) {
    if (currentStatus == newStatus) return false;

    switch (currentStatus) {
      case 'draft':
        return ['pending', 'active'].contains(newStatus);
      
      case 'pending':
        return ['draft', 'active', 'inactive'].contains(newStatus);
      
      case 'active':
        return ['inactive'].contains(newStatus);
      
      case 'inactive':
        return ['active', 'draft'].contains(newStatus);
      
      case 'suspended':
        return false; // Can't change from suspended status
      
      default:
        return false;
    }
  }

  /// Get available status transitions for a listing
  static List<String> getAvailableTransitions(String currentStatus) {
    switch (currentStatus) {
      case 'draft':
        return ['pending', 'active'];
      
      case 'pending':
        return ['draft', 'active', 'inactive'];
      
      case 'active':
        return ['inactive'];
      
      case 'inactive':
        return ['active', 'draft'];
      
      case 'suspended':
        return [];
      
      default:
        return [];
    }
  }

  /// Check if a listing can be published (made active)
  static bool canPublish(MyListingModel listing) {
    // Check if listing has required data
    if (listing.title.trim().isEmpty) return false;
    if (listing.content.trim().isEmpty) return false;
    if (listing.price <= 0) return false;
    if (listing.mainImageUrl == null || listing.mainImageUrl!.isEmpty) return false;
    if (listing.lat == null || listing.lon == null) return false;
    
    // Check status
    return ['draft', 'inactive'].contains(listing.status);
  }

  /// Check if a listing can be deactivated
  static bool canDeactivate(MyListingModel listing) {
    return listing.status == 'active' && !listing.hasActiveReservations;
  }

  /// Check if a listing can be deleted
  static bool canDelete(MyListingModel listing) {
    return !listing.hasActiveReservations && 
           !['suspended'].contains(listing.status);
  }

  /// Check if a listing can be duplicated
  static bool canDuplicate(MyListingModel listing) {
    return ['active', 'inactive', 'draft'].contains(listing.status);
  }

  /// Get status priority for sorting (higher number = higher priority)
  static int getStatusPriority(String status) {
    switch (status) {
      case 'suspended':
        return 5; // Highest priority - needs attention
      case 'pending':
        return 4; // High priority - awaiting review
      case 'active':
        return 3; // Medium-high priority - earning money
      case 'inactive':
        return 2; // Medium priority - not earning
      case 'draft':
        return 1; // Low priority - not complete
      default:
        return 0;
    }
  }

  /// Get status color for UI
  static String getStatusColor(String status) {
    switch (status) {
      case 'active':
        return '#4CAF50'; // Green
      case 'inactive':
        return '#FF9800'; // Orange
      case 'draft':
        return '#9E9E9E'; // Grey
      case 'pending':
        return '#2196F3'; // Blue
      case 'suspended':
        return '#F44336'; // Red
      default:
        return '#9E9E9E'; // Grey
    }
  }

  /// Get status icon for UI
  static String getStatusIcon(String status) {
    switch (status) {
      case 'active':
        return 'visibility';
      case 'inactive':
        return 'visibility_off';
      case 'draft':
        return 'edit';
      case 'pending':
        return 'pending';
      case 'suspended':
        return 'block';
      default:
        return 'help';
    }
  }

  /// Check if status requires admin approval
  static bool requiresApproval(String status) {
    return status == 'pending';
  }

  /// Check if status allows bookings
  static bool allowsBookings(String status) {
    return status == 'active';
  }

  /// Check if status is visible to guests
  static bool isVisibleToGuests(String status) {
    return status == 'active';
  }

  /// Get recommended actions for a listing based on its status and data
  static List<String> getRecommendedActions(MyListingModel listing) {
    final actions = <String>[];

    switch (listing.status) {
      case 'draft':
        if (canPublish(listing)) {
          actions.add('publish');
        } else {
          actions.add('complete_listing');
        }
        break;

      case 'pending':
        actions.add('wait_for_approval');
        if (listing.rejectionReason != null) {
          actions.add('address_feedback');
        }
        break;

      case 'active':
        if (listing.views == 0) {
          actions.add('improve_visibility');
        }
        if (listing.bookings == 0) {
          actions.add('optimize_pricing');
        }
        if (listing.rating != null && listing.rating! < 4.0) {
          actions.add('improve_quality');
        }
        break;

      case 'inactive':
        actions.add('reactivate');
        actions.add('update_listing');
        break;

      case 'suspended':
        actions.add('contact_support');
        if (listing.rejectionReason != null) {
          actions.add('resolve_issues');
        }
        break;
    }

    return actions;
  }

  /// Get status transition warnings
  static List<String> getTransitionWarnings(
    MyListingModel listing,
    String newStatus,
  ) {
    final warnings = <String>[];

    // Warn about deactivating with active reservations
    if (newStatus == 'inactive' && listing.hasActiveReservations) {
      warnings.add('listing_has_active_reservations');
    }

    // Warn about publishing incomplete listing
    if (newStatus == 'active' && !canPublish(listing)) {
      warnings.add('listing_incomplete');
    }

    // Warn about revenue impact
    if (listing.status == 'active' && newStatus != 'active') {
      warnings.add('will_stop_earning');
    }

    // Warn about visibility impact
    if (isVisibleToGuests(listing.status) && !isVisibleToGuests(newStatus)) {
      warnings.add('will_become_invisible');
    }

    return warnings;
  }

  /// Validate bulk status change
  static Map<String, dynamic> validateBulkStatusChange(
    List<MyListingModel> listings,
    String newStatus,
  ) {
    final result = <String, dynamic>{
      'canProceed': true,
      'warnings': <String>[],
      'errors': <String>[],
      'affectedCount': 0,
    };

    int affectedCount = 0;
    final warnings = <String>[];
    final errors = <String>[];

    for (final listing in listings) {
      if (listing.status == newStatus) continue;

      if (!canTransitionTo(listing.status, newStatus)) {
        errors.add('Cannot change ${listing.title} from ${listing.status} to $newStatus');
        continue;
      }

      final listingWarnings = getTransitionWarnings(listing, newStatus);
      warnings.addAll(listingWarnings);
      affectedCount++;
    }

    result['affectedCount'] = affectedCount;
    result['warnings'] = warnings.toSet().toList(); // Remove duplicates
    result['errors'] = errors;
    result['canProceed'] = errors.isEmpty && affectedCount > 0;

    return result;
  }

  /// Get status statistics for a list of listings
  static Map<String, int> getStatusStatistics(List<MyListingModel> listings) {
    final stats = <String, int>{
      'active': 0,
      'inactive': 0,
      'draft': 0,
      'pending': 0,
      'suspended': 0,
      'total': listings.length,
    };

    for (final listing in listings) {
      stats[listing.status] = (stats[listing.status] ?? 0) + 1;
    }

    return stats;
  }

  /// Get performance insights based on status
  static Map<String, dynamic> getPerformanceInsights(List<MyListingModel> listings) {
    final insights = <String, dynamic>{};
    
    final activeListings = listings.where((l) => l.status == 'active').toList();
    final inactiveListings = listings.where((l) => l.status == 'inactive').toList();
    final draftListings = listings.where((l) => l.status == 'draft').toList();

    insights['activeEarningPotential'] = activeListings.fold<double>(
      0.0,
      (sum, listing) => sum + listing.price,
    );

    insights['missedOpportunities'] = inactiveListings.length + draftListings.length;

    insights['averageActiveRating'] = activeListings.isNotEmpty
        ? activeListings
            .where((l) => l.rating != null)
            .fold<double>(0.0, (sum, l) => sum + l.rating!) /
            activeListings.where((l) => l.rating != null).length
        : 0.0;

    insights['needsAttention'] = listings
        .where((l) => ['suspended', 'pending'].contains(l.status))
        .length;

    return insights;
  }
}
