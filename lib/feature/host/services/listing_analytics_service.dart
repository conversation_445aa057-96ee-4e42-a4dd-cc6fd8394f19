import 'package:dio/dio.dart';
import 'package:gather_point/core/databases/api/end_points.dart';
import 'package:gather_point/core/databases/api/api_consumer.dart';
import 'package:gather_point/feature/host/data/models/listing_analytics_model.dart';

class ListingAnalyticsService {
  final ApiConsumer _apiConsumer;

  ListingAnalyticsService(this._apiConsumer);

  /// Get analytics for a specific listing
  Future<ListingAnalyticsModel> getListingAnalytics(
    int listingId, {
    String period = '30d',
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'period': period,
      };

      if (startDate != null) {
        queryParams['start_date'] = startDate.toIso8601String().split('T')[0];
      }
      if (endDate != null) {
        queryParams['end_date'] = endDate.toIso8601String().split('T')[0];
      }

      final response = await _apiConsumer.get(
        '${EndPoints.hostListings}/$listingId/analytics',
        queryParameters: queryParams,
      );

      if (response['success'] == true && response['data'] != null) {
        return ListingAnalyticsModel.fromJson(response['data']);
      }

      throw Exception('Failed to get listing analytics');
    } catch (e) {
      throw Exception('Failed to fetch listing analytics: $e');
    }
  }

  /// Get analytics summary for multiple listings
  Future<Map<String, dynamic>> getListingsAnalyticsSummary({
    List<int>? listingIds,
    String period = '30d',
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'period': period,
      };

      if (listingIds != null && listingIds.isNotEmpty) {
        queryParams['listing_ids'] = listingIds.join(',');
      }

      final response = await _apiConsumer.get(
        '${EndPoints.hostListings}/analytics/summary',
        queryParameters: queryParams,
      );

      if (response['success'] == true && response['data'] != null) {
        return response['data'];
      }

      return {};
    } catch (e) {
      throw Exception('Failed to fetch analytics summary: $e');
    }
  }

  /// Get performance comparison with market
  Future<Map<String, dynamic>> getMarketComparison(
    int listingId, {
    String period = '30d',
  }) async {
    try {
      final response = await _apiConsumer.get(
        '${EndPoints.hostListings}/$listingId/analytics/market-comparison',
        queryParameters: {
          'period': period,
        },
      );

      if (response['success'] == true && response['data'] != null) {
        return response['data'];
      }

      return {};
    } catch (e) {
      throw Exception('Failed to fetch market comparison: $e');
    }
  }

  /// Get revenue breakdown
  Future<Map<String, dynamic>> getRevenueBreakdown(
    int listingId, {
    String period = '30d',
  }) async {
    try {
      final response = await _apiConsumer.get(
        '${EndPoints.hostListings}/$listingId/analytics/revenue',
        queryParameters: {
          'period': period,
        },
      );

      if (response['success'] == true && response['data'] != null) {
        return response['data'];
      }

      return {};
    } catch (e) {
      throw Exception('Failed to fetch revenue breakdown: $e');
    }
  }

  /// Get booking patterns
  Future<Map<String, dynamic>> getBookingPatterns(
    int listingId, {
    String period = '30d',
  }) async {
    try {
      final response = await _apiConsumer.get(
        '${EndPoints.hostListings}/$listingId/analytics/booking-patterns',
        queryParameters: {
          'period': period,
        },
      );

      if (response['success'] == true && response['data'] != null) {
        return response['data'];
      }

      return {};
    } catch (e) {
      throw Exception('Failed to fetch booking patterns: $e');
    }
  }

  /// Get guest demographics
  Future<Map<String, dynamic>> getGuestDemographics(
    int listingId, {
    String period = '30d',
  }) async {
    try {
      final response = await _apiConsumer.get(
        '${EndPoints.hostListings}/$listingId/analytics/demographics',
        queryParameters: {
          'period': period,
        },
      );

      if (response['success'] == true && response['data'] != null) {
        return response['data'];
      }

      return {};
    } catch (e) {
      throw Exception('Failed to fetch guest demographics: $e');
    }
  }

  /// Get performance insights and recommendations
  Future<List<PerformanceInsight>> getPerformanceInsights(
    int listingId, {
    String period = '30d',
  }) async {
    try {
      final response = await _apiConsumer.get(
        '${EndPoints.hostListings}/$listingId/analytics/insights',
        queryParameters: {
          'period': period,
        },
      );

      if (response['success'] == true && response['data'] != null) {
        final List<dynamic> insightsData = response['data']['insights'] ?? [];
        return insightsData.map((item) => PerformanceInsight.fromJson(item)).toList();
      }

      return [];
    } catch (e) {
      throw Exception('Failed to fetch performance insights: $e');
    }
  }

  /// Export analytics data
  Future<String> exportAnalytics(
    int listingId, {
    String period = '30d',
    String format = 'csv', // 'csv', 'pdf', 'excel'
  }) async {
    try {
      final response = await _apiConsumer.get(
        '${EndPoints.hostListings}/$listingId/analytics/export',
        queryParameters: {
          'period': period,
          'format': format,
        },
      );

      if (response['success'] == true && response['data'] != null) {
        return response['data']['download_url'] ?? '';
      }

      throw Exception('Failed to export analytics');
    } catch (e) {
      throw Exception('Failed to export analytics: $e');
    }
  }

  /// Get real-time metrics
  Future<Map<String, dynamic>> getRealTimeMetrics(int listingId) async {
    try {
      final response = await _apiConsumer.get(
        '${EndPoints.hostListings}/$listingId/analytics/realtime',
      );

      if (response['success'] == true && response['data'] != null) {
        return response['data'];
      }

      return {};
    } catch (e) {
      throw Exception('Failed to fetch real-time metrics: $e');
    }
  }

  /// Generate mock analytics data for development
  static ListingAnalyticsModel generateMockAnalytics(int listingId) {
    final now = DateTime.now();
    final startDate = now.subtract(const Duration(days: 30));
    
    // Generate mock daily data
    final dailyViews = <DailyMetric>[];
    final dailyBookings = <DailyMetric>[];
    final dailyRevenue = <DailyMetric>[];
    
    for (int i = 0; i < 30; i++) {
      final date = startDate.add(Duration(days: i));
      dailyViews.add(DailyMetric(
        date: date,
        value: (10 + (i % 7) * 5 + (i % 3) * 2).toDouble(),
      ));
      dailyBookings.add(DailyMetric(
        date: date,
        value: (i % 5 == 0 ? 1 : 0).toDouble(),
      ));
      dailyRevenue.add(DailyMetric(
        date: date,
        value: (i % 5 == 0 ? 500 + (i % 3) * 200 : 0).toDouble(),
      ));
    }

    // Generate mock monthly trends
    final monthlyTrends = <MonthlyMetric>[
      MonthlyMetric(year: 2024, month: 10, views: 450, bookings: 8, revenue: 6400, occupancyRate: 0.65),
      MonthlyMetric(year: 2024, month: 11, views: 520, bookings: 12, revenue: 9600, occupancyRate: 0.75),
      MonthlyMetric(year: 2024, month: 12, views: 680, bookings: 15, revenue: 12000, occupancyRate: 0.85),
    ];

    // Generate mock insights
    final insights = <PerformanceInsight>[
      PerformanceInsight(
        type: 'positive',
        title: 'أداء ممتاز في المشاهدات',
        description: 'زادت مشاهدات عقارك بنسبة 25% مقارنة بالشهر الماضي',
      ),
      PerformanceInsight(
        type: 'negative',
        title: 'معدل تحويل منخفض',
        description: 'معدل التحويل أقل من المتوسط. فكر في تحسين الصور والوصف',
        actionText: 'تحسين العقار',
        actionRoute: '/edit-property',
      ),
      PerformanceInsight(
        type: 'neutral',
        title: 'موسم الذروة قادم',
        description: 'استعد لموسم الذروة بتحديث التقويم والأسعار',
        actionText: 'إدارة التقويم',
        actionRoute: '/calendar',
      ),
    ];

    return ListingAnalyticsModel(
      listingId: listingId,
      period: '30d',
      startDate: startDate,
      endDate: now,
      totalViews: 680,
      uniqueViews: 520,
      totalBookings: 15,
      confirmedBookings: 12,
      cancelledBookings: 3,
      totalRevenue: 12000,
      netRevenue: 10200,
      averageDailyRate: 800,
      occupancyRate: 0.75,
      conversionRate: 0.022,
      averageRating: 4.6,
      totalReviews: 8,
      responseTime: 45,
      responseRate: 0.95,
      favoriteCount: 23,
      shareCount: 12,
      dailyViews: dailyViews,
      dailyBookings: dailyBookings,
      dailyRevenue: dailyRevenue,
      monthlyTrends: monthlyTrends,
      previousPeriod: ComparisonMetrics(
        viewsChange: 0.25,
        bookingsChange: 0.15,
        revenueChange: 0.30,
        conversionRateChange: -0.05,
        occupancyRateChange: 0.10,
      ),
      marketAverage: ComparisonMetrics(
        viewsChange: 0.10,
        bookingsChange: 0.05,
        revenueChange: 0.12,
        conversionRateChange: 0.02,
        occupancyRateChange: 0.08,
      ),
      insights: insights,
      recommendations: [
        'أضف المزيد من الصور عالية الجودة',
        'حدث وصف العقار ليكون أكثر جاذبية',
        'فكر في تقليل السعر قليلاً لزيادة الحجوزات',
        'رد على الاستفسارات بشكل أسرع',
      ],
    );
  }
}
