import 'package:flutter/material.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/feature/host/data/models/my_listing_model.dart';
import 'package:gather_point/feature/host/widgets/bulk_status_manager.dart';
import 'package:gather_point/feature/host/widgets/advanced_bulk_actions_manager.dart';

class BulkActionsToolbar extends StatelessWidget {
  final List<MyListingModel> selectedListings;
  final VoidCallback onClearSelection;
  final VoidCallback onSelectAll;
  final VoidCallback onComplete;
  final bool isAllSelected;

  const BulkActionsToolbar({
    super.key,
    required this.selectedListings,
    required this.onClearSelection,
    required this.onSelectAll,
    required this.onComplete,
    required this.isAllSelected,
  });

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      height: selectedListings.isNotEmpty ? 80 : 0,
      child: selectedListings.isNotEmpty
          ? Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: const Color(0xFFFEC53A).withValues(alpha: 0.1),
                border: Border(
                  top: BorderSide(
                    color: const Color(0xFFFEC53A).withValues(alpha: 0.3),
                  ),
                ),
              ),
              child: Row(
                children: [
                  // Selection Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          '${selectedListings.length} ${s.itemsSelected}',
                          style: AppTextStyles.font14SemiBold.copyWith(
                            color: const Color(0xFFFEC53A),
                          ),
                        ),
                        Text(
                          _getTotalValue(),
                          style: AppTextStyles.font12Regular.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Quick Actions
                  Row(
                    children: [
                      // Select All/Clear
                      IconButton(
                        onPressed: isAllSelected ? onClearSelection : onSelectAll,
                        icon: Icon(
                          isAllSelected ? Icons.clear_all : Icons.select_all,
                          color: const Color(0xFFFEC53A),
                        ),
                        tooltip: isAllSelected ? s.clearSelection : s.selectAll,
                      ),

                      // Quick Status Actions
                      _buildQuickActionButton(
                        icon: Icons.visibility,
                        color: Colors.green,
                        tooltip: s.activate,
                        onPressed: () => _showQuickStatusAction(context, 'activate'),
                      ),

                      _buildQuickActionButton(
                        icon: Icons.visibility_off,
                        color: Colors.orange,
                        tooltip: s.deactivate,
                        onPressed: () => _showQuickStatusAction(context, 'deactivate'),
                      ),

                      _buildQuickActionButton(
                        icon: Icons.delete,
                        color: Colors.red,
                        tooltip: 'حذف',
                        onPressed: () => _showQuickStatusAction(context, 'delete'),
                      ),

                      // More Actions
                      _buildQuickActionButton(
                        icon: Icons.more_vert,
                        color: const Color(0xFFFEC53A),
                        tooltip: s.moreActions,
                        onPressed: () => _showAdvancedActions(context),
                      ),
                    ],
                  ),
                ],
              ),
            )
          : const SizedBox.shrink(),
    );
  }

  Widget _buildQuickActionButton({
    required IconData icon,
    required Color color,
    required String tooltip,
    required VoidCallback onPressed,
  }) {
    return Container(
      margin: const EdgeInsets.only(left: 8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: IconButton(
        onPressed: onPressed,
        icon: Icon(icon, color: color, size: 20),
        tooltip: tooltip,
        constraints: const BoxConstraints(
          minWidth: 40,
          minHeight: 40,
        ),
      ),
    );
  }

  String _getTotalValue() {
    final totalValue = selectedListings.fold<double>(
      0.0,
      (sum, listing) => sum + listing.price,
    );
    return 'القيمة الإجمالية: ${totalValue.toStringAsFixed(0)} ر.س';
  }

  void _showQuickStatusAction(BuildContext context, String action) {
    final s = S.of(context);
    
    String title;
    String message;
    Color color;
    
    switch (action) {
      case 'activate':
        title = s.activateSelected;
        message = s.activateSelectedConfirmation;
        color = Colors.green;
        break;
      case 'deactivate':
        title = s.deactivateSelected;
        message = s.deactivateSelectedConfirmation;
        color = Colors.orange;
        break;
      case 'delete':
        title = s.deleteSelected;
        message = s.deleteSelectedConfirmation;
        color = Colors.red;
        break;
      default:
        return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(message),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: color.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  Icon(Icons.info_outline, color: color, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '${selectedListings.length} ${s.listingsWillBeAffected}',
                      style: AppTextStyles.font12Regular.copyWith(
                        color: color,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(s.cancel),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _executeQuickAction(context, action);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: color,
              foregroundColor: Colors.white,
            ),
            child: Text(s.confirm),
          ),
        ],
      ),
    );
  }

  void _executeQuickAction(BuildContext context, String action) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => BulkStatusManager(
        selectedListings: selectedListings,
        onComplete: onComplete,
      ),
    );
  }

  void _showAdvancedActions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        minChildSize: 0.5,
        maxChildSize: 0.9,
        builder: (context, scrollController) => Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
          ),
          child: SingleChildScrollView(
            controller: scrollController,
            child: AdvancedBulkActionsManager(
              selectedListings: selectedListings,
              onComplete: onComplete,
            ),
          ),
        ),
      ),
    );
  }
}

// Extension to add bulk selection functionality to listings
extension BulkSelectionExtension on List<MyListingModel> {
  List<MyListingModel> getByStatus(String status) {
    return where((listing) => listing.status == status).toList();
  }

  double getTotalValue() {
    return fold<double>(0.0, (sum, listing) => sum + listing.price);
  }

  Map<String, int> getStatusBreakdown() {
    final breakdown = <String, int>{};
    for (final listing in this) {
      breakdown[listing.status] = (breakdown[listing.status] ?? 0) + 1;
    }
    return breakdown;
  }

  bool canPerformAction(String action) {
    switch (action) {
      case 'activate':
        return any((l) => ['inactive', 'draft'].contains(l.status));
      case 'deactivate':
        return any((l) => l.status == 'active');
      case 'delete':
        return isNotEmpty;
      case 'duplicate':
        return isNotEmpty;
      case 'export':
        return isNotEmpty;
      default:
        return false;
    }
  }
}
