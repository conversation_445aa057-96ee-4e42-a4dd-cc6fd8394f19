import 'package:flutter/material.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';

class ListingsErrorWidget extends StatelessWidget {
  final String message;
  final VoidCallback onRetry;

  const ListingsErrorWidget({
    super.key,
    required this.message,
    required this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Error Icon
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: const Center(
                child: Icon(
                  Icons.error_outline,
                  size: 60,
                  color: Colors.red,
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Title
            Text(
              s.errorLoadingListings,
              style: AppTextStyles.font20Bold,
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 16),

            // Error Message
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.withValues(alpha: 0.2)),
              ),
              child: Text(
                message,
                style: AppTextStyles.font14Regular.copyWith(
                  color: Colors.red[700],
                ),
                textAlign: TextAlign.center,
              ),
            ),

            const SizedBox(height: 24),

            // Retry Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: onRetry,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFFEC53A),
                  foregroundColor: Colors.black,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                icon: const Icon(Icons.refresh),
                label: Text(
                  s.tryAgain,
                  style: AppTextStyles.font16SemiBold,
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Help Text
            Text(
              s.errorPersistsContact,
              style: AppTextStyles.font12Regular.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
