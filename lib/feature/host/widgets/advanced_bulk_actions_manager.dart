import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/feature/host/data/models/my_listing_model.dart';
import 'package:gather_point/feature/host/presentation/cubit/my_listings_cubit.dart';

class AdvancedBulkActionsManager extends StatefulWidget {
  final List<MyListingModel> selectedListings;
  final VoidCallback onComplete;

  const AdvancedBulkActionsManager({
    super.key,
    required this.selectedListings,
    required this.onComplete,
  });

  @override
  State<AdvancedBulkActionsManager> createState() => _AdvancedBulkActionsManagerState();
}

class _AdvancedBulkActionsManagerState extends State<AdvancedBulkActionsManager> {
  String? _selectedAction;
  bool _isLoading = false;
  final Map<String, dynamic> _actionParameters = {};

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return Container(
      padding: const EdgeInsets.all(24),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Handle
          Center(
            child: Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Header
          Row(
            children: [
              Icon(
                Icons.settings,
                color: const Color(0xFFFEC53A),
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  s.advancedBulkActions,
                  style: AppTextStyles.font18Bold,
                ),
              ),
              IconButton(
                onPressed: () => Navigator.pop(context),
                icon: const Icon(Icons.close),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Selection Summary
          _buildSelectionSummary(s),

          const SizedBox(height: 24),

          // Action Categories
          _buildActionCategories(s),

          const SizedBox(height: 24),

          // Action Parameters (if needed)
          if (_selectedAction != null && _needsParameters(_selectedAction!))
            _buildActionParameters(s),

          // Action Buttons
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: _isLoading ? null : () => Navigator.pop(context),
                  child: Text(s.cancel),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton(
                  onPressed: _isLoading || _selectedAction == null 
                      ? null 
                      : _executeAction,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFFEC53A),
                    foregroundColor: Colors.black,
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : Text(s.executeAction),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSelectionSummary(S s) {
    final totalRevenue = widget.selectedListings.fold<double>(
      0.0,
      (sum, listing) => sum + listing.price,
    );

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            const Color(0xFFFEC53A).withValues(alpha: 0.1),
            const Color(0xFFFEC53A).withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFFEC53A).withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.checklist,
                color: const Color(0xFFFEC53A),
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                '${widget.selectedListings.length} ${s.listingsSelected}',
                style: AppTextStyles.font16SemiBold,
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Text(
                '${s.totalValue}: ',
                style: AppTextStyles.font12Regular.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              Text(
                '${totalRevenue.toStringAsFixed(0)} ر.س',
                style: AppTextStyles.font12SemiBold.copyWith(
                  color: const Color(0xFFFEC53A),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionCategories(S s) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          s.selectActionCategory,
          style: AppTextStyles.font16SemiBold,
        ),
        const SizedBox(height: 16),

        // Status Actions
        _buildActionCategory(
          s.statusActions,
          Icons.toggle_on,
          Colors.blue,
          [
            'bulk_activate',
            'bulk_deactivate',
            'bulk_draft',
            'bulk_publish',
          ],
        ),

        const SizedBox(height: 16),

        // Pricing Actions
        _buildActionCategory(
          s.pricingActions,
          Icons.monetization_on,
          Colors.green,
          [
            'bulk_price_increase',
            'bulk_price_decrease',
            'bulk_price_set',
            'bulk_discount_apply',
          ],
        ),

        const SizedBox(height: 16),

        // Management Actions
        _buildActionCategory(
          s.managementActions,
          Icons.settings,
          Colors.orange,
          [
            'bulk_duplicate',
            'bulk_export',
            'bulk_archive',
            'bulk_delete',
          ],
        ),
      ],
    );
  }

  Widget _buildActionCategory(
    String title,
    IconData icon,
    Color color,
    List<String> actions,
  ) {
    return ExpansionTile(
      leading: Icon(icon, color: color),
      title: Text(
        title,
        style: AppTextStyles.font14SemiBold,
      ),
      children: actions.map((action) {
        return RadioListTile<String>(
          value: action,
          groupValue: _selectedAction,
          onChanged: (value) {
            setState(() {
              _selectedAction = value;
              _actionParameters.clear();
            });
          },
          title: Text(
            _getActionDisplayName(action),
            style: AppTextStyles.font14Regular,
          ),
          subtitle: Text(
            _getActionDescription(action),
            style: AppTextStyles.font12Regular.copyWith(
              color: Colors.grey[600],
            ),
          ),
          activeColor: const Color(0xFFFEC53A),
        );
      }).toList(),
    );
  }

  Widget _buildActionParameters(S s) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          s.actionParameters,
          style: AppTextStyles.font16SemiBold,
        ),
        const SizedBox(height: 12),
        
        if (_selectedAction == 'bulk_price_increase' || _selectedAction == 'bulk_price_decrease')
          _buildPercentageInput(s),
        
        if (_selectedAction == 'bulk_price_set')
          _buildPriceInput(s),
        
        if (_selectedAction == 'bulk_discount_apply')
          _buildDiscountInput(s),

        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildPercentageInput(S s) {
    return TextField(
      decoration: InputDecoration(
        labelText: s.percentage,
        hintText: '10',
        suffixText: '%',
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      keyboardType: TextInputType.number,
      onChanged: (value) {
        _actionParameters['percentage'] = double.tryParse(value) ?? 0.0;
      },
    );
  }

  Widget _buildPriceInput(S s) {
    return TextField(
      decoration: InputDecoration(
        labelText: s.newPrice,
        hintText: '500',
        suffixText: 'ر.س',
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      keyboardType: TextInputType.number,
      onChanged: (value) {
        _actionParameters['price'] = double.tryParse(value) ?? 0.0;
      },
    );
  }

  Widget _buildDiscountInput(S s) {
    return Column(
      children: [
        TextField(
          decoration: InputDecoration(
            labelText: s.discountPercentage,
            hintText: '20',
            suffixText: '%',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          keyboardType: TextInputType.number,
          onChanged: (value) {
            _actionParameters['discount'] = double.tryParse(value) ?? 0.0;
          },
        ),
        const SizedBox(height: 12),
        TextField(
          decoration: InputDecoration(
            labelText: s.discountDuration,
            hintText: '7',
            suffixText: s.days,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          keyboardType: TextInputType.number,
          onChanged: (value) {
            _actionParameters['duration'] = int.tryParse(value) ?? 0;
          },
        ),
      ],
    );
  }

  bool _needsParameters(String action) {
    return [
      'bulk_price_increase',
      'bulk_price_decrease',
      'bulk_price_set',
      'bulk_discount_apply',
    ].contains(action);
  }

  String _getActionDisplayName(String action) {
    final s = S.of(context);
    
    switch (action) {
      case 'bulk_activate':
        return s.activateAll;
      case 'bulk_deactivate':
        return s.deactivateAll;
      case 'bulk_draft':
        return s.convertToDraft;
      case 'bulk_publish':
        return s.publishAll;
      case 'bulk_price_increase':
        return s.increasePrices;
      case 'bulk_price_decrease':
        return s.decreasePrices;
      case 'bulk_price_set':
        return s.setPrices;
      case 'bulk_discount_apply':
        return s.applyDiscount;
      case 'bulk_duplicate':
        return s.duplicateAll;
      case 'bulk_export':
        return s.exportAll;
      case 'bulk_archive':
        return s.archiveAll;
      case 'bulk_delete':
        return s.deleteAll;
      default:
        return action;
    }
  }

  String _getActionDescription(String action) {
    final s = S.of(context);
    
    switch (action) {
      case 'bulk_activate':
        return s.activateAllDescription;
      case 'bulk_deactivate':
        return s.deactivateAllDescription;
      case 'bulk_draft':
        return s.convertToDraftDescription;
      case 'bulk_publish':
        return s.publishAllDescription;
      case 'bulk_price_increase':
        return s.increasePricesDescription;
      case 'bulk_price_decrease':
        return s.decreasePricesDescription;
      case 'bulk_price_set':
        return s.setPricesDescription;
      case 'bulk_discount_apply':
        return s.applyDiscountDescription;
      case 'bulk_duplicate':
        return s.duplicateAllDescription;
      case 'bulk_export':
        return s.exportAllDescription;
      case 'bulk_archive':
        return s.archiveAllDescription;
      case 'bulk_delete':
        return s.deleteAllDescription;
      default:
        return '';
    }
  }

  Future<void> _executeAction() async {
    if (_selectedAction == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final listingIds = widget.selectedListings.map((l) => l.id).toList();
      
      switch (_selectedAction) {
        case 'bulk_activate':
          await context.read<MyListingsCubit>().bulkUpdateStatus(listingIds, 'active');
          break;
        case 'bulk_deactivate':
          await context.read<MyListingsCubit>().bulkUpdateStatus(listingIds, 'inactive');
          break;
        case 'bulk_draft':
          await context.read<MyListingsCubit>().bulkUpdateStatus(listingIds, 'draft');
          break;
        case 'bulk_delete':
          await context.read<MyListingsCubit>().bulkDeleteListings(listingIds);
          break;
        // Add more actions here as needed
        default:
          // For actions not yet implemented in the cubit
          await Future.delayed(const Duration(seconds: 1)); // Simulate API call
      }
      
      if (mounted) {
        Navigator.pop(context);
        widget.onComplete();
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(S.of(context).bulkActionCompleted),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(S.of(context).bulkActionError),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
