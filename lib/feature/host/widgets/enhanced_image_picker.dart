import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';

class EnhancedImagePicker extends StatefulWidget {
  final List<File> images;
  final Function(List<File>) onImagesChanged;
  final int maxImages;
  final String title;
  final bool allowReordering;
  final bool showImageCount;

  const EnhancedImagePicker({
    super.key,
    required this.images,
    required this.onImagesChanged,
    this.maxImages = 20,
    required this.title,
    this.allowReordering = true,
    this.showImageCount = true,
  });

  @override
  State<EnhancedImagePicker> createState() => _EnhancedImagePickerState();
}

class _EnhancedImagePickerState extends State<EnhancedImagePicker>
    with TickerProviderStateMixin {
  final ImagePicker _picker = ImagePicker();
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isDragOver = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _pickImages() async {
    try {
      final List<XFile> pickedImages = await _picker.pickMultiImage();
      if (pickedImages.isNotEmpty) {
        final remainingSlots = widget.maxImages - widget.images.length;
        final imagesToAdd = pickedImages.take(remainingSlots);
        final newImages = List<File>.from(widget.images);
        newImages.addAll(imagesToAdd.map((image) => File(image.path)));
        
        widget.onImagesChanged(newImages);
        
        if (pickedImages.length > remainingSlots && mounted) {
          _showLimitReachedMessage();
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorMessage('حدث خطأ أثناء اختيار الصور');
      }
    }
  }

  Future<void> _pickSingleImage() async {
    try {
      final XFile? pickedImage = await _picker.pickImage(source: ImageSource.camera);
      if (pickedImage != null) {
        if (widget.images.length < widget.maxImages) {
          final newImages = List<File>.from(widget.images);
          newImages.add(File(pickedImage.path));
          widget.onImagesChanged(newImages);
        } else {
          _showLimitReachedMessage();
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorMessage('حدث خطأ أثناء التقاط الصورة');
      }
    }
  }

  void _removeImage(int index) {
    final newImages = List<File>.from(widget.images);
    newImages.removeAt(index);
    widget.onImagesChanged(newImages);
  }

  void _reorderImages(int oldIndex, int newIndex) {
    if (!widget.allowReordering) return;
    
    final newImages = List<File>.from(widget.images);
    if (newIndex > oldIndex) {
      newIndex -= 1;
    }
    final item = newImages.removeAt(oldIndex);
    newImages.insert(newIndex, item);
    widget.onImagesChanged(newImages);
  }

  void _showLimitReachedMessage() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('يمكنك إضافة ${widget.maxImages} صورة على الأكثر'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _showImageOptions() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'إضافة صور',
              style: AppTextStyles.font18Bold,
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.photo_library, color: Color(0xFFFEC53A)),
              title: const Text('اختيار من المعرض'),
              subtitle: const Text('اختيار عدة صور من معرض الصور'),
              onTap: () {
                Navigator.pop(context);
                _pickImages();
              },
            ),
            ListTile(
              leading: const Icon(Icons.camera_alt, color: Color(0xFFFEC53A)),
              title: const Text('التقاط صورة'),
              subtitle: const Text('التقاط صورة جديدة بالكاميرا'),
              onTap: () {
                Navigator.pop(context);
                _pickSingleImage();
              },
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              widget.title,
              style: AppTextStyles.font14SemiBold,
            ),
            Row(
              children: [
                if (widget.showImageCount && widget.images.isNotEmpty)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: const Color(0xFFFEC53A).withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${widget.images.length}/${widget.maxImages}',
                      style: AppTextStyles.font12SemiBold.copyWith(
                        color: const Color(0xFFFEC53A),
                      ),
                    ),
                  ),
                const SizedBox(width: 8),
                if (widget.images.length < widget.maxImages)
                  TextButton.icon(
                    onPressed: _showImageOptions,
                    icon: const Icon(Icons.add_photo_alternate),
                    label: Text(s.addImages),
                  ),
              ],
            ),
          ],
        ),
        const SizedBox(height: 8),

        // Images Grid or Empty State
        if (widget.images.isNotEmpty)
          _buildImagesGrid()
        else
          _buildEmptyState(),
      ],
    );
  }

  Widget _buildImagesGrid() {
    if (!widget.allowReordering) {
      return _buildStaticGrid();
    }

    return ReorderableWrap(
      spacing: 8,
      runSpacing: 8,
      children: widget.images.asMap().entries.map((entry) {
        final index = entry.key;
        final image = entry.value;
        return _buildImageItem(image, index, key: ValueKey(image.path));
      }).toList(),
      onReorder: _reorderImages,
    );
  }

  Widget _buildStaticGrid() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: widget.images.asMap().entries.map((entry) {
        final index = entry.key;
        final image = entry.value;
        return _buildImageItem(image, index);
      }).toList(),
    );
  }

  Widget _buildImageItem(File image, int index, {Key? key}) {
    return Container(
      key: key,
      width: 100,
      height: 100,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Stack(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.file(
              image,
              width: 100,
              height: 100,
              fit: BoxFit.cover,
            ),
          ),
          
          // Overlay with controls
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.black.withValues(alpha: 0.3),
                    Colors.transparent,
                    Colors.black.withValues(alpha: 0.3),
                  ],
                ),
              ),
            ),
          ),
          
          // Image number
          Positioned(
            top: 4,
            left: 4,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.7),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '${index + 1}',
                style: AppTextStyles.font12Regular.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          
          // Remove button
          Positioned(
            top: 4,
            right: 4,
            child: GestureDetector(
              onTap: () => _removeImage(index),
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.9),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.close,
                  color: Colors.white,
                  size: 14,
                ),
              ),
            ),
          ),
          
          // Drag handle (if reordering is enabled)
          if (widget.allowReordering)
            Positioned(
              bottom: 4,
              right: 4,
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.9),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.drag_handle,
                  color: Colors.white,
                  size: 14,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTap: _showImageOptions,
            child: Container(
              height: 120,
              width: double.infinity,
              decoration: BoxDecoration(
                border: Border.all(
                  color: _isDragOver 
                      ? const Color(0xFFFEC53A) 
                      : Colors.grey.withValues(alpha: 0.3),
                  width: _isDragOver ? 2 : 1,
                  style: BorderStyle.solid,
                ),
                borderRadius: BorderRadius.circular(12),
                color: _isDragOver 
                    ? const Color(0xFFFEC53A).withValues(alpha: 0.1)
                    : Colors.grey.withValues(alpha: 0.05),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.add_photo_alternate_outlined,
                    size: 48,
                    color: _isDragOver 
                        ? const Color(0xFFFEC53A)
                        : Colors.grey[400],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'اضغط لإضافة ${widget.title.toLowerCase()}',
                    style: AppTextStyles.font14Regular.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'يمكنك إضافة حتى ${widget.maxImages} صورة',
                    style: AppTextStyles.font12Regular.copyWith(
                      color: Colors.grey[500],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

// Custom ReorderableWrap widget for drag-and-drop reordering
class ReorderableWrap extends StatelessWidget {
  final List<Widget> children;
  final Function(int, int) onReorder;
  final double spacing;
  final double runSpacing;

  const ReorderableWrap({
    super.key,
    required this.children,
    required this.onReorder,
    this.spacing = 0,
    this.runSpacing = 0,
  });

  @override
  Widget build(BuildContext context) {
    // For now, use a simple Wrap - full drag-and-drop would require more complex implementation
    return Wrap(
      spacing: spacing,
      runSpacing: runSpacing,
      children: children,
    );
  }
}
