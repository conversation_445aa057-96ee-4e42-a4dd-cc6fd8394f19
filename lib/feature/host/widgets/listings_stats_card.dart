import 'package:flutter/material.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/feature/host/data/models/listing_stats_model.dart';

class ListingsStatsCard extends StatelessWidget {
  final ListingStatsModel stats;

  const ListingsStatsCard({
    super.key,
    required this.stats,
  });

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFFFEC53A),
            Color(0xFFFFD700),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFFFEC53A).withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              const Icon(
                Icons.dashboard,
                color: Colors.black,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                s.dashboardOverview,
                style: AppTextStyles.font18Bold.copyWith(
                  color: Colors.black,
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${stats.totalListings} ${stats.totalListings == 1 ? 'عقار' : 'عقارات'}',
                  style: AppTextStyles.font12SemiBold.copyWith(
                    color: Colors.black,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Main Stats Row
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  icon: Icons.monetization_on,
                  label: s.totalRevenue,
                  value: stats.totalRevenueDisplay,
                  color: Colors.green[700]!,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatItem(
                  icon: Icons.calendar_today,
                  label: s.totalBookings,
                  value: stats.totalBookings.toString(),
                  color: Colors.blue[700]!,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatItem(
                  icon: Icons.star,
                  label: s.averageRating,
                  value: stats.averageRatingDisplay,
                  color: Colors.orange[700]!,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Secondary Stats
          Row(
            children: [
              Expanded(
                child: _buildMiniStat(
                  label: s.activeListings,
                  value: stats.activeListings.toString(),
                  color: Colors.green,
                ),
              ),
              Expanded(
                child: _buildMiniStat(
                  label: s.totalViews,
                  value: _formatNumber(stats.totalViews),
                  color: Colors.blue,
                ),
              ),
              Expanded(
                child: _buildMiniStat(
                  label: s.occupancyRate,
                  value: stats.occupancyRateDisplay,
                  color: Colors.purple,
                ),
              ),
              Expanded(
                child: _buildMiniStat(
                  label: s.conversionRate,
                  value: stats.conversionRateDisplay,
                  color: Colors.teal,
                ),
              ),
            ],
          ),

          // Pending Items (if any)
          if (stats.pendingListings > 0 || stats.pendingReservations > 0) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  const Icon(Icons.pending_actions, color: Colors.orange, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (stats.pendingListings > 0)
                          Text(
                            '${stats.pendingListings} عقارات قيد المراجعة',
                            style: AppTextStyles.font12SemiBold.copyWith(
                              color: Colors.orange[800],
                            ),
                          ),
                        if (stats.pendingReservations > 0)
                          Text(
                            '${stats.pendingReservations} حجوزات تحتاج موافقة',
                            style: AppTextStyles.font12SemiBold.copyWith(
                              color: Colors.orange[800],
                            ),
                          ),
                      ],
                    ),
                  ),
                  Icon(Icons.arrow_forward_ios, color: Colors.orange[800], size: 16),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: AppTextStyles.font16Bold.copyWith(color: color),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: AppTextStyles.font12Regular.copyWith(
              color: Colors.grey[700],
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildMiniStat({
    required String label,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
      child: Column(
        children: [
          Text(
            value,
            style: AppTextStyles.font14Bold.copyWith(
              color: Colors.black,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 2),
          Text(
            label,
            style: AppTextStyles.font10Regular.copyWith(
              color: Colors.black.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  String _formatNumber(int number) {
    if (number >= 1000000) {
      return '${(number / 1000000).toStringAsFixed(1)}م';
    } else if (number >= 1000) {
      return '${(number / 1000).toStringAsFixed(1)}ك';
    } else {
      return number.toString();
    }
  }
}
