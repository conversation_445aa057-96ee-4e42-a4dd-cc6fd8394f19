import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/feature/host/data/models/my_listing_model.dart';
import 'package:gather_point/feature/host/presentation/cubit/property_creation_cubit.dart';
import 'package:gather_point/feature/host/utils/property_edit_validator.dart';

class EditPropertyForm extends StatefulWidget {
  final MyListingModel propertyData;

  const EditPropertyForm({
    super.key,
    required this.propertyData,
  });

  @override
  State<EditPropertyForm> createState() => _EditPropertyFormState();
}

class _EditPropertyFormState extends State<EditPropertyForm> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _contentController = TextEditingController();
  final _priceController = TextEditingController();
  final _addressController = TextEditingController();

  // Image management
  List<String> _existingImages = [];
  List<String> _imagesToDelete = [];
  List<String> _newImages = [];
  bool _hasImageChanges = false;

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    _titleController.text = widget.propertyData.title;
    _contentController.text = widget.propertyData.content;
    _priceController.text = widget.propertyData.price.toString();
    _addressController.text = widget.propertyData.address ?? '';

    // Initialize images
    _existingImages = List.from(widget.propertyData.galleryImages);
  }

  @override
  void dispose() {
    _titleController.dispose();
    _contentController.dispose();
    _priceController.dispose();
    _addressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return BlocListener<PropertyCreationCubit, PropertyCreationState>(
      listener: (context, state) {
        if (state is PropertyUpdateSuccess) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(s.propertyUpdatedSuccessfully),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.pop(context, true);
        } else if (state is PropertyCreationError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Colors.red,
            ),
          );
        }
      },
      child: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Property Images Section
              _buildImagesSection(s),

              const SizedBox(height: 24),

              // Basic Information
              _buildBasicInfoSection(s),

              const SizedBox(height: 24),

              // Location Section
              _buildLocationSection(s),

              const SizedBox(height: 24),

              // Pricing Section
              _buildPricingSection(s),

              const SizedBox(height: 32),

              // Action Buttons
              _buildActionButtons(s),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildImagesSection(S s) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          s.propertyImages,
          style: AppTextStyles.font16SemiBold,
        ),
        const SizedBox(height: 12),
        SizedBox(
          height: 120,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: _getAllImages().length + 1,
            itemBuilder: (context, index) {
              final allImages = _getAllImages();
              if (index == allImages.length) {
                return _buildAddImageButton();
              }
              return _buildImageItem(allImages[index], index);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildImageItem(String imageUrl, int index) {
    final isExisting = _existingImages.contains(imageUrl);
    final isMarkedForDeletion = _imagesToDelete.contains(imageUrl);

    return Container(
      width: 120,
      margin: const EdgeInsets.only(right: 12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        image: DecorationImage(
          image: NetworkImage(imageUrl),
          fit: BoxFit.cover,
          colorFilter: isMarkedForDeletion
              ? ColorFilter.mode(Colors.red.withValues(alpha: 0.5), BlendMode.color)
              : null,
        ),
      ),
      child: Stack(
        children: [
          // Deletion overlay
          if (isMarkedForDeletion)
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: Colors.red.withValues(alpha: 0.3),
              ),
              child: const Center(
                child: Icon(
                  Icons.delete,
                  color: Colors.white,
                  size: 32,
                ),
              ),
            ),

          // Remove button
          Positioned(
            top: 8,
            right: 8,
            child: GestureDetector(
              onTap: () => _removeImage(imageUrl, index),
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: isMarkedForDeletion ? Colors.green : Colors.red,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  isMarkedForDeletion ? Icons.undo : Icons.close,
                  color: Colors.white,
                  size: 16,
                ),
              ),
            ),
          ),

          // New image indicator
          if (!isExisting)
            Positioned(
              bottom: 8,
              left: 8,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.green,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Text(
                  'جديد',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// Get all images (existing + new - deleted)
  List<String> _getAllImages() {
    final allImages = <String>[];

    // Add existing images that are not marked for deletion
    for (final image in _existingImages) {
      if (!_imagesToDelete.contains(image)) {
        allImages.add(image);
      }
    }

    // Add new images
    allImages.addAll(_newImages);

    return allImages;
  }

  /// Remove or restore image
  void _removeImage(String imageUrl, int index) {
    setState(() {
      if (_existingImages.contains(imageUrl)) {
        // Toggle deletion status for existing images
        if (_imagesToDelete.contains(imageUrl)) {
          _imagesToDelete.remove(imageUrl);
        } else {
          _imagesToDelete.add(imageUrl);
        }
      } else {
        // Permanently remove new images
        _newImages.remove(imageUrl);
      }
      _hasImageChanges = true;
    });
  }

  Widget _buildAddImageButton() {
    return GestureDetector(
      onTap: _addNewImage,
      child: Container(
        width: 120,
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.add_photo_alternate, size: 32, color: Colors.grey),
            SizedBox(height: 8),
            Text('إضافة صورة', style: TextStyle(color: Colors.grey)),
          ],
        ),
      ),
    );
  }

  /// Add new image
  void _addNewImage() {
    // In a real implementation, you would use image_picker
    // For now, we'll simulate adding a new image
    setState(() {
      _newImages.add('https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800');
      _hasImageChanges = true;
    });

    // Show success message
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم إضافة الصورة بنجاح'),
        backgroundColor: Colors.green,
      ),
    );
  }

  Widget _buildBasicInfoSection(S s) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          s.basicInformation,
          style: AppTextStyles.font16SemiBold,
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _titleController,
          decoration: InputDecoration(
            labelText: s.propertyTitle,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return s.pleaseEnterTitle;
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _contentController,
          maxLines: 4,
          decoration: InputDecoration(
            labelText: s.propertyDescription,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return s.pleaseEnterDescription;
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildLocationSection(S s) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          s.location,
          style: AppTextStyles.font16SemiBold,
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _addressController,
          decoration: InputDecoration(
            labelText: s.address,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            suffixIcon: IconButton(
              onPressed: () {
                // Handle map picker
              },
              icon: const Icon(Icons.location_on),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPricingSection(S s) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          s.pricing,
          style: AppTextStyles.font16SemiBold,
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _priceController,
          keyboardType: TextInputType.number,
          decoration: InputDecoration(
            labelText: s.pricePerNight,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            suffixText: 'ر.س',
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return s.pleaseEnterPrice;
            }
            if (double.tryParse(value) == null) {
              return s.pleaseEnterValidPrice;
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildActionButtons(S s) {
    return BlocBuilder<PropertyCreationCubit, PropertyCreationState>(
      builder: (context, state) {
        final isLoading = state is PropertyCreationLoading;

        return Column(
          children: [
            // Save as Draft
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: isLoading ? null : _saveAsDraft,
                icon: const Icon(Icons.save),
                label: Text(s.saveAsDraft),
              ),
            ),
            const SizedBox(height: 12),
            // Update Property
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: isLoading ? null : _updateProperty,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFFEC53A),
                  foregroundColor: Colors.black,
                ),
                icon: isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.update),
                label: Text(s.updateProperty),
              ),
            ),
          ],
        );
      },
    );
  }

  void _saveAsDraft() {
    if (_formKey.currentState!.validate()) {
      final propertyData = _getFormData();
      context.read<PropertyCreationCubit>().savePropertyAsDraft(
        widget.propertyData.id,
        propertyData,
      );
    }
  }

  void _updateProperty() {
    if (_formKey.currentState!.validate()) {
      final propertyData = _getFormData();

      // Validate changes
      final validation = PropertyEditValidator.validatePropertyEdit(
        widget.propertyData,
        propertyData,
      );

      if (!validation.isValid) {
        _showValidationErrors(validation.errors);
        return;
      }

      if (!validation.hasChanges) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('لم يتم إجراء أي تغييرات'),
            backgroundColor: Colors.orange,
          ),
        );
        return;
      }

      // Show warnings if any
      if (validation.warnings.isNotEmpty) {
        _showValidationWarnings(validation.warnings, () {
          _performUpdate(propertyData);
        });
      } else {
        _performUpdate(propertyData);
      }
    }
  }

  void _performUpdate(Map<String, dynamic> propertyData) {
    context.read<PropertyCreationCubit>().updateProperty(
      propertyId: widget.propertyData.id,
      propertyData: propertyData,
    );
  }

  void _showValidationErrors(List<String> errors) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('أخطاء في البيانات'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: errors.map((error) => Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Icon(Icons.error, color: Colors.red, size: 16),
                const SizedBox(width: 8),
                Expanded(child: Text(error)),
              ],
            ),
          )).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  void _showValidationWarnings(List<String> warnings, VoidCallback onProceed) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تحذيرات'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('تم اكتشاف التحذيرات التالية:'),
            const SizedBox(height: 12),
            ...warnings.map((warning) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Icon(Icons.warning, color: Colors.orange, size: 16),
                  const SizedBox(width: 8),
                  Expanded(child: Text(warning)),
                ],
              ),
            )),
            const SizedBox(height: 12),
            const Text('هل تريد المتابعة؟'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              onProceed();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFFEC53A),
              foregroundColor: Colors.black,
            ),
            child: const Text('متابعة'),
          ),
        ],
      ),
    );
  }

  Map<String, dynamic> _getFormData() {
    final data = {
      'title': _titleController.text,
      'content': _contentController.text,
      'price': double.tryParse(_priceController.text) ?? 0.0,
      'address': _addressController.text,
      'lat': widget.propertyData.lat,
      'lon': widget.propertyData.lon,
      'service_category_id': widget.propertyData.category.id,
      'no_guests': widget.propertyData.noGuests,
      'beds': widget.propertyData.beds,
      'baths': widget.propertyData.baths,
    };

    // Add image changes if any
    if (_hasImageChanges) {
      data['images'] = _getAllImages();
      data['images_to_delete'] = _imagesToDelete;
      data['new_images'] = _newImages;
    }

    return data;
  }
}
