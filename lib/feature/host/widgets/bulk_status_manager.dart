import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/feature/host/data/models/my_listing_model.dart';
import 'package:gather_point/feature/host/presentation/cubit/my_listings_cubit.dart';

class BulkStatusManager extends StatefulWidget {
  final List<MyListingModel> selectedListings;
  final VoidCallback onComplete;

  const BulkStatusManager({
    super.key,
    required this.selectedListings,
    required this.onComplete,
  });

  @override
  State<BulkStatusManager> createState() => _BulkStatusManagerState();
}

class _BulkStatusManagerState extends State<BulkStatusManager> {
  String? _selectedAction;
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return Container(
      padding: const EdgeInsets.all(24),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Handle
          Center(
            child: Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Header
          Row(
            children: [
              Icon(
                Icons.checklist,
                color: const Color(0xFFFEC53A),
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  s.bulkActions,
                  style: AppTextStyles.font18Bold,
                ),
              ),
              IconButton(
                onPressed: () => Navigator.pop(context),
                icon: const Icon(Icons.close),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Selection Summary
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFFFEC53A).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: const Color(0xFFFEC53A),
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    '${widget.selectedListings.length} ${s.listingsSelected}',
                    style: AppTextStyles.font14SemiBold,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Status Breakdown
          Text(
            s.currentStatusBreakdown,
            style: AppTextStyles.font16SemiBold,
          ),
          const SizedBox(height: 12),
          _buildStatusBreakdown(),

          const SizedBox(height: 24),

          // Available Actions
          Text(
            s.selectAction,
            style: AppTextStyles.font16SemiBold,
          ),
          const SizedBox(height: 12),
          ..._buildActionOptions(),

          const SizedBox(height: 24),

          // Action Buttons
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: _isLoading ? null : () => Navigator.pop(context),
                  child: Text(s.cancel),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton(
                  onPressed: _isLoading || _selectedAction == null 
                      ? null 
                      : _executeAction,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFFEC53A),
                    foregroundColor: Colors.black,
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : Text(s.applyAction),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatusBreakdown() {
    final statusCounts = <String, int>{};
    
    for (final listing in widget.selectedListings) {
      statusCounts[listing.status] = (statusCounts[listing.status] ?? 0) + 1;
    }

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: statusCounts.entries.map((entry) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: _getStatusColor(entry.key).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: _getStatusColor(entry.key).withValues(alpha: 0.3),
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color: _getStatusColor(entry.key),
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 6),
              Text(
                '${_getStatusDisplayName(entry.key)}: ${entry.value}',
                style: AppTextStyles.font12SemiBold,
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  List<Widget> _buildActionOptions() {
    final availableActions = _getAvailableActions();
    
    return availableActions.map((action) {
      return Padding(
        padding: const EdgeInsets.only(bottom: 8),
        child: RadioListTile<String>(
          value: action,
          groupValue: _selectedAction,
          onChanged: (value) {
            setState(() {
              _selectedAction = value;
            });
          },
          title: Row(
            children: [
              Icon(
                _getActionIcon(action),
                color: _getActionColor(action),
                size: 20,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _getActionDisplayName(action),
                      style: AppTextStyles.font14SemiBold,
                    ),
                    Text(
                      _getActionDescription(action),
                      style: AppTextStyles.font12Regular.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          activeColor: const Color(0xFFFEC53A),
        ),
      );
    }).toList();
  }

  List<String> _getAvailableActions() {
    final actions = <String>[];
    
    // Check what actions are possible based on selected listings
    final hasActive = widget.selectedListings.any((l) => l.status == 'active');
    final hasInactive = widget.selectedListings.any((l) => l.status == 'inactive');
    final hasDraft = widget.selectedListings.any((l) => l.status == 'draft');
    
    if (hasInactive || hasDraft) {
      actions.add('activate');
    }
    
    if (hasActive) {
      actions.add('deactivate');
    }
    
    if (hasActive || hasInactive) {
      actions.add('convert_to_draft');
    }
    
    actions.add('delete');
    
    return actions;
  }

  String _getActionDisplayName(String action) {
    final s = S.of(context);
    
    switch (action) {
      case 'activate':
        return s.activateAll;
      case 'deactivate':
        return s.deactivateAll;
      case 'convert_to_draft':
        return s.convertToDraft;
      case 'delete':
        return s.deleteAll;
      default:
        return action;
    }
  }

  String _getActionDescription(String action) {
    final s = S.of(context);
    
    switch (action) {
      case 'activate':
        return s.activateAllDescription;
      case 'deactivate':
        return s.deactivateAllDescription;
      case 'convert_to_draft':
        return s.convertToDraftDescription;
      case 'delete':
        return s.deleteAllDescription;
      default:
        return '';
    }
  }

  IconData _getActionIcon(String action) {
    switch (action) {
      case 'activate':
        return Icons.visibility;
      case 'deactivate':
        return Icons.visibility_off;
      case 'convert_to_draft':
        return Icons.edit;
      case 'delete':
        return Icons.delete;
      default:
        return Icons.help;
    }
  }

  Color _getActionColor(String action) {
    switch (action) {
      case 'activate':
        return Colors.green;
      case 'deactivate':
        return Colors.orange;
      case 'convert_to_draft':
        return Colors.blue;
      case 'delete':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'active':
        return Colors.green;
      case 'inactive':
        return Colors.orange;
      case 'draft':
        return Colors.grey;
      case 'pending':
        return Colors.blue;
      case 'suspended':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _getStatusDisplayName(String status) {
    switch (status) {
      case 'active':
        return 'نشط';
      case 'inactive':
        return 'غير نشط';
      case 'draft':
        return 'مسودة';
      case 'pending':
        return 'قيد المراجعة';
      case 'suspended':
        return 'معلق';
      default:
        return status;
    }
  }

  Future<void> _executeAction() async {
    if (_selectedAction == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final listingIds = widget.selectedListings.map((l) => l.id).toList();
      
      switch (_selectedAction) {
        case 'activate':
          await context.read<MyListingsCubit>().bulkUpdateStatus(listingIds, 'active');
          break;
        case 'deactivate':
          await context.read<MyListingsCubit>().bulkUpdateStatus(listingIds, 'inactive');
          break;
        case 'convert_to_draft':
          await context.read<MyListingsCubit>().bulkUpdateStatus(listingIds, 'draft');
          break;
        case 'delete':
          await context.read<MyListingsCubit>().bulkDeleteListings(listingIds);
          break;
      }
      
      if (mounted) {
        Navigator.pop(context);
        widget.onComplete();
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(S.of(context).bulkActionCompleted),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(S.of(context).bulkActionError),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
