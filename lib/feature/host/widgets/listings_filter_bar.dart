import 'package:flutter/material.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/feature/host/data/models/listing_filter_model.dart';

class ListingsFilterBar extends StatefulWidget {
  final ListingFilterModel currentFilter;
  final Function(ListingFilterModel) onFilterChanged;
  final Function(String) onSearchChanged;
  final TextEditingController searchController;

  const ListingsFilterBar({
    super.key,
    required this.currentFilter,
    required this.onFilterChanged,
    required this.onSearchChanged,
    required this.searchController,
  });

  @override
  State<ListingsFilterBar> createState() => _ListingsFilterBarState();
}

class _ListingsFilterBarState extends State<ListingsFilterBar> {
  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Colors.grey.withValues(alpha: 0.2)),
        ),
      ),
      child: Column(
        children: [
          // Search Bar
          Container(
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(12),
            ),
            child: TextField(
              controller: widget.searchController,
              onChanged: widget.onSearchChanged,
              decoration: InputDecoration(
                hintText: s.searchListings,
                prefixIcon: const Icon(Icons.search, color: Colors.grey),
                suffixIcon: widget.searchController.text.isNotEmpty
                    ? IconButton(
                        onPressed: () {
                          widget.searchController.clear();
                          widget.onSearchChanged('');
                        },
                        icon: const Icon(Icons.clear, color: Colors.grey),
                      )
                    : null,
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
            ),
          ),

          const SizedBox(height: 12),

          // Filter Chips
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                // All Listings
                _buildFilterChip(
                  label: s.allListings,
                  isSelected: !widget.currentFilter.hasActiveFilters,
                  onTap: () => widget.onFilterChanged(ListingFilterModel.allListings),
                ),

                const SizedBox(width: 8),

                // Active Listings
                _buildFilterChip(
                  label: s.activeListings,
                  isSelected: widget.currentFilter.status == 'active',
                  onTap: () => widget.onFilterChanged(ListingFilterModel.activeListings),
                ),

                const SizedBox(width: 8),

                // Inactive Listings
                _buildFilterChip(
                  label: s.inactiveListings,
                  isSelected: widget.currentFilter.status == 'inactive',
                  onTap: () => widget.onFilterChanged(ListingFilterModel.inactiveListings),
                ),

                const SizedBox(width: 8),

                // Draft Listings
                _buildFilterChip(
                  label: s.drafts,
                  isSelected: widget.currentFilter.status == 'draft',
                  onTap: () => widget.onFilterChanged(ListingFilterModel.draftListings),
                ),

                const SizedBox(width: 8),

                // Pending Listings
                _buildFilterChip(
                  label: s.pendingReview,
                  isSelected: widget.currentFilter.status == 'pending',
                  onTap: () => widget.onFilterChanged(ListingFilterModel.pendingListings),
                ),

                const SizedBox(width: 8),

                // Top Performing
                _buildFilterChip(
                  label: s.topPerforming,
                  isSelected: widget.currentFilter == ListingFilterModel.topPerforming,
                  onTap: () => widget.onFilterChanged(ListingFilterModel.topPerforming),
                ),

                const SizedBox(width: 8),

                // Needs Attention
                _buildFilterChip(
                  label: s.needsAttention,
                  isSelected: widget.currentFilter == ListingFilterModel.needsAttention,
                  onTap: () => widget.onFilterChanged(ListingFilterModel.needsAttention),
                ),

                const SizedBox(width: 8),

                // Advanced Filters Button
                _buildAdvancedFiltersButton(),
              ],
            ),
          ),

          // Active Filters Indicator
          if (widget.currentFilter.hasActiveFilters) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(
                  Icons.filter_alt,
                  size: 16,
                  color: const Color(0xFFFEC53A),
                ),
                const SizedBox(width: 4),
                Text(
                  widget.currentFilter.displayName,
                  style: AppTextStyles.font12SemiBold.copyWith(
                    color: const Color(0xFFFEC53A),
                  ),
                ),
                if (widget.currentFilter.activeFilterCount > 1) ...[
                  const SizedBox(width: 4),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: const Color(0xFFFEC53A),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Text(
                      widget.currentFilter.activeFilterCount.toString(),
                      style: AppTextStyles.font10Regular.copyWith(
                        color: Colors.black,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
                const Spacer(),
                TextButton(
                  onPressed: () {
                    widget.searchController.clear();
                    widget.onFilterChanged(ListingFilterModel.allListings);
                  },
                  child: Text(
                    s.clearFilters,
                    style: AppTextStyles.font12Regular.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildFilterChip({
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFFFEC53A) : Colors.grey[200],
          borderRadius: BorderRadius.circular(20),
          border: isSelected
              ? Border.all(color: const Color(0xFFFEC53A))
              : Border.all(color: Colors.grey.withValues(alpha: 0.3)),
        ),
        child: Text(
          label,
          style: AppTextStyles.font12SemiBold.copyWith(
            color: isSelected ? Colors.black : Colors.grey[700],
          ),
        ),
      ),
    );
  }

  Widget _buildAdvancedFiltersButton() {
    return GestureDetector(
      onTap: _showAdvancedFiltersDialog,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.blue[50],
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.tune,
              size: 16,
              color: Colors.blue[700],
            ),
            const SizedBox(width: 4),
            Text(
              'فلاتر متقدمة',
              style: AppTextStyles.font12SemiBold.copyWith(
                color: Colors.blue[700],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showAdvancedFiltersDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        expand: false,
        builder: (context, scrollController) {
          return Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Handle
                Center(
                  child: Container(
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // Title
                Text(
                  'فلاتر متقدمة',
                  style: AppTextStyles.font18Bold,
                ),
                const SizedBox(height: 16),

                // Advanced filter options would go here
                Expanded(
                  child: SingleChildScrollView(
                    controller: scrollController,
                    child: Column(
                      children: [
                        // Sort Options
                        _buildSortSection(),
                        const SizedBox(height: 24),

                        // Price Range
                        _buildPriceRangeSection(),
                        const SizedBox(height: 24),

                        // Date Range
                        _buildDateRangeSection(),
                        const SizedBox(height: 24),

                        // Performance Filters
                        _buildPerformanceSection(),
                      ],
                    ),
                  ),
                ),

                // Apply Button
                const SizedBox(height: 16),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context);
                      // Apply advanced filters
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFFFEC53A),
                      foregroundColor: Colors.black,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: const Text('تطبيق الفلاتر'),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildSortSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'ترتيب حسب',
          style: AppTextStyles.font16SemiBold,
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          children: [
            _buildSortChip('تاريخ الإنشاء', 'created_at'),
            _buildSortChip('تاريخ التحديث', 'updated_at'),
            _buildSortChip('السعر', 'price'),
            _buildSortChip('الإيرادات', 'revenue'),
            _buildSortChip('التقييم', 'rating'),
          ],
        ),
      ],
    );
  }

  Widget _buildSortChip(String label, String value) {
    final isSelected = widget.currentFilter.sortBy == value;
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        if (selected) {
          widget.onFilterChanged(
            widget.currentFilter.copyWith(sortBy: value),
          );
        }
      },
    );
  }

  Widget _buildPriceRangeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'نطاق السعر',
          style: AppTextStyles.font16SemiBold,
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: TextField(
                decoration: const InputDecoration(
                  labelText: 'الحد الأدنى',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: TextField(
                decoration: const InputDecoration(
                  labelText: 'الحد الأعلى',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDateRangeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'تاريخ الإنشاء',
          style: AppTextStyles.font16SemiBold,
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () {
                  // Show date picker
                },
                icon: const Icon(Icons.calendar_today),
                label: const Text('من تاريخ'),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () {
                  // Show date picker
                },
                icon: const Icon(Icons.calendar_today),
                label: const Text('إلى تاريخ'),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPerformanceSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الأداء',
          style: AppTextStyles.font16SemiBold,
        ),
        const SizedBox(height: 8),
        CheckboxListTile(
          title: const Text('يحتوي على حجوزات'),
          value: widget.currentFilter.hasBookings,
          onChanged: (value) {
            widget.onFilterChanged(
              widget.currentFilter.copyWith(hasBookings: value),
            );
          },
        ),
        CheckboxListTile(
          title: const Text('يحتوي على تقييمات'),
          value: widget.currentFilter.hasReviews,
          onChanged: (value) {
            widget.onFilterChanged(
              widget.currentFilter.copyWith(hasReviews: value),
            );
          },
        ),
      ],
    );
  }
}
