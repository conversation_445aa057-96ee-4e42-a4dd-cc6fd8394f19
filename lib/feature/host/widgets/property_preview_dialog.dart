import 'dart:io';
import 'package:flutter/material.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/feature/home/<USER>/Data%20Sources/Models/ServiceCategory.dart';
import 'package:gather_point/feature/host/data/models/property_type_model.dart';
import 'package:gather_point/feature/cancellation_policies/data/models/cancellation_policy_model.dart';

class PropertyPreviewDialog extends StatelessWidget {
  final String title;
  final String description;
  final double price;
  final double? weekendPrice;
  final double? weeklyPrice;
  final double? monthlyPrice;
  final int guests;
  final int bedrooms;
  final int bathrooms;
  final String? bookingRules;
  final String? cancellationRules;
  final ServiceCategory category;
  final PropertyTypeModel propertyType;
  final CancellationPolicyModel cancellationPolicy;
  final List<String> facilityNames;
  final File? mainImage;
  final File? video;
  final List<File> galleryImages;
  final double? latitude;
  final double? longitude;

  const PropertyPreviewDialog({
    super.key,
    required this.title,
    required this.description,
    required this.price,
    this.weekendPrice,
    this.weeklyPrice,
    this.monthlyPrice,
    required this.guests,
    required this.bedrooms,
    required this.bathrooms,
    this.bookingRules,
    this.cancellationRules,
    required this.category,
    required this.propertyType,
    required this.cancellationPolicy,
    required this.facilityNames,
    this.mainImage,
    this.video,
    required this.galleryImages,
    this.latitude,
    this.longitude,
  });

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return Dialog(
      insetPadding: const EdgeInsets.all(16),
      child: Container(
        width: double.infinity,
        height: MediaQuery.of(context).size.height * 0.9,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          color: Colors.white,
        ),
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                color: Color(0xFFFEC53A),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      s.propertyPreview,
                      style: AppTextStyles.font18Bold.copyWith(
                        color: Colors.black,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close, color: Colors.black),
                  ),
                ],
              ),
            ),

            // Content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Main Image
                    if (mainImage != null)
                      Container(
                        height: 200,
                        width: double.infinity,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          image: DecorationImage(
                            image: FileImage(mainImage!),
                            fit: BoxFit.cover,
                          ),
                        ),
                      )
                    else
                      Container(
                        height: 200,
                        width: double.infinity,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          color: Colors.grey[200],
                        ),
                        child: const Center(
                          child: Icon(
                            Icons.image,
                            size: 64,
                            color: Colors.grey,
                          ),
                        ),
                      ),

                    const SizedBox(height: 16),

                    // Title and Category
                    Text(
                      title,
                      style: AppTextStyles.font20Bold,
                    ),
                    const SizedBox(height: 4),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: const Color(0xFFFEC53A).withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        category.title,
                        style: AppTextStyles.font12SemiBold.copyWith(
                          color: const Color(0xFFFEC53A),
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Price Section
                    _buildPriceSection(s),

                    const SizedBox(height: 16),

                    // Property Details
                    _buildPropertyDetails(s),

                    const SizedBox(height: 16),

                    // Description
                    Text(
                      s.description,
                      style: AppTextStyles.font16SemiBold,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      description,
                      style: AppTextStyles.font14Regular.copyWith(
                        color: Colors.grey[700],
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Facilities
                    if (facilityNames.isNotEmpty) ...[
                      Text(
                        s.facilities,
                        style: AppTextStyles.font16SemiBold,
                      ),
                      const SizedBox(height: 8),
                      Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: facilityNames.map((facility) {
                          return Container(
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                            decoration: BoxDecoration(
                              color: Colors.blue.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(16),
                              border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
                            ),
                            child: Text(
                              facility,
                              style: AppTextStyles.font12Regular.copyWith(
                                color: Colors.blue[700],
                              ),
                            ),
                          );
                        }).toList(),
                      ),
                      const SizedBox(height: 16),
                    ],

                    // Gallery
                    if (galleryImages.isNotEmpty) ...[
                      Text(
                        s.gallery,
                        style: AppTextStyles.font16SemiBold,
                      ),
                      const SizedBox(height: 8),
                      SizedBox(
                        height: 100,
                        child: ListView.builder(
                          scrollDirection: Axis.horizontal,
                          itemCount: galleryImages.length,
                          itemBuilder: (context, index) {
                            return Container(
                              margin: const EdgeInsets.only(right: 8),
                              width: 100,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(8),
                                image: DecorationImage(
                                  image: FileImage(galleryImages[index]),
                                  fit: BoxFit.cover,
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                      const SizedBox(height: 16),
                    ],

                    // Policies
                    _buildPoliciesSection(s),

                    const SizedBox(height: 16),

                    // Location
                    if (latitude != null && longitude != null) ...[
                      Text(
                        s.location,
                        style: AppTextStyles.font16SemiBold,
                      ),
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.location_pin, color: Color(0xFFFEC53A)),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                'خط العرض: ${latitude!.toStringAsFixed(6)}\nخط الطول: ${longitude!.toStringAsFixed(6)}',
                                style: AppTextStyles.font12Regular,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),

            // Footer
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(16),
                  bottomRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.of(context).pop(false),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        side: const BorderSide(color: Colors.grey),
                      ),
                      child: Text(s.editProperty),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () => Navigator.of(context).pop(true),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFFFEC53A),
                        foregroundColor: Colors.black,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                      child: Text(s.publishProperty),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPriceSection(S s) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFFEC53A).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFFEC53A).withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                '${price.toStringAsFixed(0)} ${s.sar}',
                style: AppTextStyles.font20Bold.copyWith(
                  color: const Color(0xFFFEC53A),
                ),
              ),
              Text(
                ' / ${s.night}',
                style: AppTextStyles.font14Regular.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          if (weekendPrice != null || weeklyPrice != null || monthlyPrice != null) ...[
            const SizedBox(height: 8),
            if (weekendPrice != null)
              Text(
                '${s.weekendPrice}: ${weekendPrice!.toStringAsFixed(0)} ${s.sar}',
                style: AppTextStyles.font12Regular,
              ),
            if (weeklyPrice != null)
              Text(
                '${s.weeklyPrice}: ${weeklyPrice!.toStringAsFixed(0)} ${s.sar}',
                style: AppTextStyles.font12Regular,
              ),
            if (monthlyPrice != null)
              Text(
                '${s.monthlyPrice}: ${monthlyPrice!.toStringAsFixed(0)} ${s.sar}',
                style: AppTextStyles.font12Regular,
              ),
          ],
        ],
      ),
    );
  }

  Widget _buildPropertyDetails(S s) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            s.propertyDetails,
            style: AppTextStyles.font16SemiBold,
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildDetailItem(
                  icon: Icons.people,
                  label: s.guests,
                  value: guests.toString(),
                ),
              ),
              Expanded(
                child: _buildDetailItem(
                  icon: Icons.bed,
                  label: s.bedrooms,
                  value: bedrooms.toString(),
                ),
              ),
              Expanded(
                child: _buildDetailItem(
                  icon: Icons.bathroom,
                  label: s.bathrooms,
                  value: bathrooms.toString(),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildDetailItem(
                  icon: Icons.category,
                  label: s.propertyType,
                  value: propertyType.title,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDetailItem({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Column(
      children: [
        Icon(icon, color: const Color(0xFFFEC53A), size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: AppTextStyles.font14SemiBold,
        ),
        Text(
          label,
          style: AppTextStyles.font12Regular.copyWith(
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildPoliciesSection(S s) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            s.policies,
            style: AppTextStyles.font16SemiBold,
          ),
          const SizedBox(height: 12),

          // Cancellation Policy
          Row(
            children: [
              const Icon(Icons.policy, color: Colors.blue, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      s.cancellationPolicy,
                      style: AppTextStyles.font14SemiBold,
                    ),
                    Text(
                      cancellationPolicy.name,
                      style: AppTextStyles.font12Regular.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          if (bookingRules != null && bookingRules!.isNotEmpty) ...[
            const SizedBox(height: 12),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Icon(Icons.rule, color: Colors.blue, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        s.bookingRules,
                        style: AppTextStyles.font14SemiBold,
                      ),
                      Text(
                        bookingRules!,
                        style: AppTextStyles.font12Regular.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],

          if (cancellationRules != null && cancellationRules!.isNotEmpty) ...[
            const SizedBox(height: 12),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Icon(Icons.cancel, color: Colors.blue, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        s.cancellationRules,
                        style: AppTextStyles.font14SemiBold,
                      ),
                      Text(
                        cancellationRules!,
                        style: AppTextStyles.font12Regular.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }
}
