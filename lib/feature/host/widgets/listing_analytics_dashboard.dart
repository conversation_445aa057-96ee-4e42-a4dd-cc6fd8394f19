import 'package:flutter/material.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/feature/host/data/models/listing_analytics_model.dart';
import 'package:gather_point/feature/host/widgets/analytics_metric_card.dart';
import 'package:gather_point/feature/host/widgets/analytics_chart_widget.dart';
import 'package:gather_point/feature/host/widgets/performance_insights_widget.dart';

class ListingAnalyticsDashboard extends StatefulWidget {
  final ListingAnalyticsModel analytics;
  final VoidCallback? onPeriodChanged;
  final VoidCallback? onExport;

  const ListingAnalyticsDashboard({
    super.key,
    required this.analytics,
    this.onPeriodChanged,
    this.onExport,
  });

  @override
  State<ListingAnalyticsDashboard> createState() => _ListingAnalyticsDashboardState();
}

class _ListingAnalyticsDashboardState extends State<ListingAnalyticsDashboard> {
  String _selectedPeriod = '30d';
  int _selectedTabIndex = 0;

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header with period selector
        _buildHeader(s),

        const SizedBox(height: 16),

        // Performance Grade
        _buildPerformanceGrade(s),

        const SizedBox(height: 24),

        // Key Metrics
        _buildKeyMetrics(s),

        const SizedBox(height: 24),

        // Tab Navigation
        _buildTabNavigation(s),

        const SizedBox(height: 16),

        // Tab Content
        Expanded(
          child: _buildTabContent(s),
        ),
      ],
    );
  }

  Widget _buildHeader(S s) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                s.analyticsOverview,
                style: AppTextStyles.font20Bold,
              ),
              const SizedBox(height: 4),
              Text(
                '${_formatDate(widget.analytics.startDate)} - ${_formatDate(widget.analytics.endDate)}',
                style: AppTextStyles.font12Regular.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
        
        // Period Selector
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
            borderRadius: BorderRadius.circular(8),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: _selectedPeriod,
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedPeriod = value;
                  });
                  widget.onPeriodChanged?.call();
                }
              },
              items: [
                DropdownMenuItem(value: '7d', child: Text(s.last7Days)),
                DropdownMenuItem(value: '30d', child: Text(s.last30Days)),
                DropdownMenuItem(value: '90d', child: Text(s.last90Days)),
                DropdownMenuItem(value: '1y', child: Text(s.lastYear)),
              ],
            ),
          ),
        ),

        const SizedBox(width: 8),

        // Export Button
        IconButton(
          onPressed: widget.onExport,
          icon: const Icon(Icons.download),
          tooltip: s.exportData,
        ),
      ],
    );
  }

  Widget _buildPerformanceGrade(S s) {
    final grade = widget.analytics.performanceGrade;
    Color gradeColor;
    
    switch (grade) {
      case 'ممتاز':
        gradeColor = Colors.green;
        break;
      case 'جيد جداً':
        gradeColor = Colors.blue;
        break;
      case 'جيد':
        gradeColor = Colors.orange;
        break;
      default:
        gradeColor = Colors.red;
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            gradeColor.withValues(alpha: 0.1),
            gradeColor.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: gradeColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: gradeColor,
              shape: BoxShape.circle,
            ),
            child: Icon(
              widget.analytics.hasGoodPerformance ? Icons.trending_up : Icons.trending_down,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  s.performanceGrade,
                  style: AppTextStyles.font14Regular.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
                Text(
                  grade,
                  style: AppTextStyles.font20Bold.copyWith(
                    color: gradeColor,
                  ),
                ),
              ],
            ),
          ),
          Text(
            widget.analytics.hasGoodPerformance ? '📈' : '📉',
            style: const TextStyle(fontSize: 24),
          ),
        ],
      ),
    );
  }

  Widget _buildKeyMetrics(S s) {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 1.5,
      children: [
        AnalyticsMetricCard(
          title: s.totalViews,
          value: widget.analytics.totalViews.toString(),
          change: widget.analytics.previousPeriod?.viewsChange,
          icon: Icons.visibility,
          color: Colors.blue,
        ),
        AnalyticsMetricCard(
          title: s.totalBookings,
          value: widget.analytics.totalBookings.toString(),
          change: widget.analytics.previousPeriod?.bookingsChange,
          icon: Icons.calendar_today,
          color: Colors.green,
        ),
        AnalyticsMetricCard(
          title: s.totalRevenue,
          value: widget.analytics.totalRevenueDisplay,
          change: widget.analytics.previousPeriod?.revenueChange,
          icon: Icons.monetization_on,
          color: const Color(0xFFFEC53A),
        ),
        AnalyticsMetricCard(
          title: s.conversionRate,
          value: widget.analytics.conversionRateDisplay,
          change: widget.analytics.previousPeriod?.conversionRateChange,
          icon: Icons.trending_up,
          color: Colors.purple,
        ),
      ],
    );
  }

  Widget _buildTabNavigation(S s) {
    final tabs = [
      s.overview,
      s.charts,
      s.insights,
      s.details,
    ];

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: tabs.asMap().entries.map((entry) {
          final index = entry.key;
          final title = entry.value;
          final isSelected = index == _selectedTabIndex;

          return GestureDetector(
            onTap: () => setState(() => _selectedTabIndex = index),
            child: Container(
              margin: const EdgeInsets.only(right: 16),
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
              decoration: BoxDecoration(
                color: isSelected ? const Color(0xFFFEC53A) : Colors.transparent,
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: isSelected 
                      ? const Color(0xFFFEC53A) 
                      : Colors.grey.withValues(alpha: 0.3),
                ),
              ),
              child: Text(
                title,
                style: AppTextStyles.font14SemiBold.copyWith(
                  color: isSelected ? Colors.black : Colors.grey[700],
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildTabContent(S s) {
    switch (_selectedTabIndex) {
      case 0:
        return _buildOverviewTab(s);
      case 1:
        return _buildChartsTab(s);
      case 2:
        return _buildInsightsTab(s);
      case 3:
        return _buildDetailsTab(s);
      default:
        return _buildOverviewTab(s);
    }
  }

  Widget _buildOverviewTab(S s) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Secondary Metrics
          Row(
            children: [
              Expanded(
                child: _buildSecondaryMetric(
                  s.occupancyRate,
                  widget.analytics.occupancyRateDisplay,
                  Icons.hotel,
                  Colors.teal,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildSecondaryMetric(
                  s.averageRating,
                  widget.analytics.averageRatingDisplay,
                  Icons.star,
                  Colors.amber,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: _buildSecondaryMetric(
                  s.responseTime,
                  widget.analytics.responseTimeDisplay,
                  Icons.schedule,
                  Colors.indigo,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildSecondaryMetric(
                  s.responseRate,
                  widget.analytics.responseRateDisplay,
                  Icons.reply,
                  Colors.cyan,
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Quick Chart
          if (widget.analytics.dailyViews.isNotEmpty)
            AnalyticsChartWidget(
              title: s.viewsTrend,
              data: widget.analytics.dailyViews,
              color: Colors.blue,
              height: 200,
            ),
        ],
      ),
    );
  }

  Widget _buildChartsTab(S s) {
    return SingleChildScrollView(
      child: Column(
        children: [
          if (widget.analytics.dailyViews.isNotEmpty)
            AnalyticsChartWidget(
              title: s.dailyViews,
              data: widget.analytics.dailyViews,
              color: Colors.blue,
              height: 250,
            ),

          const SizedBox(height: 24),

          if (widget.analytics.dailyBookings.isNotEmpty)
            AnalyticsChartWidget(
              title: s.dailyBookings,
              data: widget.analytics.dailyBookings,
              color: Colors.green,
              height: 250,
            ),

          const SizedBox(height: 24),

          if (widget.analytics.dailyRevenue.isNotEmpty)
            AnalyticsChartWidget(
              title: s.dailyRevenue,
              data: widget.analytics.dailyRevenue,
              color: const Color(0xFFFEC53A),
              height: 250,
            ),
        ],
      ),
    );
  }

  Widget _buildInsightsTab(S s) {
    return PerformanceInsightsWidget(
      insights: widget.analytics.insights,
      recommendations: widget.analytics.recommendations,
    );
  }

  Widget _buildDetailsTab(S s) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildDetailSection(s.bookingMetrics, [
            _buildDetailItem(s.totalBookings, widget.analytics.totalBookings.toString()),
            _buildDetailItem(s.confirmedBookings, widget.analytics.confirmedBookings.toString()),
            _buildDetailItem(s.cancelledBookings, widget.analytics.cancelledBookings.toString()),
            _buildDetailItem(s.cancellationRate, widget.analytics.cancellationRateDisplay),
          ]),

          const SizedBox(height: 24),

          _buildDetailSection(s.revenueMetrics, [
            _buildDetailItem(s.totalRevenue, widget.analytics.totalRevenueDisplay),
            _buildDetailItem(s.netRevenue, '${widget.analytics.netRevenue.toStringAsFixed(0)} ر.س'),
            _buildDetailItem(s.averageDailyRate, '${widget.analytics.averageDailyRate.toStringAsFixed(0)} ر.س'),
          ]),

          const SizedBox(height: 24),

          _buildDetailSection(s.engagementMetrics, [
            _buildDetailItem(s.totalViews, widget.analytics.totalViews.toString()),
            _buildDetailItem(s.uniqueViews, widget.analytics.uniqueViews.toString()),
            _buildDetailItem(s.favoriteCount, widget.analytics.favoriteCount.toString()),
            _buildDetailItem(s.shareCount, widget.analytics.shareCount.toString()),
          ]),
        ],
      ),
    );
  }

  Widget _buildSecondaryMetric(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.2)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: AppTextStyles.font16Bold.copyWith(color: color),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: AppTextStyles.font12Regular.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildDetailSection(String title, List<Widget> items) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: AppTextStyles.font16SemiBold,
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey.withValues(alpha: 0.2)),
          ),
          child: Column(
            children: items,
          ),
        ),
      ],
    );
  }

  Widget _buildDetailItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: AppTextStyles.font14Regular,
          ),
          Text(
            value,
            style: AppTextStyles.font14SemiBold.copyWith(
              color: const Color(0xFFFEC53A),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
