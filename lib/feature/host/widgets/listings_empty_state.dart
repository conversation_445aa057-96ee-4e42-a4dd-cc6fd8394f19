import 'package:flutter/material.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';

class ListingsEmptyState extends StatelessWidget {
  final VoidCallback onCreateListing;

  const ListingsEmptyState({
    super.key,
    required this.onCreateListing,
  });

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Illustration
            Container(
              width: 200,
              height: 200,
              decoration: BoxDecoration(
                color: const Color(0xFFFEC53A).withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: const Center(
                child: Icon(
                  Icons.home_work_outlined,
                  size: 80,
                  color: Color(0xFFFEC53A),
                ),
              ),
            ),

            const SizedBox(height: 32),

            // Title
            Text(
              s.noListingsYet,
              style: AppTextStyles.font24Bold,
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 16),

            // Description
            Text(
              s.noListingsDescription,
              style: AppTextStyles.font16Regular.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 32),

            // Create Listing Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: onCreateListing,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFFEC53A),
                  foregroundColor: Colors.black,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                icon: const Icon(Icons.add_home),
                label: Text(
                  s.createFirstListing,
                  style: AppTextStyles.font16SemiBold,
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Tips Section
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.blue.withValues(alpha: 0.2)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.lightbulb_outline,
                        color: Colors.blue[700],
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        s.hostingTips,
                        style: AppTextStyles.font14SemiBold.copyWith(
                          color: Colors.blue[700],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  _buildTip(s.tip1, Icons.camera_alt),
                  const SizedBox(height: 8),
                  _buildTip(s.tip2, Icons.description),
                  const SizedBox(height: 8),
                  _buildTip(s.tip3, Icons.price_check),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Help Section
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  s.needHelp,
                  style: AppTextStyles.font14Regular.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(width: 8),
                GestureDetector(
                  onTap: () {
                    // Navigate to help or support
                  },
                  child: Text(
                    s.contactSupport,
                    style: AppTextStyles.font14SemiBold.copyWith(
                      color: const Color(0xFFFEC53A),
                      decoration: TextDecoration.underline,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTip(String text, IconData icon) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          size: 16,
          color: Colors.blue[600],
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            text,
            style: AppTextStyles.font12Regular.copyWith(
              color: Colors.blue[800],
            ),
          ),
        ),
      ],
    );
  }
}
