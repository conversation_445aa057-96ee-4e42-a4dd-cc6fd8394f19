import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/feature/host/data/models/my_listing_model.dart';
import 'package:gather_point/feature/host/presentation/cubit/my_listings_cubit.dart';

class ListingStatusChangeDialog extends StatefulWidget {
  final MyListingModel listing;

  const ListingStatusChangeDialog({
    super.key,
    required this.listing,
  });

  @override
  State<ListingStatusChangeDialog> createState() => _ListingStatusChangeDialogState();
}

class _ListingStatusChangeDialogState extends State<ListingStatusChangeDialog> {
  String? _selectedStatus;
  final TextEditingController _reasonController = TextEditingController();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _selectedStatus = widget.listing.status;
  }

  @override
  void dispose() {
    _reasonController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: double.infinity,
        constraints: const BoxConstraints(maxWidth: 400),
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.edit_note,
                  color: const Color(0xFFFEC53A),
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    s.changeListingStatus,
                    style: AppTextStyles.font18Bold,
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Current Status
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  Text(
                    s.currentStatus,
                    style: AppTextStyles.font14SemiBold,
                  ),
                  const Spacer(),
                  _buildStatusBadge(widget.listing.status),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Status Options
            Text(
              s.selectNewStatus,
              style: AppTextStyles.font16SemiBold,
            ),
            const SizedBox(height: 12),

            ..._buildStatusOptions(),

            // Reason (if needed)
            if (_needsReason()) ...[
              const SizedBox(height: 24),
              Text(
                s.changeReason,
                style: AppTextStyles.font14SemiBold,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: _reasonController,
                maxLines: 3,
                decoration: InputDecoration(
                  hintText: s.enterChangeReason,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ],

            const SizedBox(height: 24),

            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: _isLoading ? null : () => Navigator.pop(context),
                    child: Text(s.cancel),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading || !_canChangeStatus() 
                        ? null 
                        : _changeStatus,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFFFEC53A),
                      foregroundColor: Colors.black,
                    ),
                    child: _isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : Text(s.changeStatus),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildStatusOptions() {
    final availableStatuses = _getAvailableStatuses();
    
    return availableStatuses.map((status) {
      return Padding(
        padding: const EdgeInsets.only(bottom: 8),
        child: RadioListTile<String>(
          value: status,
          groupValue: _selectedStatus,
          onChanged: (value) {
            setState(() {
              _selectedStatus = value;
            });
          },
          title: Row(
            children: [
              _buildStatusBadge(status),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _getStatusDisplayName(status),
                      style: AppTextStyles.font14SemiBold,
                    ),
                    Text(
                      _getStatusDescription(status),
                      style: AppTextStyles.font12Regular.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          activeColor: const Color(0xFFFEC53A),
        ),
      );
    }).toList();
  }

  Widget _buildStatusBadge(String status) {
    Color backgroundColor;
    Color textColor;
    IconData icon;

    switch (status) {
      case 'active':
        backgroundColor = Colors.green;
        textColor = Colors.white;
        icon = Icons.visibility;
        break;
      case 'inactive':
        backgroundColor = Colors.orange;
        textColor = Colors.white;
        icon = Icons.visibility_off;
        break;
      case 'draft':
        backgroundColor = Colors.grey;
        textColor = Colors.white;
        icon = Icons.edit;
        break;
      case 'pending':
        backgroundColor = Colors.blue;
        textColor = Colors.white;
        icon = Icons.pending;
        break;
      default:
        backgroundColor = Colors.grey;
        textColor = Colors.white;
        icon = Icons.help;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: textColor, size: 12),
          const SizedBox(width: 4),
          Text(
            _getStatusDisplayName(status),
            style: AppTextStyles.font10Regular.copyWith(
              color: textColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  List<String> _getAvailableStatuses() {
    final currentStatus = widget.listing.status;
    
    switch (currentStatus) {
      case 'draft':
        return ['draft', 'pending'];
      case 'pending':
        return ['pending', 'draft'];
      case 'active':
        return ['active', 'inactive'];
      case 'inactive':
        return ['inactive', 'active'];
      case 'suspended':
        return ['suspended']; // Can't change from suspended
      default:
        return ['draft', 'pending', 'active', 'inactive'];
    }
  }

  String _getStatusDisplayName(String status) {
    switch (status) {
      case 'active':
        return 'نشط';
      case 'inactive':
        return 'غير نشط';
      case 'draft':
        return 'مسودة';
      case 'pending':
        return 'قيد المراجعة';
      case 'suspended':
        return 'معلق';
      default:
        return status;
    }
  }

  String _getStatusDescription(String status) {
    final s = S.of(context);
    
    switch (status) {
      case 'active':
        return s.activeStatusDescription;
      case 'inactive':
        return s.inactiveStatusDescription;
      case 'draft':
        return s.draftStatusDescription;
      case 'pending':
        return s.pendingStatusDescription;
      case 'suspended':
        return s.suspendedStatusDescription;
      default:
        return '';
    }
  }

  bool _needsReason() {
    if (_selectedStatus == null || _selectedStatus == widget.listing.status) {
      return false;
    }
    
    // Require reason for certain transitions
    return (_selectedStatus == 'inactive' && widget.listing.status == 'active') ||
           (_selectedStatus == 'draft' && widget.listing.status == 'pending');
  }

  bool _canChangeStatus() {
    if (_selectedStatus == null || _selectedStatus == widget.listing.status) {
      return false;
    }
    
    if (_needsReason() && _reasonController.text.trim().isEmpty) {
      return false;
    }
    
    return true;
  }

  Future<void> _changeStatus() async {
    if (!_canChangeStatus()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // For now, we'll use the toggle method
      // In a real implementation, you'd have a more specific method
      await context.read<MyListingsCubit>().toggleListingStatus(widget.listing.id);
      
      if (mounted) {
        Navigator.pop(context, true);
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(S.of(context).statusChangedSuccessfully),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(S.of(context).statusChangeError),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
