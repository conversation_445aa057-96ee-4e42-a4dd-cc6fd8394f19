import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';

class AnalyticsMetricCard extends StatelessWidget {
  final String title;
  final String value;
  final double? change;
  final IconData icon;
  final Color color;
  final String? subtitle;

  const AnalyticsMetricCard({
    super.key,
    required this.title,
    required this.value,
    this.change,
    required this.icon,
    required this.color,
    this.subtitle,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with icon
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 20,
                ),
              ),
              const Spacer(),
              if (change != null) _buildChangeIndicator(),
            ],
          ),

          const SizedBox(height: 12),

          // Value
          Text(
            value,
            style: AppTextStyles.font20Bold.copyWith(
              color: color,
            ),
          ),

          const SizedBox(height: 4),

          // Title
          Text(
            title,
            style: AppTextStyles.font12Regular.copyWith(
              color: Colors.grey[600],
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),

          // Subtitle if provided
          if (subtitle != null) ...[
            const SizedBox(height: 4),
            Text(
              subtitle!,
              style: AppTextStyles.font10Regular.copyWith(
                color: Colors.grey[500],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildChangeIndicator() {
    if (change == null) return const SizedBox.shrink();

    final isPositive = change! > 0;
    final isNegative = change! < 0;
    final changeColor = isPositive ? Colors.green : (isNegative ? Colors.red : Colors.grey);
    final changeIcon = isPositive ? Icons.trending_up : (isNegative ? Icons.trending_down : Icons.trending_flat);
    final changeText = '${isPositive ? '+' : ''}${(change! * 100).toStringAsFixed(1)}%';

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: changeColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            changeIcon,
            color: changeColor,
            size: 12,
          ),
          const SizedBox(width: 2),
          Text(
            changeText,
            style: AppTextStyles.font10Regular.copyWith(
              color: changeColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
