import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/feature/host/data/models/my_listing_model.dart';
import 'package:gather_point/feature/host/utils/navigation_helpers.dart';

class ListingCard extends StatelessWidget {
  final MyListingModel listing;
  final bool isGridView;
  final bool isSelectionMode;
  final bool isSelected;
  final VoidCallback onTap;
  final Function(bool) onSelectionChanged;

  const ListingCard({
    super.key,
    required this.listing,
    required this.isGridView,
    required this.isSelectionMode,
    required this.isSelected,
    required this.onTap,
    required this.onSelectionChanged,
  });

  @override
  Widget build(BuildContext context) {
    if (isGridView) {
      return _buildGridCard(context);
    } else {
      return _buildListCard(context);
    }
  }

  Widget _buildGridCard(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Stack(
          children: [
            // Main Card
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Image
                  Expanded(
                    flex: 3,
                    child: _buildImage(),
                  ),

                  // Content
                  Expanded(
                    flex: 2,
                    child: Padding(
                      padding: const EdgeInsets.all(12),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Title
                          Text(
                            listing.title,
                            style: AppTextStyles.font14SemiBold,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),

                          // Price
                          Text(
                            listing.priceDisplay,
                            style: AppTextStyles.font16Bold.copyWith(
                              color: const Color(0xFFFEC53A),
                            ),
                          ),
                          const Spacer(),

                          // Performance
                          Text(
                            listing.performanceDisplay,
                            style: AppTextStyles.font10Regular.copyWith(
                              color: Colors.grey[600],
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Status Badge
            Positioned(
              top: 8,
              left: 8,
              child: _buildStatusBadge(),
            ),

            // Selection Checkbox
            if (isSelectionMode)
              Positioned(
                top: 8,
                right: 8,
                child: Checkbox(
                  value: isSelected,
                  onChanged: (value) => onSelectionChanged(value ?? false),
                  shape: const CircleBorder(),
                ),
              ),

            // Quick Actions
            if (!isSelectionMode)
              Positioned(
                top: 8,
                right: 8,
                child: _buildQuickActions(context),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildListCard(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Stack(
          children: [
            Row(
              children: [
                // Image
                SizedBox(
                  width: 120,
                  height: 120,
                  child: _buildImage(),
                ),

                // Content
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Title and Status
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                listing.title,
                                style: AppTextStyles.font16SemiBold,
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const SizedBox(width: 8),
                            _buildStatusBadge(),
                          ],
                        ),
                        const SizedBox(height: 8),

                        // Details
                        Text(
                          listing.roomsDisplay,
                          style: AppTextStyles.font12Regular.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 4),

                        // Price
                        Text(
                          listing.priceDisplay,
                          style: AppTextStyles.font18Bold.copyWith(
                            color: const Color(0xFFFEC53A),
                          ),
                        ),
                        const SizedBox(height: 8),

                        // Performance
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                listing.performanceDisplay,
                                style: AppTextStyles.font12Regular.copyWith(
                                  color: Colors.grey[600],
                                ),
                              ),
                            ),
                            Text(
                              listing.timeSinceCreated,
                              style: AppTextStyles.font10Regular.copyWith(
                                color: Colors.grey[500],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),

            // Selection Checkbox
            if (isSelectionMode)
              Positioned(
                top: 8,
                right: 8,
                child: Checkbox(
                  value: isSelected,
                  onChanged: (value) => onSelectionChanged(value ?? false),
                  shape: const CircleBorder(),
                ),
              ),

            // Quick Actions
            if (!isSelectionMode)
              Positioned(
                top: 8,
                right: 8,
                child: _buildQuickActions(context),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildImage() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(isGridView ? 12 : 8),
      child: listing.mainImageUrl != null
          ? Image.network(
              listing.mainImageUrl!,
              fit: BoxFit.cover,
              width: double.infinity,
              height: double.infinity,
              errorBuilder: (context, error, stackTrace) => _buildPlaceholderImage(),
            )
          : _buildPlaceholderImage(),
    );
  }

  Widget _buildPlaceholderImage() {
    return Container(
      color: Colors.grey[200],
      child: Center(
        child: Icon(
          Icons.image,
          size: isGridView ? 32 : 24,
          color: Colors.grey[400],
        ),
      ),
    );
  }

  Widget _buildStatusBadge() {
    Color backgroundColor;
    Color textColor;
    IconData icon;

    switch (listing.status) {
      case 'active':
        backgroundColor = Colors.green;
        textColor = Colors.white;
        icon = Icons.visibility;
        break;
      case 'inactive':
        backgroundColor = Colors.orange;
        textColor = Colors.white;
        icon = Icons.visibility_off;
        break;
      case 'draft':
        backgroundColor = Colors.grey;
        textColor = Colors.white;
        icon = Icons.edit;
        break;
      case 'pending':
        backgroundColor = Colors.blue;
        textColor = Colors.white;
        icon = Icons.pending;
        break;
      case 'suspended':
        backgroundColor = Colors.red;
        textColor = Colors.white;
        icon = Icons.block;
        break;
      default:
        backgroundColor = Colors.grey;
        textColor = Colors.white;
        icon = Icons.help;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: textColor, size: 12),
          const SizedBox(width: 4),
          Text(
            listing.statusDisplayName,
            style: AppTextStyles.font10Regular.copyWith(
              color: textColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions(BuildContext context) {
    return PopupMenuButton<String>(
      icon: Container(
        padding: const EdgeInsets.all(4),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.5),
          shape: BoxShape.circle,
        ),
        child: const Icon(
          Icons.more_vert,
          color: Colors.white,
          size: 16,
        ),
      ),
      onSelected: (value) => _handleQuickAction(context, value),
      itemBuilder: (context) => [
        const PopupMenuItem(
          value: 'edit',
          child: Row(
            children: [
              Icon(Icons.edit, size: 16),
              SizedBox(width: 8),
              Text('تعديل'),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'toggle_status',
          child: Row(
            children: [
              Icon(
                listing.isActive ? Icons.visibility_off : Icons.visibility,
                size: 16,
              ),
              const SizedBox(width: 8),
              Text(listing.isActive ? 'إلغاء تفعيل' : 'تفعيل'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'duplicate',
          child: Row(
            children: [
              Icon(Icons.copy, size: 16),
              SizedBox(width: 8),
              Text('نسخ'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'analytics',
          child: Row(
            children: [
              Icon(Icons.analytics, size: 16),
              SizedBox(width: 8),
              Text('الإحصائيات'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'delete',
          child: Row(
            children: [
              Icon(Icons.delete, size: 16, color: Colors.red),
              SizedBox(width: 8),
              Text('حذف', style: TextStyle(color: Colors.red)),
            ],
          ),
        ),
      ],
    );
  }

  void _handleQuickAction(BuildContext context, String action) {
    switch (action) {
      case 'edit':
        HostNavigationHelpers.editProperty(
          context,
          propertyId: listing.id,
          propertyData: listing,
        );
        break;
      case 'toggle_status':
        // Handle status toggle
        break;
      case 'duplicate':
        // Handle duplication
        break;
      case 'analytics':
        HostNavigationHelpers.viewPropertyAnalytics(
          context,
          propertyId: listing.id,
          propertyTitle: listing.title,
        );
        break;
      case 'delete':
        _showDeleteDialog(context);
        break;
    }
  }

  void _showDeleteDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف العقار'),
        content: Text('هل أنت متأكد من حذف "${listing.title}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // Handle deletion
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}
