import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/feature/host/data/models/my_listing_model.dart';
import 'package:gather_point/feature/host/presentation/cubit/my_listings_cubit.dart';

class ListingStatusManager extends StatelessWidget {
  final MyListingModel listing;
  final bool showAsCard;
  final VoidCallback? onStatusChanged;

  const ListingStatusManager({
    super.key,
    required this.listing,
    this.showAsCard = true,
    this.onStatusChanged,
  });

  @override
  Widget build(BuildContext context) {
    if (showAsCard) {
      return _buildStatusCard(context);
    } else {
      return _buildStatusRow(context);
    }
  }

  Widget _buildStatusCard(BuildContext context) {
    final s = S.of(context);

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                Icons.settings,
                color: const Color(0xFFFEC53A),
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                s.listingStatus,
                style: AppTextStyles.font16SemiBold,
              ),
              const Spacer(),
              _buildCurrentStatusBadge(),
            ],
          ),

          const SizedBox(height: 16),

          // Status Description
          Text(
            _getStatusDescription(listing.status, s),
            style: AppTextStyles.font14Regular.copyWith(
              color: Colors.grey[600],
            ),
          ),

          const SizedBox(height: 16),

          // Action Buttons
          _buildActionButtons(context),

          // Additional Info
          if (listing.rejectionReason != null) ...[
            const SizedBox(height: 16),
            _buildRejectionInfo(context),
          ],

          if (listing.pendingReservations > 0) ...[
            const SizedBox(height: 16),
            _buildPendingReservationsInfo(context),
          ],
        ],
      ),
    );
  }

  Widget _buildStatusRow(BuildContext context) {
    return Row(
      children: [
        _buildCurrentStatusBadge(),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            _getStatusDescription(listing.status, S.of(context)),
            style: AppTextStyles.font12Regular.copyWith(
              color: Colors.grey[600],
            ),
          ),
        ),
        _buildQuickToggleButton(context),
      ],
    );
  }

  Widget _buildCurrentStatusBadge() {
    Color backgroundColor;
    Color textColor;
    IconData icon;

    switch (listing.status) {
      case 'active':
        backgroundColor = Colors.green;
        textColor = Colors.white;
        icon = Icons.visibility;
        break;
      case 'inactive':
        backgroundColor = Colors.orange;
        textColor = Colors.white;
        icon = Icons.visibility_off;
        break;
      case 'draft':
        backgroundColor = Colors.grey;
        textColor = Colors.white;
        icon = Icons.edit;
        break;
      case 'pending':
        backgroundColor = Colors.blue;
        textColor = Colors.white;
        icon = Icons.pending;
        break;
      case 'suspended':
        backgroundColor = Colors.red;
        textColor = Colors.white;
        icon = Icons.block;
        break;
      default:
        backgroundColor = Colors.grey;
        textColor = Colors.white;
        icon = Icons.help;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: textColor, size: 14),
          const SizedBox(width: 6),
          Text(
            listing.statusDisplayName,
            style: AppTextStyles.font12SemiBold.copyWith(
              color: textColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    final s = S.of(context);
    final buttons = <Widget>[];

    switch (listing.status) {
      case 'draft':
        buttons.addAll([
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => _publishListing(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFFEC53A),
                foregroundColor: Colors.black,
              ),
              icon: const Icon(Icons.publish),
              label: Text(s.publishListing),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: OutlinedButton.icon(
              onPressed: () => _editListing(context),
              icon: const Icon(Icons.edit),
              label: Text(s.editListing),
            ),
          ),
        ]);
        break;

      case 'active':
        buttons.addAll([
          Expanded(
            child: OutlinedButton.icon(
              onPressed: () => _deactivateListing(context),
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.orange,
                side: const BorderSide(color: Colors.orange),
              ),
              icon: const Icon(Icons.visibility_off),
              label: Text(s.deactivate),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => _editListing(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFFEC53A),
                foregroundColor: Colors.black,
              ),
              icon: const Icon(Icons.edit),
              label: Text(s.editListing),
            ),
          ),
        ]);
        break;

      case 'inactive':
        buttons.addAll([
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => _activateListing(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
              icon: const Icon(Icons.visibility),
              label: Text(s.activate),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: OutlinedButton.icon(
              onPressed: () => _editListing(context),
              icon: const Icon(Icons.edit),
              label: Text(s.editListing),
            ),
          ),
        ]);
        break;

      case 'pending':
        buttons.add(
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () => _editListing(context),
              icon: const Icon(Icons.edit),
              label: Text(s.editWhilePending),
            ),
          ),
        );
        break;

      case 'suspended':
        buttons.add(
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () => _contactSupport(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
              icon: const Icon(Icons.support_agent),
              label: Text(s.contactSupport),
            ),
          ),
        );
        break;
    }

    return Row(children: buttons);
  }

  Widget _buildQuickToggleButton(BuildContext context) {
    if (listing.status == 'active') {
      return IconButton(
        onPressed: () => _deactivateListing(context),
        icon: const Icon(Icons.visibility_off, color: Colors.orange),
        tooltip: S.of(context).deactivate,
      );
    } else if (listing.status == 'inactive') {
      return IconButton(
        onPressed: () => _activateListing(context),
        icon: const Icon(Icons.visibility, color: Colors.green),
        tooltip: S.of(context).activate,
      );
    }
    return const SizedBox.shrink();
  }

  Widget _buildRejectionInfo(BuildContext context) {
    final s = S.of(context);

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.error, color: Colors.red, size: 20),
              const SizedBox(width: 8),
              Text(
                s.rejectionReason,
                style: AppTextStyles.font14SemiBold.copyWith(
                  color: Colors.red[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            listing.rejectionReason!,
            style: AppTextStyles.font12Regular.copyWith(
              color: Colors.red[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPendingReservationsInfo(BuildContext context) {
    final s = S.of(context);

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.orange.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          const Icon(Icons.pending_actions, color: Colors.orange, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              '${listing.pendingReservations} ${s.pendingReservations}',
              style: AppTextStyles.font12SemiBold.copyWith(
                color: Colors.orange[700],
              ),
            ),
          ),
          TextButton(
            onPressed: () => _viewReservations(context),
            child: Text(s.viewReservations),
          ),
        ],
      ),
    );
  }

  String _getStatusDescription(String status, S s) {
    switch (status) {
      case 'active':
        return s.activeStatusDescription;
      case 'inactive':
        return s.inactiveStatusDescription;
      case 'draft':
        return s.draftStatusDescription;
      case 'pending':
        return s.pendingStatusDescription;
      case 'suspended':
        return s.suspendedStatusDescription;
      default:
        return s.unknownStatusDescription;
    }
  }

  // Action Methods
  void _publishListing(BuildContext context) {
    _showConfirmationDialog(
      context,
      title: S.of(context).publishListing,
      message: S.of(context).publishListingConfirmation,
      onConfirm: () {
        context.read<MyListingsCubit>().toggleListingStatus(listing.id);
        onStatusChanged?.call();
      },
    );
  }

  void _activateListing(BuildContext context) {
    context.read<MyListingsCubit>().toggleListingStatus(listing.id);
    onStatusChanged?.call();
  }

  void _deactivateListing(BuildContext context) {
    _showConfirmationDialog(
      context,
      title: S.of(context).deactivateListing,
      message: S.of(context).deactivateListingConfirmation,
      onConfirm: () {
        context.read<MyListingsCubit>().toggleListingStatus(listing.id);
        onStatusChanged?.call();
      },
    );
  }

  void _editListing(BuildContext context) {
    Navigator.pushNamed(
      context,
      '/edit-property',
      arguments: listing.id,
    );
  }

  void _contactSupport(BuildContext context) {
    Navigator.pushNamed(context, '/support');
  }

  void _viewReservations(BuildContext context) {
    Navigator.pushNamed(
      context,
      '/listing-reservations',
      arguments: listing.id,
    );
  }

  void _showConfirmationDialog(
    BuildContext context, {
    required String title,
    required String message,
    required VoidCallback onConfirm,
  }) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(S.of(context).cancel),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              onConfirm();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFFEC53A),
              foregroundColor: Colors.black,
            ),
            child: Text(S.of(context).confirm),
          ),
        ],
      ),
    );
  }
}
