import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/feature/host/data/models/listing_analytics_model.dart';

class PerformanceInsightsWidget extends StatelessWidget {
  final List<PerformanceInsight> insights;
  final List<String> recommendations;

  const PerformanceInsightsWidget({
    super.key,
    required this.insights,
    required this.recommendations,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Insights Section
          if (insights.isNotEmpty) ...[
            Text(
              'رؤى الأداء',
              style: AppTextStyles.font18Bold,
            ),
            const SizedBox(height: 16),
            ...insights.map((insight) => _buildInsightCard(insight)),
            const SizedBox(height: 24),
          ],

          // Recommendations Section
          if (recommendations.isNotEmpty) ...[
            Text(
              'توصيات للتحسين',
              style: AppTextStyles.font18Bold,
            ),
            const SizedBox(height: 16),
            ...recommendations.asMap().entries.map((entry) {
              return _buildRecommendationCard(entry.key + 1, entry.value);
            }),
          ],

          // Empty State
          if (insights.isEmpty && recommendations.isEmpty)
            _buildEmptyState(),
        ],
      ),
    );
  }

  Widget _buildInsightCard(PerformanceInsight insight) {
    Color cardColor;
    IconData cardIcon;

    switch (insight.type) {
      case 'positive':
        cardColor = Colors.green;
        cardIcon = Icons.trending_up;
        break;
      case 'negative':
        cardColor = Colors.red;
        cardIcon = Icons.trending_down;
        break;
      default:
        cardColor = Colors.blue;
        cardIcon = Icons.info;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: cardColor.withValues(alpha: 0.3)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: cardColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  cardIcon,
                  color: cardColor,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  insight.title,
                  style: AppTextStyles.font16SemiBold.copyWith(
                    color: cardColor,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Description
          Text(
            insight.description,
            style: AppTextStyles.font14Regular.copyWith(
              color: Colors.grey[700],
            ),
          ),

          // Action Button
          if (insight.actionText != null) ...[
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () {
                  // Handle action navigation
                  if (insight.actionRoute != null) {
                    // Navigator.pushNamed(context, insight.actionRoute!);
                  }
                },
                style: OutlinedButton.styleFrom(
                  foregroundColor: cardColor,
                  side: BorderSide(color: cardColor),
                ),
                icon: Icon(Icons.arrow_forward, size: 16),
                label: Text(insight.actionText!),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildRecommendationCard(int index, String recommendation) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFFEC53A).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFFEC53A).withValues(alpha: 0.3)),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Number Badge
          Container(
            width: 24,
            height: 24,
            decoration: const BoxDecoration(
              color: Color(0xFFFEC53A),
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                '$index',
                style: AppTextStyles.font12SemiBold.copyWith(
                  color: Colors.black,
                ),
              ),
            ),
          ),

          const SizedBox(width: 12),

          // Recommendation Text
          Expanded(
            child: Text(
              recommendation,
              style: AppTextStyles.font14Regular.copyWith(
                color: Colors.grey[800],
              ),
            ),
          ),

          // Check Icon
          Icon(
            Icons.lightbulb_outline,
            color: const Color(0xFFFEC53A),
            size: 20,
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        children: [
          Icon(
            Icons.analytics_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد رؤى متاحة حالياً',
            style: AppTextStyles.font16SemiBold.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'سيتم إنشاء رؤى وتوصيات مخصصة بناءً على أداء عقارك',
            style: AppTextStyles.font14Regular.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
