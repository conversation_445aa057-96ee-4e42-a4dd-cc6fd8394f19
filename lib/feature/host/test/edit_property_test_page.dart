import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/core/databases/api/api_consumer.dart';
import 'package:gather_point/feature/host/presentation/cubit/property_creation_cubit.dart';
import 'package:gather_point/feature/host/presentation/views/edit_property_page.dart';
import 'package:gather_point/feature/host/services/property_edit_service.dart';
import 'package:gather_point/feature/dashboard/data/services/properties_api_service.dart';

/// Test page to easily access the edit property functionality
class EditPropertyTestPage extends StatelessWidget {
  const EditPropertyTestPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Edit Property Test'),
        backgroundColor: const Color(0xFFFEC53A),
        foregroundColor: Colors.black,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Test Edit Property Functionality',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            
            // Test with mock property ID 1
            ElevatedButton.icon(
              onPressed: () => _navigateToEditProperty(context, 1),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFFEC53A),
                foregroundColor: Colors.black,
                padding: const EdgeInsets.all(16),
              ),
              icon: const Icon(Icons.edit),
              label: const Text('Edit Property #1 (Mock Data)'),
            ),
            
            const SizedBox(height: 16),
            
            // Test with mock property ID 2
            ElevatedButton.icon(
              onPressed: () => _navigateToEditProperty(context, 2),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFFEC53A),
                foregroundColor: Colors.black,
                padding: const EdgeInsets.all(16),
              ),
              icon: const Icon(Icons.edit),
              label: const Text('Edit Property #2 (Mock Data)'),
            ),
            
            const SizedBox(height: 16),
            
            // Test with mock property ID 3
            ElevatedButton.icon(
              onPressed: () => _navigateToEditProperty(context, 3),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFFEC53A),
                foregroundColor: Colors.black,
                padding: const EdgeInsets.all(16),
              ),
              icon: const Icon(Icons.edit),
              label: const Text('Edit Property #3 (Mock Data)'),
            ),
            
            const SizedBox(height: 32),
            
            const Text(
              'Instructions:',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              '1. Click any button above to test the edit property functionality\n'
              '2. You should see the EditPropertyForm with pre-populated data\n'
              '3. The form should show existing property information\n'
              '4. You can modify the data and save changes\n'
              '5. Image management should allow adding/removing images',
              style: TextStyle(fontSize: 14),
            ),
            
            const SizedBox(height: 32),
            
            // Navigation to other host features
            OutlinedButton.icon(
              onPressed: () => Navigator.pushNamed(context, '/host/my-listings'),
              icon: const Icon(Icons.list),
              label: const Text('Go to My Listings'),
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToEditProperty(BuildContext context, int propertyId) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => MultiBlocProvider(
          providers: [
            BlocProvider(
              create: (context) => PropertyCreationCubit(
                context.read<PropertiesApiService>(),
                PropertyEditService(context.read<ApiConsumer>()),
              ),
            ),
          ],
          child: EditPropertyPage(
            propertyId: propertyId,
          ),
        ),
      ),
    );
  }
}

/// Widget to add to your main app for easy testing
class EditPropertyTestButton extends StatelessWidget {
  const EditPropertyTestButton({super.key});

  @override
  Widget build(BuildContext context) {
    return FloatingActionButton.extended(
      onPressed: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const EditPropertyTestPage(),
          ),
        );
      },
      backgroundColor: const Color(0xFFFEC53A),
      foregroundColor: Colors.black,
      icon: const Icon(Icons.edit),
      label: const Text('Test Edit'),
    );
  }
}

/// Extension to easily add edit property test to any screen
extension EditPropertyTestExtension on Widget {
  Widget withEditPropertyTest(BuildContext context) {
    return Stack(
      children: [
        this,
        Positioned(
          bottom: 16,
          left: 16,
          child: FloatingActionButton(
            mini: true,
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const EditPropertyTestPage(),
                ),
              );
            },
            backgroundColor: Colors.orange,
            child: const Icon(Icons.bug_report, color: Colors.white),
          ),
        ),
      ],
    );
  }
}
