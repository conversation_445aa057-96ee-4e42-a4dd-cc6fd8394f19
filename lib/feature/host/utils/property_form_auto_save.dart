import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class PropertyFormAutoSave {
  static const String _autoSaveKey = 'property_form_auto_save';
  static const String _autoSaveTimestampKey = 'property_form_auto_save_timestamp';
  
  /// Save form data to local storage
  static Future<void> saveFormData({
    required String title,
    required String description,
    required String price,
    String? weekendPrice,
    String? weeklyPrice,
    String? monthlyPrice,
    required String guests,
    required String beds,
    required String baths,
    String? bookingRules,
    String? cancellationRules,
    int? selectedCategoryId,
    int? selectedPropertyTypeId,
    int? selectedCancellationPolicyId,
    List<int>? selectedFacilities,
    double? latitude,
    double? longitude,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      final formData = {
        'title': title,
        'description': description,
        'price': price,
        'weekend_price': weekendPrice,
        'weekly_price': weeklyPrice,
        'monthly_price': monthlyPrice,
        'guests': guests,
        'beds': beds,
        'baths': baths,
        'booking_rules': bookingRules,
        'cancellation_rules': cancellationRules,
        'selected_category_id': selectedCategoryId,
        'selected_property_type_id': selectedPropertyTypeId,
        'selected_cancellation_policy_id': selectedCancellationPolicyId,
        'selected_facilities': selectedFacilities,
        'latitude': latitude,
        'longitude': longitude,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };
      
      await prefs.setString(_autoSaveKey, jsonEncode(formData));
      await prefs.setInt(_autoSaveTimestampKey, DateTime.now().millisecondsSinceEpoch);
    } catch (e) {
      // Silently fail - auto-save is not critical
      debugPrint('Auto-save failed: $e');
    }
  }
  
  /// Load saved form data from local storage
  static Future<Map<String, dynamic>?> loadFormData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedData = prefs.getString(_autoSaveKey);
      
      if (savedData != null) {
        final formData = jsonDecode(savedData) as Map<String, dynamic>;
        
        // Check if data is not too old (older than 7 days)
        final timestamp = formData['timestamp'] as int?;
        if (timestamp != null) {
          final savedDate = DateTime.fromMillisecondsSinceEpoch(timestamp);
          final daysDifference = DateTime.now().difference(savedDate).inDays;
          
          if (daysDifference > 7) {
            // Data is too old, clear it
            await clearSavedData();
            return null;
          }
        }
        
        return formData;
      }
    } catch (e) {
      // Silently fail and clear corrupted data
      debugPrint('Auto-load failed: $e');
      await clearSavedData();
    }
    
    return null;
  }
  
  /// Clear saved form data
  static Future<void> clearSavedData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_autoSaveKey);
      await prefs.remove(_autoSaveTimestampKey);
    } catch (e) {
      debugPrint('Failed to clear auto-save data: $e');
    }
  }
  
  /// Check if there is saved data available
  static Future<bool> hasSavedData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.containsKey(_autoSaveKey);
    } catch (e) {
      return false;
    }
  }
  
  /// Get the timestamp of the last save
  static Future<DateTime?> getLastSaveTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final timestamp = prefs.getInt(_autoSaveTimestampKey);
      
      if (timestamp != null) {
        return DateTime.fromMillisecondsSinceEpoch(timestamp);
      }
    } catch (e) {
      debugPrint('Failed to get last save time: $e');
    }
    
    return null;
  }
  
  /// Format the last save time for display
  static String formatLastSaveTime(DateTime lastSave) {
    final now = DateTime.now();
    final difference = now.difference(lastSave);
    
    if (difference.inMinutes < 1) {
      return 'منذ لحظات';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else {
      return 'منذ ${difference.inDays} يوم';
    }
  }
  
  /// Auto-save form data with debouncing
  static Future<void> autoSaveWithDebounce({
    required String title,
    required String description,
    required String price,
    String? weekendPrice,
    String? weeklyPrice,
    String? monthlyPrice,
    required String guests,
    required String beds,
    required String baths,
    String? bookingRules,
    String? cancellationRules,
    int? selectedCategoryId,
    int? selectedPropertyTypeId,
    int? selectedCancellationPolicyId,
    List<int>? selectedFacilities,
    double? latitude,
    double? longitude,
  }) async {
    // Only save if there's meaningful content
    if (title.trim().isNotEmpty || 
        description.trim().isNotEmpty || 
        price.trim().isNotEmpty) {
      
      await saveFormData(
        title: title,
        description: description,
        price: price,
        weekendPrice: weekendPrice,
        weeklyPrice: weeklyPrice,
        monthlyPrice: monthlyPrice,
        guests: guests,
        beds: beds,
        baths: baths,
        bookingRules: bookingRules,
        cancellationRules: cancellationRules,
        selectedCategoryId: selectedCategoryId,
        selectedPropertyTypeId: selectedPropertyTypeId,
        selectedCancellationPolicyId: selectedCancellationPolicyId,
        selectedFacilities: selectedFacilities,
        latitude: latitude,
        longitude: longitude,
      );
    }
  }
}
