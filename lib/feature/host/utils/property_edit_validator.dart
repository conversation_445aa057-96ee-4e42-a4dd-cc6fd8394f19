import 'package:gather_point/feature/host/data/models/my_listing_model.dart';

class PropertyEditValidator {
  /// Validate property data for editing
  static ValidationResult validatePropertyEdit(
    MyListingModel originalProperty,
    Map<String, dynamic> updatedData,
  ) {
    final errors = <String>[];
    final warnings = <String>[];
    final changes = <PropertyChange>[];

    // Detect and validate changes
    _validateTitleChange(originalProperty, updatedData, errors, warnings, changes);
    _validateContentChange(originalProperty, updatedData, errors, warnings, changes);
    _validatePriceChange(originalProperty, updatedData, errors, warnings, changes);
    _validateLocationChange(originalProperty, updatedData, errors, warnings, changes);
    _validateImageChanges(originalProperty, updatedData, errors, warnings, changes);

    // Check if any changes were made
    if (changes.isEmpty) {
      warnings.add('لم يتم إجراء أي تغييرات على العقار');
    }

    // Validate business rules
    _validateBusinessRules(originalProperty, updatedData, errors, warnings);

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
      changes: changes,
      hasChanges: changes.isNotEmpty,
    );
  }

  static void _validateTitleChange(
    MyListingModel original,
    Map<String, dynamic> updated,
    List<String> errors,
    List<String> warnings,
    List<PropertyChange> changes,
  ) {
    final newTitle = updated['title'] as String?;
    
    if (newTitle == null || newTitle.isEmpty) {
      errors.add('عنوان العقار مطلوب');
      return;
    }

    if (newTitle.length < 10) {
      errors.add('عنوان العقار يجب أن يكون 10 أحرف على الأقل');
    }

    if (newTitle.length > 100) {
      errors.add('عنوان العقار يجب أن يكون أقل من 100 حرف');
    }

    if (newTitle != original.title) {
      changes.add(PropertyChange(
        field: 'title',
        oldValue: original.title,
        newValue: newTitle,
        type: ChangeType.text,
      ));

      // Check for significant title changes
      if (_calculateSimilarity(original.title, newTitle) < 0.5) {
        warnings.add('تم تغيير العنوان بشكل كبير، قد يؤثر هذا على ترتيب العقار في نتائج البحث');
      }
    }
  }

  static void _validateContentChange(
    MyListingModel original,
    Map<String, dynamic> updated,
    List<String> errors,
    List<String> warnings,
    List<PropertyChange> changes,
  ) {
    final newContent = updated['content'] as String?;
    
    if (newContent == null || newContent.isEmpty) {
      errors.add('وصف العقار مطلوب');
      return;
    }

    if (newContent.length < 50) {
      warnings.add('وصف العقار قصير، يُنصح بإضافة المزيد من التفاصيل');
    }

    if (newContent != original.content) {
      changes.add(PropertyChange(
        field: 'content',
        oldValue: original.content,
        newValue: newContent,
        type: ChangeType.text,
      ));
    }
  }

  static void _validatePriceChange(
    MyListingModel original,
    Map<String, dynamic> updated,
    List<String> errors,
    List<String> warnings,
    List<PropertyChange> changes,
  ) {
    final newPrice = updated['price'] as double?;
    
    if (newPrice == null || newPrice <= 0) {
      errors.add('سعر العقار يجب أن يكون أكبر من صفر');
      return;
    }

    if (newPrice < 50) {
      warnings.add('السعر منخفض جداً، تأكد من صحة السعر');
    }

    if (newPrice > 10000) {
      warnings.add('السعر مرتفع جداً، تأكد من صحة السعر');
    }

    if (newPrice != original.price) {
      final priceChange = ((newPrice - original.price) / original.price) * 100;
      
      changes.add(PropertyChange(
        field: 'price',
        oldValue: original.price,
        newValue: newPrice,
        type: ChangeType.price,
        changePercentage: priceChange,
      ));

      // Price change warnings
      if (priceChange.abs() > 50) {
        warnings.add('تغيير كبير في السعر (${priceChange.toStringAsFixed(1)}%)، قد يؤثر على الحجوزات');
      } else if (priceChange > 20) {
        warnings.add('زيادة في السعر (${priceChange.toStringAsFixed(1)}%)، قد تقلل من الطلب');
      } else if (priceChange < -20) {
        warnings.add('انخفاض في السعر (${priceChange.abs().toStringAsFixed(1)}%)، قد يؤثر على الإيرادات');
      }
    }
  }

  static void _validateLocationChange(
    MyListingModel original,
    Map<String, dynamic> updated,
    List<String> errors,
    List<String> warnings,
    List<PropertyChange> changes,
  ) {
    final newLat = updated['lat'] as double?;
    final newLon = updated['lon'] as double?;
    final newAddress = updated['address'] as String?;

    if (newLat != null && newLon != null) {
      if (newLat != original.lat || newLon != original.lon) {
        changes.add(PropertyChange(
          field: 'location',
          oldValue: '${original.lat}, ${original.lon}',
          newValue: '$newLat, $newLon',
          type: ChangeType.location,
        ));

        warnings.add('تم تغيير موقع العقار، تأكد من صحة الموقع الجديد');
      }
    }

    if (newAddress != null && newAddress != original.address) {
      changes.add(PropertyChange(
        field: 'address',
        oldValue: original.address ?? '',
        newValue: newAddress,
        type: ChangeType.text,
      ));
    }
  }

  static void _validateImageChanges(
    MyListingModel original,
    Map<String, dynamic> updated,
    List<String> errors,
    List<String> warnings,
    List<PropertyChange> changes,
  ) {
    final newImages = updated['images'] as List<String>?;
    final imagesToDelete = updated['images_to_delete'] as List<String>? ?? [];
    final newImagesList = updated['new_images'] as List<String>? ?? [];

    if (newImages != null) {
      if (newImages.isEmpty) {
        errors.add('العقار يجب أن يحتوي على صورة واحدة على الأقل');
        return;
      }

      if (newImages.length > 20) {
        warnings.add('عدد كبير من الصور (${newImages.length})، قد يؤثر على سرعة التحميل');
      }

      // Check if images changed
      if (!_listsEqual(original.galleryImages, newImages)) {
        changes.add(PropertyChange(
          field: 'images',
          oldValue: original.galleryImages.length.toString(),
          newValue: newImages.length.toString(),
          type: ChangeType.images,
          metadata: {
            'deleted_count': imagesToDelete.length,
            'added_count': newImagesList.length,
          },
        ));

        if (imagesToDelete.isNotEmpty) {
          warnings.add('سيتم حذف ${imagesToDelete.length} صورة');
        }

        if (newImagesList.isNotEmpty) {
          warnings.add('سيتم إضافة ${newImagesList.length} صورة جديدة');
        }
      }
    }
  }

  static void _validateBusinessRules(
    MyListingModel original,
    Map<String, dynamic> updated,
    List<String> errors,
    List<String> warnings,
  ) {
    // Check if property has active reservations
    if (original.hasActiveReservations) {
      final newPrice = updated['price'] as double?;
      if (newPrice != null && newPrice != original.price) {
        warnings.add('العقار يحتوي على حجوزات نشطة، تغيير السعر قد يؤثر على الحجوزات المستقبلية فقط');
      }

      final newImages = updated['images'] as List<String>?;
      if (newImages != null && !_listsEqual(original.galleryImages, newImages)) {
        warnings.add('العقار يحتوي على حجوزات نشطة، تأكد من أن الصور الجديدة تمثل العقار بدقة');
      }
    }

    // Check property status
    if (original.status == 'pending') {
      warnings.add('العقار قيد المراجعة، قد تحتاج التغييرات إلى مراجعة إضافية');
    }

    if (original.status == 'suspended') {
      errors.add('لا يمكن تعديل عقار معلق، يرجى التواصل مع الدعم');
    }
  }

  static double _calculateSimilarity(String str1, String str2) {
    // Simple similarity calculation based on common words
    final words1 = str1.toLowerCase().split(' ').toSet();
    final words2 = str2.toLowerCase().split(' ').toSet();
    final intersection = words1.intersection(words2);
    final union = words1.union(words2);
    return union.isEmpty ? 0.0 : intersection.length / union.length;
  }

  static bool _listsEqual<T>(List<T> list1, List<T> list2) {
    if (list1.length != list2.length) return false;
    for (int i = 0; i < list1.length; i++) {
      if (list1[i] != list2[i]) return false;
    }
    return true;
  }
}

class ValidationResult {
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;
  final List<PropertyChange> changes;
  final bool hasChanges;

  ValidationResult({
    required this.isValid,
    required this.errors,
    required this.warnings,
    required this.changes,
    required this.hasChanges,
  });

  String get summary {
    if (!hasChanges) return 'لا توجد تغييرات';
    
    final changeCount = changes.length;
    if (changeCount == 1) {
      return 'تغيير واحد: ${changes.first.fieldDisplayName}';
    } else {
      return '$changeCount تغييرات';
    }
  }
}

class PropertyChange {
  final String field;
  final dynamic oldValue;
  final dynamic newValue;
  final ChangeType type;
  final double? changePercentage;
  final Map<String, dynamic>? metadata;

  PropertyChange({
    required this.field,
    required this.oldValue,
    required this.newValue,
    required this.type,
    this.changePercentage,
    this.metadata,
  });

  String get fieldDisplayName {
    switch (field) {
      case 'title':
        return 'العنوان';
      case 'content':
        return 'الوصف';
      case 'price':
        return 'السعر';
      case 'location':
        return 'الموقع';
      case 'address':
        return 'العنوان';
      case 'images':
        return 'الصور';
      default:
        return field;
    }
  }

  String get changeDescription {
    switch (type) {
      case ChangeType.price:
        if (changePercentage != null) {
          final direction = changePercentage! > 0 ? 'زيادة' : 'انخفاض';
          return '$direction بنسبة ${changePercentage!.abs().toStringAsFixed(1)}%';
        }
        return 'تغيير من ${oldValue} إلى ${newValue}';
      case ChangeType.images:
        final meta = metadata ?? {};
        final deleted = meta['deleted_count'] ?? 0;
        final added = meta['added_count'] ?? 0;
        return 'حذف $deleted، إضافة $added';
      default:
        return 'تغيير من "$oldValue" إلى "$newValue"';
    }
  }
}

enum ChangeType {
  text,
  price,
  location,
  images,
  other,
}
