import 'dart:io';

class PropertyValidationHelper {
  // Price validation constants
  static const double minDailyPrice = 50.0;
  static const double maxDailyPrice = 10000.0;
  static const double minWeeklyPrice = 300.0;
  static const double maxWeeklyPrice = 60000.0;
  static const double minMonthlyPrice = 1000.0;
  static const double maxMonthlyPrice = 200000.0;

  // Guest and room validation constants
  static const int minGuests = 1;
  static const int maxGuests = 20;
  static const int minBeds = 1;
  static const int maxBeds = 10;
  static const int minBaths = 1;
  static const int maxBaths = 10;

  // Text validation constants
  static const int minTitleLength = 10;
  static const int maxTitleLength = 100;
  static const int minDescriptionLength = 50;
  static const int maxDescriptionLength = 1000;

  // Media validation constants
  static const int maxImageSizeMB = 5;
  static const int maxVideoSizeMB = 50;
  static const int maxGalleryImages = 20;

  /// Validates property title
  static String? validateTitle(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'يرجى إدخال عنوان العقار';
    }
    
    if (value.trim().length < minTitleLength) {
      return 'يجب أن يكون العنوان $minTitleLength أحرف على الأقل';
    }
    
    if (value.trim().length > maxTitleLength) {
      return 'يجب أن يكون العنوان $maxTitleLength حرف على الأكثر';
    }
    
    return null;
  }

  /// Validates property description
  static String? validateDescription(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'يرجى إدخال وصف العقار';
    }
    
    if (value.trim().length < minDescriptionLength) {
      return 'يجب أن يكون الوصف $minDescriptionLength حرف على الأقل';
    }
    
    if (value.trim().length > maxDescriptionLength) {
      return 'يجب أن يكون الوصف $maxDescriptionLength حرف على الأكثر';
    }
    
    return null;
  }

  /// Validates daily price
  static String? validateDailyPrice(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'يرجى إدخال السعر اليومي';
    }
    
    final price = double.tryParse(value);
    if (price == null) {
      return 'يرجى إدخال سعر صحيح';
    }
    
    if (price < minDailyPrice) {
      return 'السعر اليومي يجب أن يكون $minDailyPrice ريال على الأقل';
    }
    
    if (price > maxDailyPrice) {
      return 'السعر اليومي يجب أن يكون $maxDailyPrice ريال على الأكثر';
    }
    
    return null;
  }

  /// Validates weekend price
  static String? validateWeekendPrice(String? value, double? dailyPrice) {
    if (value == null || value.trim().isEmpty) {
      return null; // Weekend price is optional
    }
    
    final price = double.tryParse(value);
    if (price == null) {
      return 'يرجى إدخال سعر صحيح';
    }
    
    if (price < minDailyPrice) {
      return 'سعر العطلة يجب أن يكون $minDailyPrice ريال على الأقل';
    }
    
    if (price > maxDailyPrice) {
      return 'سعر العطلة يجب أن يكون $maxDailyPrice ريال على الأكثر';
    }
    
    // Weekend price should typically be higher than daily price
    if (dailyPrice != null && price < dailyPrice) {
      return 'سعر العطلة عادة ما يكون أعلى من السعر اليومي';
    }
    
    return null;
  }

  /// Validates weekly price
  static String? validateWeeklyPrice(String? value, double? dailyPrice) {
    if (value == null || value.trim().isEmpty) {
      return null; // Weekly price is optional
    }
    
    final price = double.tryParse(value);
    if (price == null) {
      return 'يرجى إدخال سعر صحيح';
    }
    
    if (price < minWeeklyPrice) {
      return 'السعر الأسبوعي يجب أن يكون $minWeeklyPrice ريال على الأقل';
    }
    
    if (price > maxWeeklyPrice) {
      return 'السعر الأسبوعي يجب أن يكون $maxWeeklyPrice ريال على الأكثر';
    }
    
    // Weekly price should be less than 7 times daily price (discount expected)
    if (dailyPrice != null && price > (dailyPrice * 7)) {
      return 'السعر الأسبوعي عادة ما يكون أقل من 7 أضعاف السعر اليومي';
    }
    
    return null;
  }

  /// Validates monthly price
  static String? validateMonthlyPrice(String? value, double? dailyPrice) {
    if (value == null || value.trim().isEmpty) {
      return null; // Monthly price is optional
    }
    
    final price = double.tryParse(value);
    if (price == null) {
      return 'يرجى إدخال سعر صحيح';
    }
    
    if (price < minMonthlyPrice) {
      return 'السعر الشهري يجب أن يكون $minMonthlyPrice ريال على الأقل';
    }
    
    if (price > maxMonthlyPrice) {
      return 'السعر الشهري يجب أن يكون $maxMonthlyPrice ريال على الأكثر';
    }
    
    // Monthly price should be less than 30 times daily price (discount expected)
    if (dailyPrice != null && price > (dailyPrice * 30)) {
      return 'السعر الشهري عادة ما يكون أقل من 30 ضعف السعر اليومي';
    }
    
    return null;
  }

  /// Validates number of guests
  static String? validateGuests(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'يرجى إدخال عدد الضيوف';
    }
    
    final guests = int.tryParse(value);
    if (guests == null) {
      return 'يرجى إدخال رقم صحيح';
    }
    
    if (guests < minGuests) {
      return 'عدد الضيوف يجب أن يكون $minGuests على الأقل';
    }
    
    if (guests > maxGuests) {
      return 'عدد الضيوف يجب أن يكون $maxGuests على الأكثر';
    }
    
    return null;
  }

  /// Validates number of bedrooms
  static String? validateBedrooms(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'يرجى إدخال عدد غرف النوم';
    }
    
    final beds = int.tryParse(value);
    if (beds == null) {
      return 'يرجى إدخال رقم صحيح';
    }
    
    if (beds < minBeds) {
      return 'عدد غرف النوم يجب أن يكون $minBeds على الأقل';
    }
    
    if (beds > maxBeds) {
      return 'عدد غرف النوم يجب أن يكون $maxBeds على الأكثر';
    }
    
    return null;
  }

  /// Validates number of bathrooms
  static String? validateBathrooms(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'يرجى إدخال عدد الحمامات';
    }
    
    final baths = int.tryParse(value);
    if (baths == null) {
      return 'يرجى إدخال رقم صحيح';
    }
    
    if (baths < minBaths) {
      return 'عدد الحمامات يجب أن يكون $minBaths على الأقل';
    }
    
    if (baths > maxBaths) {
      return 'عدد الحمامات يجب أن يكون $maxBaths على الأكثر';
    }
    
    return null;
  }

  /// Validates image file size
  static String? validateImageSize(File? image) {
    if (image == null) return null;
    
    final sizeInBytes = image.lengthSync();
    final sizeInMB = sizeInBytes / (1024 * 1024);
    
    if (sizeInMB > maxImageSizeMB) {
      return 'حجم الصورة يجب أن يكون أقل من $maxImageSizeMB ميجابايت';
    }
    
    return null;
  }

  /// Validates video file size
  static String? validateVideoSize(File? video) {
    if (video == null) return null;
    
    final sizeInBytes = video.lengthSync();
    final sizeInMB = sizeInBytes / (1024 * 1024);
    
    if (sizeInMB > maxVideoSizeMB) {
      return 'حجم الفيديو يجب أن يكون أقل من $maxVideoSizeMB ميجابايت';
    }
    
    return null;
  }

  /// Validates gallery images count
  static String? validateGalleryCount(List<File> images) {
    if (images.length > maxGalleryImages) {
      return 'يمكنك إضافة $maxGalleryImages صورة على الأكثر';
    }
    
    return null;
  }

  /// Validates that guests can be accommodated by bedrooms
  static String? validateGuestsVsBedrooms(int? guests, int? bedrooms) {
    if (guests == null || bedrooms == null) return null;
    
    // Rough estimate: 2 guests per bedroom + 2 extra for living room
    final maxRecommendedGuests = (bedrooms * 2) + 2;
    
    if (guests > maxRecommendedGuests) {
      return 'عدد الضيوف كبير مقارنة بعدد غرف النوم المتاحة';
    }
    
    return null;
  }

  /// Validates location coordinates
  static String? validateLocation(double? latitude, double? longitude) {
    if (latitude == null || longitude == null) {
      return 'يرجى تحديد موقع العقار';
    }
    
    // Basic coordinate validation for Saudi Arabia region
    if (latitude < 16.0 || latitude > 33.0) {
      return 'الموقع المحدد خارج نطاق المملكة العربية السعودية';
    }
    
    if (longitude < 34.0 || longitude > 56.0) {
      return 'الموقع المحدد خارج نطاق المملكة العربية السعودية';
    }
    
    return null;
  }
}
