import 'package:flutter/material.dart';
import 'package:gather_point/feature/host/data/models/my_listing_model.dart';
import 'package:gather_point/feature/host/routes/host_routes.dart';

class HostNavigationHelpers {
  /// Navigate to My Listings page
  static Future<void> goToMyListings(BuildContext context) {
    return HostRoutes.navigateToMyListings(context);
  }

  /// Navigate to Edit Property page
  static Future<bool?> editProperty(
    BuildContext context, {
    required int propertyId,
    MyListingModel? propertyData,
    bool waitForResult = false,
  }) {
    if (waitForResult) {
      return HostRoutes.navigateToEditPropertyAndWaitForResult(
        context,
        propertyId: propertyId,
        propertyData: propertyData,
      );
    } else {
      return HostRoutes.navigateToEditProperty(
        context,
        propertyId: propertyId,
        propertyData: propertyData,
      ).then((_) => null);
    }
  }

  /// Navigate to Property Analytics page
  static Future<void> viewPropertyAnalytics(
    BuildContext context, {
    required int propertyId,
    String? propertyTitle,
  }) {
    return HostRoutes.navigateToPropertyAnalytics(
      context,
      propertyId: propertyId,
      propertyTitle: propertyTitle,
    );
  }

  /// Show property action bottom sheet
  static void showPropertyActions(
    BuildContext context, {
    required MyListingModel property,
    VoidCallback? onEdit,
    VoidCallback? onAnalytics,
    VoidCallback? onDelete,
    VoidCallback? onDuplicate,
    VoidCallback? onToggleStatus,
  }) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => PropertyActionsBottomSheet(
        property: property,
        onEdit: onEdit ?? () => editProperty(context, propertyId: property.id, propertyData: property),
        onAnalytics: onAnalytics ?? () => viewPropertyAnalytics(context, propertyId: property.id, propertyTitle: property.title),
        onDelete: onDelete,
        onDuplicate: onDuplicate,
        onToggleStatus: onToggleStatus,
      ),
    );
  }

  /// Show confirmation dialog
  static Future<bool> showConfirmationDialog(
    BuildContext context, {
    required String title,
    required String message,
    String? confirmText,
    String? cancelText,
    Color? confirmColor,
  }) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(cancelText ?? 'إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: confirmColor ?? const Color(0xFFFEC53A),
              foregroundColor: Colors.black,
            ),
            child: Text(confirmText ?? 'تأكيد'),
          ),
        ],
      ),
    );
    return result ?? false;
  }

  /// Show success snackbar
  static void showSuccessMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// Show error snackbar
  static void showErrorMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// Show info snackbar
  static void showInfoMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.blue,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}

class PropertyActionsBottomSheet extends StatelessWidget {
  final MyListingModel property;
  final VoidCallback onEdit;
  final VoidCallback onAnalytics;
  final VoidCallback? onDelete;
  final VoidCallback? onDuplicate;
  final VoidCallback? onToggleStatus;

  const PropertyActionsBottomSheet({
    super.key,
    required this.property,
    required this.onEdit,
    required this.onAnalytics,
    this.onDelete,
    this.onDuplicate,
    this.onToggleStatus,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 16),

          // Property Info
          Row(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.network(
                  property.mainImageUrl ?? '',
                  width: 60,
                  height: 60,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) => Container(
                    width: 60,
                    height: 60,
                    color: Colors.grey[300],
                    child: const Icon(Icons.image),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      property.title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      property.statusDisplayName,
                      style: TextStyle(
                        fontSize: 12,
                        color: _getStatusColor(property.status),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Actions
          _buildActionTile(
            icon: Icons.edit,
            title: 'تعديل العقار',
            onTap: () {
              Navigator.pop(context);
              onEdit();
            },
          ),
          _buildActionTile(
            icon: Icons.analytics,
            title: 'عرض التحليلات',
            onTap: () {
              Navigator.pop(context);
              onAnalytics();
            },
          ),
          if (onToggleStatus != null)
            _buildActionTile(
              icon: property.status == 'active' ? Icons.visibility_off : Icons.visibility,
              title: property.status == 'active' ? 'إلغاء تفعيل' : 'تفعيل',
              onTap: () {
                Navigator.pop(context);
                onToggleStatus!();
              },
            ),
          if (onDuplicate != null)
            _buildActionTile(
              icon: Icons.copy,
              title: 'نسخ العقار',
              onTap: () {
                Navigator.pop(context);
                onDuplicate!();
              },
            ),
          if (onDelete != null)
            _buildActionTile(
              icon: Icons.delete,
              title: 'حذف العقار',
              color: Colors.red,
              onTap: () {
                Navigator.pop(context);
                onDelete!();
              },
            ),

          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildActionTile({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    Color? color,
  }) {
    return ListTile(
      leading: Icon(icon, color: color),
      title: Text(
        title,
        style: TextStyle(color: color),
      ),
      onTap: onTap,
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'active':
        return Colors.green;
      case 'inactive':
        return Colors.orange;
      case 'draft':
        return Colors.grey;
      case 'pending':
        return Colors.blue;
      case 'suspended':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}
