import 'package:gather_point/feature/host/data/services/my_listings_api_service.dart';
import 'package:gather_point/feature/host/data/models/my_listing_model.dart';
import 'package:gather_point/feature/host/data/models/listing_stats_model.dart';
import 'package:gather_point/feature/home/<USER>/Data%20Sources/Models/ServiceCategory.dart';

/// Mock implementation of MyListingsApiService for development/testing
class MockMyListingsApiService implements MyListingsApiService {
  static List<MyListingModel> _mockListings = [];
  static ListingStatsModel? _mockStats;

  @override
  Future<List<MyListingModel>> getMyListings({
    int page = 1,
    int limit = 20,
    String? status,
    String? category,
    String? sortBy,
    String? sortOrder = 'desc',
    String? search,
  }) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));

    if (_mockListings.isEmpty) {
      _initializeMockData();
    }

    var filteredListings = List<MyListingModel>.from(_mockListings);

    // Apply filters
    if (status != null) {
      filteredListings = filteredListings.where((listing) => listing.status == status).toList();
    }

    if (search != null && search.isNotEmpty) {
      filteredListings = filteredListings.where((listing) =>
          listing.title.toLowerCase().contains(search.toLowerCase()) ||
          listing.content.toLowerCase().contains(search.toLowerCase())).toList();
    }

    // Apply sorting
    if (sortBy != null) {
      switch (sortBy) {
        case 'created_at':
          filteredListings.sort((a, b) => sortOrder == 'asc' 
              ? a.createdAt.compareTo(b.createdAt)
              : b.createdAt.compareTo(a.createdAt));
          break;
        case 'price':
          filteredListings.sort((a, b) => sortOrder == 'asc'
              ? a.price.compareTo(b.price)
              : b.price.compareTo(a.price));
          break;
        case 'title':
          filteredListings.sort((a, b) => sortOrder == 'asc'
              ? a.title.compareTo(b.title)
              : b.title.compareTo(a.title));
          break;
      }
    }

    // Apply pagination
    final startIndex = (page - 1) * limit;
    final endIndex = startIndex + limit;
    
    if (startIndex >= filteredListings.length) {
      return [];
    }

    return filteredListings.sublist(
      startIndex,
      endIndex > filteredListings.length ? filteredListings.length : endIndex,
    );
  }

  @override
  Future<ListingStatsModel> getListingStats() async {
    await Future.delayed(const Duration(milliseconds: 300));

    if (_mockStats == null) {
      _initializeMockStats();
    }

    return _mockStats!;
  }

  @override
  Future<bool> toggleListingStatus(int listingId) async {
    await Future.delayed(const Duration(milliseconds: 300));

    final index = _mockListings.indexWhere((listing) => listing.id == listingId);
    if (index != -1) {
      final listing = _mockListings[index];
      final newStatus = listing.status == 'active' ? 'inactive' : 'active';
      _mockListings[index] = listing.copyWith(status: newStatus);
      _updateMockStats();
      return true;
    }
    return false;
  }

  @override
  Future<bool> deleteListing(int listingId) async {
    await Future.delayed(const Duration(milliseconds: 300));

    final initialLength = _mockListings.length;
    _mockListings.removeWhere((listing) => listing.id == listingId);
    
    if (_mockListings.length < initialLength) {
      _updateMockStats();
      return true;
    }
    return false;
  }

  @override
  Future<MyListingModel> duplicateListing(int listingId) async {
    await Future.delayed(const Duration(milliseconds: 500));

    final originalListing = _mockListings.firstWhere((listing) => listing.id == listingId);
    final duplicatedListing = originalListing.copyWith(
      id: DateTime.now().millisecondsSinceEpoch,
      title: '${originalListing.title} (Copy)',
      status: 'draft',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    _mockListings.insert(0, duplicatedListing);
    _updateMockStats();
    return duplicatedListing;
  }

  @override
  Future<bool> bulkUpdateStatus(List<int> listingIds, String status) async {
    await Future.delayed(const Duration(milliseconds: 500));

    for (final id in listingIds) {
      final index = _mockListings.indexWhere((listing) => listing.id == id);
      if (index != -1) {
        _mockListings[index] = _mockListings[index].copyWith(status: status);
      }
    }

    _updateMockStats();
    return true;
  }

  @override
  Future<bool> bulkDeleteListings(List<int> listingIds) async {
    await Future.delayed(const Duration(milliseconds: 500));

    _mockListings.removeWhere((listing) => listingIds.contains(listing.id));
    _updateMockStats();
    return true;
  }

  @override
  Future<List<dynamic>> getListingReservations(
    int listingId, {
    String? status,
    int page = 1,
    int limit = 20,
  }) async {
    await Future.delayed(const Duration(milliseconds: 300));
    return []; // Return empty list for mock
  }

  @override
  Future<Map<String, dynamic>> getListingAnalytics(
    int listingId, {
    String? period = 'month',
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    await Future.delayed(const Duration(milliseconds: 300));
    return {
      'views': 45,
      'bookings': 3,
      'revenue': 450.0,
      'rating': 4.5,
      'occupancy_rate': 0.75,
    };
  }

  @override
  Future<bool> updateListingQuickSettings(
    int listingId, {
    double? price,
    double? weekendPrice,
    double? weeklyPrice,
    double? monthlyPrice,
    bool? isAvailable,
    int? minStay,
    int? maxStay,
  }) async {
    await Future.delayed(const Duration(milliseconds: 300));
    return true;
  }

  @override
  Future<Map<String, dynamic>> getListingCalendar(
    int listingId, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    await Future.delayed(const Duration(milliseconds: 300));
    return {
      'available_dates': [],
      'blocked_dates': [],
      'booked_dates': [],
    };
  }

  @override
  Future<bool> updateListingAvailability(
    int listingId, {
    required List<String> dates,
    required bool isAvailable,
    double? customPrice,
  }) async {
    await Future.delayed(const Duration(milliseconds: 300));
    return true;
  }

  @override
  Future<Map<String, dynamic>> getListingReviews(
    int listingId, {
    int page = 1,
    int limit = 20,
    String? sortBy = 'created_at',
    String? sortOrder = 'desc',
  }) async {
    await Future.delayed(const Duration(milliseconds: 300));
    return {
      'reviews': [],
      'total': 0,
      'average_rating': 0.0,
    };
  }

  static void _initializeMockData() {
    _mockListings = [
      MyListingModel(
        id: 1,
        title: 'Beautiful Apartment in Riyadh',
        content: 'A stunning 2-bedroom apartment with modern amenities',
        price: 150.0,
        status: 'active',
        isAvailable: true,
        views: 45,
        bookings: 3,
        rating: 4.5,
        reviewCount: 8,
        mainImageUrl: 'https://via.placeholder.com/300x200',
        galleryImages: ['https://via.placeholder.com/300x200'],
        category: ServiceCategory(id: 1, title: 'Apartments', icon: '', image: '', order: 1),
        noGuests: 4,
        beds: 2,
        baths: 2,
        address: 'Riyadh, Saudi Arabia',
        lat: 24.7136,
        lon: 46.6753,
        createdAt: DateTime.now().subtract(const Duration(days: 10)),
        updatedAt: DateTime.now().subtract(const Duration(days: 2)),
        facilities: ['wifi', 'parking', 'pool'],
        hasActiveReservations: true,
        pendingReservations: 1,
      ),
      MyListingModel(
        id: 2,
        title: 'Luxury Villa with Pool',
        content: 'Spacious villa perfect for families',
        price: 300.0,
        status: 'active',
        isAvailable: true,
        views: 78,
        bookings: 5,
        rating: 4.8,
        reviewCount: 12,
        mainImageUrl: 'https://via.placeholder.com/300x200',
        galleryImages: ['https://via.placeholder.com/300x200'],
        category: ServiceCategory(id: 2, title: 'Villas', icon: '', image: '', order: 2),
        noGuests: 8,
        beds: 4,
        baths: 3,
        address: 'Riyadh, Saudi Arabia',
        lat: 24.7136,
        lon: 46.6753,
        createdAt: DateTime.now().subtract(const Duration(days: 5)),
        updatedAt: DateTime.now().subtract(const Duration(days: 1)),
        facilities: ['wifi', 'parking', 'pool', 'gym'],
        hasActiveReservations: false,
        pendingReservations: 0,
      ),
      MyListingModel(
        id: 3,
        title: 'Cozy Studio Downtown',
        content: 'Perfect for business travelers',
        price: 80.0,
        status: 'inactive',
        isAvailable: false,
        views: 23,
        bookings: 1,
        rating: 4.2,
        reviewCount: 3,
        mainImageUrl: 'https://via.placeholder.com/300x200',
        galleryImages: ['https://via.placeholder.com/300x200'],
        category: ServiceCategory(id: 3, title: 'Studios', icon: '', image: '', order: 3),
        noGuests: 2,
        beds: 1,
        baths: 1,
        address: 'Riyadh, Saudi Arabia',
        lat: 24.7136,
        lon: 46.6753,
        createdAt: DateTime.now().subtract(const Duration(days: 15)),
        updatedAt: DateTime.now().subtract(const Duration(days: 3)),
        facilities: ['wifi', 'ac'],
        hasActiveReservations: false,
        pendingReservations: 0,
      ),
    ];
  }

  static void _initializeMockStats() {
    _updateMockStats();
  }

  static void _updateMockStats() {
    final activeListings = _mockListings.where((l) => l.status == 'active').length;
    final inactiveListings = _mockListings.where((l) => l.status == 'inactive').length;
    final draftListings = _mockListings.where((l) => l.status == 'draft').length;
    final totalViews = _mockListings.fold(0, (sum, l) => sum + l.views);
    final totalBookings = _mockListings.fold(0, (sum, l) => sum + l.bookings);
    final totalRevenue = _mockListings.fold(0.0, (sum, l) => sum + (l.price * l.bookings));

    _mockStats = ListingStatsModel(
      totalListings: _mockListings.length,
      activeListings: activeListings,
      inactiveListings: inactiveListings,
      draftListings: draftListings,
      pendingListings: 0,
      suspendedListings: 0,
      totalViews: totalViews,
      totalBookings: totalBookings,
      totalRevenue: totalRevenue,
      averageRating: 4.5,
      totalReviews: 23,
      pendingReservations: 1,
      activeReservations: 4,
      occupancyRate: 0.75,
      averageDailyRate: 176.67,
      conversionRate: 0.15,
      categoryBreakdown: {
        'Apartments': 1,
        'Villas': 1,
        'Studios': 1,
      },
      monthlyRevenue: {
        'Jan': 1200.0,
        'Feb': 1500.0,
        'Mar': 1800.0,
      },
      monthlyBookings: {
        'Jan': 8,
        'Feb': 10,
        'Mar': 12,
      },
      topPerformingListings: [],
    );
  }

  /// Add a new listing to mock data
  static void addMockListing(MyListingModel listing) {
    _mockListings.insert(0, listing);
    _updateMockStats();
  }

  /// Clear all mock data
  static void clearMockData() {
    _mockListings.clear();
    _mockStats = null;
  }
}
