import 'package:dio/dio.dart';
import 'package:gather_point/core/databases/api/end_points.dart';
import 'package:gather_point/core/databases/api/api_consumer.dart';
import 'package:gather_point/feature/host/data/models/my_listing_model.dart';
import 'package:gather_point/feature/host/data/models/listing_stats_model.dart';

class MyListingsApiService {
  final ApiConsumer _apiConsumer;

  MyListingsApiService(this._apiConsumer);

  /// Fetch host's listings with optional filters
  Future<List<MyListingModel>> getMyListings({
    int page = 1,
    int limit = 20,
    String? status, // 'active', 'inactive', 'draft', 'pending'
    String? category,
    String? sortBy, // 'created_at', 'updated_at', 'price', 'title'
    String? sortOrder = 'desc', // 'asc', 'desc'
    String? search,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
        'sort_by': sortBy ?? 'created_at',
        'sort_order': sortOrder,
      };

      if (status != null) queryParams['status'] = status;
      if (category != null) queryParams['category'] = category;
      if (search != null && search.isNotEmpty) queryParams['search'] = search;

      final response = await _apiConsumer.get(
        EndPoints.hostListings,
        queryParameters: queryParams,
      );

      if (response['success'] == true && response['data'] != null) {
        final List<dynamic> listingsData = response['data']['listings'] ?? response['data'];
        return listingsData.map((json) => MyListingModel.fromJson(json)).toList();
      }

      return [];
    } catch (e) {
      throw Exception('Failed to fetch listings: $e');
    }
  }

  /// Get listing statistics for the host
  Future<ListingStatsModel> getListingStats() async {
    try {
      final response = await _apiConsumer.get(EndPoints.hostListingsStats);

      if (response['success'] == true && response['data'] != null) {
        return ListingStatsModel.fromJson(response['data']);
      }

      throw Exception('Failed to get listing stats');
    } catch (e) {
      throw Exception('Failed to fetch listing stats: $e');
    }
  }

  /// Toggle listing status (active/inactive)
  Future<bool> toggleListingStatus(int listingId) async {
    try {
      final response = await _apiConsumer.post(
        '${EndPoints.hostListingToggleStatus}/$listingId',
      );

      return response['success'] == true;
    } catch (e) {
      throw Exception('Failed to toggle listing status: $e');
    }
  }

  /// Delete a listing
  Future<bool> deleteListing(int listingId) async {
    try {
      final response = await _apiConsumer.delete(
        '${EndPoints.hostListingDelete}/$listingId',
      );

      return response['success'] == true;
    } catch (e) {
      throw Exception('Failed to delete listing: $e');
    }
  }

  /// Duplicate a listing
  Future<MyListingModel> duplicateListing(int listingId) async {
    try {
      final response = await _apiConsumer.post(
        '${EndPoints.hostListingDuplicate}/$listingId',
      );

      if (response['success'] == true && response['data'] != null) {
        return MyListingModel.fromJson(response['data']);
      }

      throw Exception('Failed to duplicate listing');
    } catch (e) {
      throw Exception('Failed to duplicate listing: $e');
    }
  }

  /// Bulk update listing status
  Future<bool> bulkUpdateStatus(List<int> listingIds, String status) async {
    try {
      final response = await _apiConsumer.post(
        '${EndPoints.hostListings}/bulk-status',
        data: {
          'listing_ids': listingIds,
          'status': status,
        },
      );

      return response['success'] == true;
    } catch (e) {
      throw Exception('Failed to bulk update status: $e');
    }
  }

  /// Bulk delete listings
  Future<bool> bulkDeleteListings(List<int> listingIds) async {
    try {
      final response = await _apiConsumer.post(
        '${EndPoints.hostListings}/bulk-delete',
        data: {
          'listing_ids': listingIds,
        },
      );

      return response['success'] == true;
    } catch (e) {
      throw Exception('Failed to bulk delete listings: $e');
    }
  }

  /// Get listing performance analytics
  Future<Map<String, dynamic>> getListingAnalytics(
    int listingId, {
    String period = '30d', // '7d', '30d', '90d', '1y'
  }) async {
    try {
      final response = await _apiConsumer.get(
        '${EndPoints.hostListings}/$listingId/analytics',
        queryParameters: {
          'period': period,
        },
      );

      if (response['success'] == true && response['data'] != null) {
        return response['data'];
      }

      return {};
    } catch (e) {
      throw Exception('Failed to fetch listing analytics: $e');
    }
  }

  /// Update listing quick settings (price, availability, etc.)
  Future<bool> updateListingQuickSettings(
    int listingId, {
    double? price,
    double? weekendPrice,
    double? weeklyPrice,
    double? monthlyPrice,
    bool? isAvailable,
    int? minStay,
    int? maxStay,
  }) async {
    try {
      final data = <String, dynamic>{};
      
      if (price != null) data['price'] = price;
      if (weekendPrice != null) data['weekend_price'] = weekendPrice;
      if (weeklyPrice != null) data['weekly_price'] = weeklyPrice;
      if (monthlyPrice != null) data['monthly_price'] = monthlyPrice;
      if (isAvailable != null) data['is_available'] = isAvailable;
      if (minStay != null) data['min_stay'] = minStay;
      if (maxStay != null) data['max_stay'] = maxStay;

      final response = await _apiConsumer.patch(
        '${EndPoints.hostListings}/$listingId/quick-settings',
        data: data,
      );

      return response['success'] == true;
    } catch (e) {
      throw Exception('Failed to update listing settings: $e');
    }
  }

  /// Get listing booking calendar
  Future<Map<String, dynamic>> getListingCalendar(
    int listingId, {
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      final response = await _apiConsumer.get(
        '${EndPoints.hostListings}/$listingId/calendar',
        queryParameters: {
          'start_date': startDate.toIso8601String().split('T')[0],
          'end_date': endDate.toIso8601String().split('T')[0],
        },
      );

      if (response['success'] == true && response['data'] != null) {
        return response['data'];
      }

      return {};
    } catch (e) {
      throw Exception('Failed to fetch listing calendar: $e');
    }
  }

  /// Update listing availability for specific dates
  Future<bool> updateListingAvailability(
    int listingId, {
    required List<String> dates, // Format: 'YYYY-MM-DD'
    required bool isAvailable,
    double? customPrice,
  }) async {
    try {
      final response = await _apiConsumer.post(
        '${EndPoints.hostListings}/$listingId/availability',
        data: {
          'dates': dates,
          'is_available': isAvailable,
          if (customPrice != null) 'custom_price': customPrice,
        },
      );

      return response['success'] == true;
    } catch (e) {
      throw Exception('Failed to update listing availability: $e');
    }
  }

  /// Get listing reviews summary
  Future<Map<String, dynamic>> getListingReviews(
    int listingId, {
    int page = 1,
    int limit = 10,
  }) async {
    try {
      final response = await _apiConsumer.get(
        '${EndPoints.hostListings}/$listingId/reviews',
        queryParameters: {
          'page': page,
          'limit': limit,
        },
      );

      if (response['success'] == true && response['data'] != null) {
        return response['data'];
      }

      return {};
    } catch (e) {
      throw Exception('Failed to fetch listing reviews: $e');
    }
  }

  /// Get listing reservations
  Future<List<dynamic>> getListingReservations(
    int listingId, {
    String? status, // 'pending', 'confirmed', 'cancelled', 'completed'
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };

      if (status != null) queryParams['status'] = status;

      final response = await _apiConsumer.get(
        '${EndPoints.hostListings}/$listingId/reservations',
        queryParameters: queryParams,
      );

      if (response['success'] == true && response['data'] != null) {
        return response['data']['reservations'] ?? response['data'];
      }

      return [];
    } catch (e) {
      throw Exception('Failed to fetch listing reservations: $e');
    }
  }
}
