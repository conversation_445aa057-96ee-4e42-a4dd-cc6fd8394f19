class ListingFilterModel {
  final String? status;
  final String? category;
  final String? sortBy;
  final String? sortOrder;
  final String? search;
  final double? minPrice;
  final double? maxPrice;
  final int? minGuests;
  final int? maxGuests;
  final DateTime? createdAfter;
  final DateTime? createdBefore;
  final bool? hasBookings;
  final bool? hasReviews;
  final double? minRating;
  final List<String>? propertyTypes;
  final List<String>? facilities;

  ListingFilterModel({
    this.status,
    this.category,
    this.sortBy,
    this.sortOrder,
    this.search,
    this.minPrice,
    this.maxPrice,
    this.minGuests,
    this.maxGuests,
    this.createdAfter,
    this.createdBefore,
    this.hasBookings,
    this.hasReviews,
    this.minRating,
    this.propertyTypes,
    this.facilities,
  });

  factory ListingFilterModel.fromJson(Map<String, dynamic> json) {
    return ListingFilterModel(
      status: json['status'],
      category: json['category'],
      sortBy: json['sort_by'],
      sortOrder: json['sort_order'],
      search: json['search'],
      minPrice: json['min_price']?.toDouble(),
      maxPrice: json['max_price']?.toDouble(),
      minGuests: json['min_guests'],
      maxGuests: json['max_guests'],
      createdAfter: json['created_after'] != null 
          ? DateTime.parse(json['created_after'])
          : null,
      createdBefore: json['created_before'] != null 
          ? DateTime.parse(json['created_before'])
          : null,
      hasBookings: json['has_bookings'],
      hasReviews: json['has_reviews'],
      minRating: json['min_rating']?.toDouble(),
      propertyTypes: json['property_types'] != null 
          ? List<String>.from(json['property_types'])
          : null,
      facilities: json['facilities'] != null 
          ? List<String>.from(json['facilities'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    
    if (status != null) data['status'] = status;
    if (category != null) data['category'] = category;
    if (sortBy != null) data['sort_by'] = sortBy;
    if (sortOrder != null) data['sort_order'] = sortOrder;
    if (search != null && search!.isNotEmpty) data['search'] = search;
    if (minPrice != null) data['min_price'] = minPrice;
    if (maxPrice != null) data['max_price'] = maxPrice;
    if (minGuests != null) data['min_guests'] = minGuests;
    if (maxGuests != null) data['max_guests'] = maxGuests;
    if (createdAfter != null) data['created_after'] = createdAfter!.toIso8601String();
    if (createdBefore != null) data['created_before'] = createdBefore!.toIso8601String();
    if (hasBookings != null) data['has_bookings'] = hasBookings;
    if (hasReviews != null) data['has_reviews'] = hasReviews;
    if (minRating != null) data['min_rating'] = minRating;
    if (propertyTypes != null && propertyTypes!.isNotEmpty) data['property_types'] = propertyTypes;
    if (facilities != null && facilities!.isNotEmpty) data['facilities'] = facilities;
    
    return data;
  }

  // Predefined filter presets
  static ListingFilterModel get allListings => ListingFilterModel();
  
  static ListingFilterModel get activeListings => ListingFilterModel(
    status: 'active',
    sortBy: 'updated_at',
    sortOrder: 'desc',
  );
  
  static ListingFilterModel get inactiveListings => ListingFilterModel(
    status: 'inactive',
    sortBy: 'updated_at',
    sortOrder: 'desc',
  );
  
  static ListingFilterModel get draftListings => ListingFilterModel(
    status: 'draft',
    sortBy: 'created_at',
    sortOrder: 'desc',
  );
  
  static ListingFilterModel get pendingListings => ListingFilterModel(
    status: 'pending',
    sortBy: 'created_at',
    sortOrder: 'desc',
  );
  
  static ListingFilterModel get topPerforming => ListingFilterModel(
    status: 'active',
    hasBookings: true,
    sortBy: 'revenue',
    sortOrder: 'desc',
  );
  
  static ListingFilterModel get needsAttention => ListingFilterModel(
    status: 'active',
    hasBookings: false,
    sortBy: 'created_at',
    sortOrder: 'asc',
  );
  
  static ListingFilterModel get recentlyCreated => ListingFilterModel(
    sortBy: 'created_at',
    sortOrder: 'desc',
  );
  
  static ListingFilterModel get highRated => ListingFilterModel(
    status: 'active',
    hasReviews: true,
    minRating: 4.0,
    sortBy: 'rating',
    sortOrder: 'desc',
  );

  // Helper methods
  bool get hasActiveFilters {
    return status != null ||
           category != null ||
           search != null ||
           minPrice != null ||
           maxPrice != null ||
           minGuests != null ||
           maxGuests != null ||
           createdAfter != null ||
           createdBefore != null ||
           hasBookings != null ||
           hasReviews != null ||
           minRating != null ||
           (propertyTypes != null && propertyTypes!.isNotEmpty) ||
           (facilities != null && facilities!.isNotEmpty);
  }

  int get activeFilterCount {
    int count = 0;
    if (status != null) count++;
    if (category != null) count++;
    if (search != null && search!.isNotEmpty) count++;
    if (minPrice != null || maxPrice != null) count++;
    if (minGuests != null || maxGuests != null) count++;
    if (createdAfter != null || createdBefore != null) count++;
    if (hasBookings != null) count++;
    if (hasReviews != null) count++;
    if (minRating != null) count++;
    if (propertyTypes != null && propertyTypes!.isNotEmpty) count++;
    if (facilities != null && facilities!.isNotEmpty) count++;
    return count;
  }

  String get displayName {
    if (!hasActiveFilters) return 'جميع العقارات';
    
    switch (status) {
      case 'active':
        return 'العقارات النشطة';
      case 'inactive':
        return 'العقارات غير النشطة';
      case 'draft':
        return 'المسودات';
      case 'pending':
        return 'قيد المراجعة';
      default:
        if (search != null && search!.isNotEmpty) {
          return 'البحث: $search';
        }
        return 'مفلتر ($activeFilterCount)';
    }
  }

  // Copy with method
  ListingFilterModel copyWith({
    String? status,
    String? category,
    String? sortBy,
    String? sortOrder,
    String? search,
    double? minPrice,
    double? maxPrice,
    int? minGuests,
    int? maxGuests,
    DateTime? createdAfter,
    DateTime? createdBefore,
    bool? hasBookings,
    bool? hasReviews,
    double? minRating,
    List<String>? propertyTypes,
    List<String>? facilities,
    bool clearStatus = false,
    bool clearCategory = false,
    bool clearSearch = false,
    bool clearPriceRange = false,
    bool clearGuestRange = false,
    bool clearDateRange = false,
    bool clearBookingFilter = false,
    bool clearReviewFilter = false,
    bool clearRatingFilter = false,
    bool clearPropertyTypes = false,
    bool clearFacilities = false,
  }) {
    return ListingFilterModel(
      status: clearStatus ? null : (status ?? this.status),
      category: clearCategory ? null : (category ?? this.category),
      sortBy: sortBy ?? this.sortBy,
      sortOrder: sortOrder ?? this.sortOrder,
      search: clearSearch ? null : (search ?? this.search),
      minPrice: clearPriceRange ? null : (minPrice ?? this.minPrice),
      maxPrice: clearPriceRange ? null : (maxPrice ?? this.maxPrice),
      minGuests: clearGuestRange ? null : (minGuests ?? this.minGuests),
      maxGuests: clearGuestRange ? null : (maxGuests ?? this.maxGuests),
      createdAfter: clearDateRange ? null : (createdAfter ?? this.createdAfter),
      createdBefore: clearDateRange ? null : (createdBefore ?? this.createdBefore),
      hasBookings: clearBookingFilter ? null : (hasBookings ?? this.hasBookings),
      hasReviews: clearReviewFilter ? null : (hasReviews ?? this.hasReviews),
      minRating: clearRatingFilter ? null : (minRating ?? this.minRating),
      propertyTypes: clearPropertyTypes ? null : (propertyTypes ?? this.propertyTypes),
      facilities: clearFacilities ? null : (facilities ?? this.facilities),
    );
  }

  // Clear all filters
  ListingFilterModel clearAll() {
    return ListingFilterModel(
      sortBy: sortBy,
      sortOrder: sortOrder,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ListingFilterModel &&
        other.status == status &&
        other.category == category &&
        other.sortBy == sortBy &&
        other.sortOrder == sortOrder &&
        other.search == search &&
        other.minPrice == minPrice &&
        other.maxPrice == maxPrice &&
        other.minGuests == minGuests &&
        other.maxGuests == maxGuests &&
        other.createdAfter == createdAfter &&
        other.createdBefore == createdBefore &&
        other.hasBookings == hasBookings &&
        other.hasReviews == hasReviews &&
        other.minRating == minRating;
  }

  @override
  int get hashCode {
    return Object.hash(
      status,
      category,
      sortBy,
      sortOrder,
      search,
      minPrice,
      maxPrice,
      minGuests,
      maxGuests,
      createdAfter,
      createdBefore,
      hasBookings,
      hasReviews,
      minRating,
    );
  }

  @override
  String toString() {
    return 'ListingFilterModel(status: $status, category: $category, search: $search, activeFilters: $activeFilterCount)';
  }
}
