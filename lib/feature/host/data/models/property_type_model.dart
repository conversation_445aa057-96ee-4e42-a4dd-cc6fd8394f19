class PropertyTypeModel {
  final int id;
  final String title;
  final int order;
  final String createdAt;

  const PropertyTypeModel({
    required this.id,
    required this.title,
    required this.order,
    required this.createdAt,
  });

  factory PropertyTypeModel.fromJson(Map<String, dynamic> json) {
    return PropertyTypeModel(
      id: json['id'] ?? 0,
      title: json['title'] ?? '',
      order: json['order'] ?? 0,
      createdAt: json['created_at'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'order': order,
      'created_at': createdAt,
    };
  }

  PropertyTypeModel copyWith({
    int? id,
    String? title,
    int? order,
    String? createdAt,
  }) {
    return PropertyTypeModel(
      id: id ?? this.id,
      title: title ?? this.title,
      order: order ?? this.order,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PropertyTypeModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'PropertyTypeModel(id: $id, title: $title, order: $order, createdAt: $createdAt)';
  }
}
