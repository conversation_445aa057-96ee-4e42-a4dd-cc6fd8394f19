class ListingAnalyticsModel {
  final int listingId;
  final String period; // '7d', '30d', '90d', '1y'
  final DateTime startDate;
  final DateTime endDate;
  
  // Core Metrics
  final int totalViews;
  final int uniqueViews;
  final int totalBookings;
  final int confirmedBookings;
  final int cancelledBookings;
  final double totalRevenue;
  final double netRevenue;
  final double averageDailyRate;
  final double occupancyRate;
  final double conversionRate;
  
  // Performance Metrics
  final double averageRating;
  final int totalReviews;
  final int responseTime; // in minutes
  final double responseRate; // percentage
  final int favoriteCount;
  final int shareCount;
  
  // Time Series Data
  final List<DailyMetric> dailyViews;
  final List<DailyMetric> dailyBookings;
  final List<DailyMetric> dailyRevenue;
  final List<MonthlyMetric> monthlyTrends;
  
  // Comparison Data
  final ComparisonMetrics? previousPeriod;
  final ComparisonMetrics? marketAverage;
  
  // Insights
  final List<PerformanceInsight> insights;
  final List<String> recommendations;

  ListingAnalyticsModel({
    required this.listingId,
    required this.period,
    required this.startDate,
    required this.endDate,
    required this.totalViews,
    required this.uniqueViews,
    required this.totalBookings,
    required this.confirmedBookings,
    required this.cancelledBookings,
    required this.totalRevenue,
    required this.netRevenue,
    required this.averageDailyRate,
    required this.occupancyRate,
    required this.conversionRate,
    required this.averageRating,
    required this.totalReviews,
    required this.responseTime,
    required this.responseRate,
    required this.favoriteCount,
    required this.shareCount,
    required this.dailyViews,
    required this.dailyBookings,
    required this.dailyRevenue,
    required this.monthlyTrends,
    this.previousPeriod,
    this.marketAverage,
    required this.insights,
    required this.recommendations,
  });

  factory ListingAnalyticsModel.fromJson(Map<String, dynamic> json) {
    return ListingAnalyticsModel(
      listingId: json['listing_id'] ?? 0,
      period: json['period'] ?? '30d',
      startDate: DateTime.parse(json['start_date'] ?? DateTime.now().toIso8601String()),
      endDate: DateTime.parse(json['end_date'] ?? DateTime.now().toIso8601String()),
      totalViews: json['total_views'] ?? 0,
      uniqueViews: json['unique_views'] ?? 0,
      totalBookings: json['total_bookings'] ?? 0,
      confirmedBookings: json['confirmed_bookings'] ?? 0,
      cancelledBookings: json['cancelled_bookings'] ?? 0,
      totalRevenue: (json['total_revenue'] ?? 0).toDouble(),
      netRevenue: (json['net_revenue'] ?? 0).toDouble(),
      averageDailyRate: (json['average_daily_rate'] ?? 0).toDouble(),
      occupancyRate: (json['occupancy_rate'] ?? 0).toDouble(),
      conversionRate: (json['conversion_rate'] ?? 0).toDouble(),
      averageRating: (json['average_rating'] ?? 0).toDouble(),
      totalReviews: json['total_reviews'] ?? 0,
      responseTime: json['response_time'] ?? 0,
      responseRate: (json['response_rate'] ?? 0).toDouble(),
      favoriteCount: json['favorite_count'] ?? 0,
      shareCount: json['share_count'] ?? 0,
      dailyViews: (json['daily_views'] as List<dynamic>? ?? [])
          .map((item) => DailyMetric.fromJson(item))
          .toList(),
      dailyBookings: (json['daily_bookings'] as List<dynamic>? ?? [])
          .map((item) => DailyMetric.fromJson(item))
          .toList(),
      dailyRevenue: (json['daily_revenue'] as List<dynamic>? ?? [])
          .map((item) => DailyMetric.fromJson(item))
          .toList(),
      monthlyTrends: (json['monthly_trends'] as List<dynamic>? ?? [])
          .map((item) => MonthlyMetric.fromJson(item))
          .toList(),
      previousPeriod: json['previous_period'] != null
          ? ComparisonMetrics.fromJson(json['previous_period'])
          : null,
      marketAverage: json['market_average'] != null
          ? ComparisonMetrics.fromJson(json['market_average'])
          : null,
      insights: (json['insights'] as List<dynamic>? ?? [])
          .map((item) => PerformanceInsight.fromJson(item))
          .toList(),
      recommendations: List<String>.from(json['recommendations'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'listing_id': listingId,
      'period': period,
      'start_date': startDate.toIso8601String(),
      'end_date': endDate.toIso8601String(),
      'total_views': totalViews,
      'unique_views': uniqueViews,
      'total_bookings': totalBookings,
      'confirmed_bookings': confirmedBookings,
      'cancelled_bookings': cancelledBookings,
      'total_revenue': totalRevenue,
      'net_revenue': netRevenue,
      'average_daily_rate': averageDailyRate,
      'occupancy_rate': occupancyRate,
      'conversion_rate': conversionRate,
      'average_rating': averageRating,
      'total_reviews': totalReviews,
      'response_time': responseTime,
      'response_rate': responseRate,
      'favorite_count': favoriteCount,
      'share_count': shareCount,
      'daily_views': dailyViews.map((item) => item.toJson()).toList(),
      'daily_bookings': dailyBookings.map((item) => item.toJson()).toList(),
      'daily_revenue': dailyRevenue.map((item) => item.toJson()).toList(),
      'monthly_trends': monthlyTrends.map((item) => item.toJson()).toList(),
      'previous_period': previousPeriod?.toJson(),
      'market_average': marketAverage?.toJson(),
      'insights': insights.map((item) => item.toJson()).toList(),
      'recommendations': recommendations,
    };
  }

  // Helper getters
  String get totalRevenueDisplay {
    if (totalRevenue >= 1000000) {
      return '${(totalRevenue / 1000000).toStringAsFixed(1)}م ر.س';
    } else if (totalRevenue >= 1000) {
      return '${(totalRevenue / 1000).toStringAsFixed(1)}ك ر.س';
    } else {
      return '${totalRevenue.toStringAsFixed(0)} ر.س';
    }
  }

  String get occupancyRateDisplay => '${(occupancyRate * 100).toStringAsFixed(1)}%';
  String get conversionRateDisplay => '${(conversionRate * 100).toStringAsFixed(1)}%';
  String get responseRateDisplay => '${responseRate.toStringAsFixed(1)}%';
  String get averageRatingDisplay => averageRating > 0 ? '${averageRating.toStringAsFixed(1)} ⭐' : 'لا توجد تقييمات';

  double get cancellationRate => totalBookings > 0 ? cancelledBookings / totalBookings : 0.0;
  String get cancellationRateDisplay => '${(cancellationRate * 100).toStringAsFixed(1)}%';

  String get responseTimeDisplay {
    if (responseTime < 60) {
      return '$responseTime دقيقة';
    } else if (responseTime < 1440) {
      return '${(responseTime / 60).toStringAsFixed(1)} ساعة';
    } else {
      return '${(responseTime / 1440).toStringAsFixed(1)} يوم';
    }
  }

  bool get hasGoodPerformance {
    return conversionRate >= 0.02 && // 2% conversion rate
           occupancyRate >= 0.6 && // 60% occupancy
           averageRating >= 4.0 && // 4+ star rating
           responseRate >= 0.9; // 90% response rate
  }

  String get performanceGrade {
    int score = 0;
    if (conversionRate >= 0.03) score += 25;
    else if (conversionRate >= 0.02) score += 20;
    else if (conversionRate >= 0.01) score += 15;

    if (occupancyRate >= 0.8) score += 25;
    else if (occupancyRate >= 0.6) score += 20;
    else if (occupancyRate >= 0.4) score += 15;

    if (averageRating >= 4.5) score += 25;
    else if (averageRating >= 4.0) score += 20;
    else if (averageRating >= 3.5) score += 15;

    if (responseRate >= 0.95) score += 25;
    else if (responseRate >= 0.9) score += 20;
    else if (responseRate >= 0.8) score += 15;

    if (score >= 90) return 'ممتاز';
    if (score >= 75) return 'جيد جداً';
    if (score >= 60) return 'جيد';
    if (score >= 45) return 'مقبول';
    return 'يحتاج تحسين';
  }
}

class DailyMetric {
  final DateTime date;
  final double value;

  DailyMetric({
    required this.date,
    required this.value,
  });

  factory DailyMetric.fromJson(Map<String, dynamic> json) {
    return DailyMetric(
      date: DateTime.parse(json['date']),
      value: (json['value'] ?? 0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'value': value,
    };
  }
}

class MonthlyMetric {
  final int year;
  final int month;
  final double views;
  final double bookings;
  final double revenue;
  final double occupancyRate;

  MonthlyMetric({
    required this.year,
    required this.month,
    required this.views,
    required this.bookings,
    required this.revenue,
    required this.occupancyRate,
  });

  factory MonthlyMetric.fromJson(Map<String, dynamic> json) {
    return MonthlyMetric(
      year: json['year'] ?? 0,
      month: json['month'] ?? 0,
      views: (json['views'] ?? 0).toDouble(),
      bookings: (json['bookings'] ?? 0).toDouble(),
      revenue: (json['revenue'] ?? 0).toDouble(),
      occupancyRate: (json['occupancy_rate'] ?? 0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'year': year,
      'month': month,
      'views': views,
      'bookings': bookings,
      'revenue': revenue,
      'occupancy_rate': occupancyRate,
    };
  }

  String get monthName {
    const months = [
      '', 'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    return months[month];
  }
}

class ComparisonMetrics {
  final double viewsChange;
  final double bookingsChange;
  final double revenueChange;
  final double conversionRateChange;
  final double occupancyRateChange;

  ComparisonMetrics({
    required this.viewsChange,
    required this.bookingsChange,
    required this.revenueChange,
    required this.conversionRateChange,
    required this.occupancyRateChange,
  });

  factory ComparisonMetrics.fromJson(Map<String, dynamic> json) {
    return ComparisonMetrics(
      viewsChange: (json['views_change'] ?? 0).toDouble(),
      bookingsChange: (json['bookings_change'] ?? 0).toDouble(),
      revenueChange: (json['revenue_change'] ?? 0).toDouble(),
      conversionRateChange: (json['conversion_rate_change'] ?? 0).toDouble(),
      occupancyRateChange: (json['occupancy_rate_change'] ?? 0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'views_change': viewsChange,
      'bookings_change': bookingsChange,
      'revenue_change': revenueChange,
      'conversion_rate_change': conversionRateChange,
      'occupancy_rate_change': occupancyRateChange,
    };
  }
}

class PerformanceInsight {
  final String type; // 'positive', 'negative', 'neutral'
  final String title;
  final String description;
  final String? actionText;
  final String? actionRoute;

  PerformanceInsight({
    required this.type,
    required this.title,
    required this.description,
    this.actionText,
    this.actionRoute,
  });

  factory PerformanceInsight.fromJson(Map<String, dynamic> json) {
    return PerformanceInsight(
      type: json['type'] ?? 'neutral',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      actionText: json['action_text'],
      actionRoute: json['action_route'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'title': title,
      'description': description,
      'action_text': actionText,
      'action_route': actionRoute,
    };
  }
}
