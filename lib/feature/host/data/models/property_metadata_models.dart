/// Models for property creation metadata (cancellation policies, property types, facilities)

class CancellationPolicy {
  final int id;
  final String name;
  final String nameAr;
  final String description;
  final String descriptionAr;
  final int refundDays;
  final double refundPercentage;
  final bool isActive;

  const CancellationPolicy({
    required this.id,
    required this.name,
    required this.nameAr,
    required this.description,
    required this.descriptionAr,
    required this.refundDays,
    required this.refundPercentage,
    this.isActive = true,
  });

  factory CancellationPolicy.fromJson(Map<String, dynamic> json) {
    return CancellationPolicy(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      nameAr: json['name_ar'] ?? '',
      description: json['description'] ?? '',
      descriptionAr: json['description_ar'] ?? '',
      refundDays: json['refund_days'] ?? 0,
      refundPercentage: (json['refund_percentage'] ?? 0.0).toDouble(),
      isActive: json['is_active'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'name_ar': nameAr,
      'description': description,
      'description_ar': descriptionAr,
      'refund_days': refundDays,
      'refund_percentage': refundPercentage,
      'is_active': isActive,
    };
  }

  static List<CancellationPolicy> getDefaultPolicies() {
    return [
      const CancellationPolicy(
        id: 1,
        name: 'Flexible',
        nameAr: 'مرن',
        description: 'Full refund 1 day prior to arrival',
        descriptionAr: 'استرداد كامل قبل يوم واحد من الوصول',
        refundDays: 1,
        refundPercentage: 100.0,
      ),
      const CancellationPolicy(
        id: 2,
        name: 'Moderate',
        nameAr: 'متوسط',
        description: 'Full refund 5 days prior to arrival',
        descriptionAr: 'استرداد كامل قبل 5 أيام من الوصول',
        refundDays: 5,
        refundPercentage: 100.0,
      ),
      const CancellationPolicy(
        id: 3,
        name: 'Strict',
        nameAr: 'صارم',
        description: 'Full refund 14 days prior to arrival',
        descriptionAr: 'استرداد كامل قبل 14 يوم من الوصول',
        refundDays: 14,
        refundPercentage: 100.0,
      ),
    ];
  }
}

class PropertyType {
  final int id;
  final String name;
  final String nameAr;
  final String description;
  final String descriptionAr;
  final String icon;
  final bool isActive;

  const PropertyType({
    required this.id,
    required this.name,
    required this.nameAr,
    required this.description,
    required this.descriptionAr,
    required this.icon,
    this.isActive = true,
  });

  factory PropertyType.fromJson(Map<String, dynamic> json) {
    return PropertyType(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      nameAr: json['name_ar'] ?? '',
      description: json['description'] ?? '',
      descriptionAr: json['description_ar'] ?? '',
      icon: json['icon'] ?? '',
      isActive: json['is_active'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'name_ar': nameAr,
      'description': description,
      'description_ar': descriptionAr,
      'icon': icon,
      'is_active': isActive,
    };
  }

  static List<PropertyType> getDefaultTypes() {
    return [
      const PropertyType(
        id: 1,
        name: 'Entire Place',
        nameAr: 'المكان بالكامل',
        description: 'Guests have the entire place to themselves',
        descriptionAr: 'الضيوف لديهم المكان بالكامل لأنفسهم',
        icon: 'home',
      ),
      const PropertyType(
        id: 2,
        name: 'Private Room',
        nameAr: 'غرفة خاصة',
        description: 'Guests have a private room in a shared space',
        descriptionAr: 'الضيوف لديهم غرفة خاصة في مساحة مشتركة',
        icon: 'bed',
      ),
      const PropertyType(
        id: 3,
        name: 'Shared Room',
        nameAr: 'غرفة مشتركة',
        description: 'Guests sleep in a room shared with others',
        descriptionAr: 'الضيوف ينامون في غرفة مشتركة مع آخرين',
        icon: 'people',
      ),
    ];
  }
}

class PropertyFacility {
  final int id;
  final String name;
  final String nameAr;
  final String icon;
  final String category;
  final String categoryAr;
  final bool isPopular;
  final bool isActive;

  const PropertyFacility({
    required this.id,
    required this.name,
    required this.nameAr,
    required this.icon,
    required this.category,
    required this.categoryAr,
    this.isPopular = false,
    this.isActive = true,
  });

  factory PropertyFacility.fromJson(Map<String, dynamic> json) {
    return PropertyFacility(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      nameAr: json['name_ar'] ?? '',
      icon: json['icon'] ?? '',
      category: json['category'] ?? '',
      categoryAr: json['category_ar'] ?? '',
      isPopular: json['is_popular'] ?? false,
      isActive: json['is_active'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'name_ar': nameAr,
      'icon': icon,
      'category': category,
      'category_ar': categoryAr,
      'is_popular': isPopular,
      'is_active': isActive,
    };
  }

  static List<PropertyFacility> getDefaultFacilities() {
    return [
      // Essential
      const PropertyFacility(
        id: 1,
        name: 'WiFi',
        nameAr: 'واي فاي',
        icon: 'wifi',
        category: 'Essential',
        categoryAr: 'أساسي',
        isPopular: true,
      ),
      const PropertyFacility(
        id: 2,
        name: 'Air Conditioning',
        nameAr: 'تكييف',
        icon: 'ac_unit',
        category: 'Essential',
        categoryAr: 'أساسي',
        isPopular: true,
      ),
      const PropertyFacility(
        id: 3,
        name: 'Kitchen',
        nameAr: 'مطبخ',
        icon: 'kitchen',
        category: 'Essential',
        categoryAr: 'أساسي',
        isPopular: true,
      ),
      const PropertyFacility(
        id: 4,
        name: 'Parking',
        nameAr: 'موقف سيارات',
        icon: 'local_parking',
        category: 'Essential',
        categoryAr: 'أساسي',
        isPopular: true,
      ),

      // Entertainment
      const PropertyFacility(
        id: 5,
        name: 'TV',
        nameAr: 'تلفزيون',
        icon: 'tv',
        category: 'Entertainment',
        categoryAr: 'ترفيه',
        isPopular: true,
      ),
      const PropertyFacility(
        id: 6,
        name: 'Netflix',
        nameAr: 'نتفليكس',
        icon: 'movie',
        category: 'Entertainment',
        categoryAr: 'ترفيه',
      ),

      // Outdoor
      const PropertyFacility(
        id: 7,
        name: 'Swimming Pool',
        nameAr: 'مسبح',
        icon: 'pool',
        category: 'Outdoor',
        categoryAr: 'خارجي',
        isPopular: true,
      ),
      const PropertyFacility(
        id: 8,
        name: 'Garden',
        nameAr: 'حديقة',
        icon: 'local_florist',
        category: 'Outdoor',
        categoryAr: 'خارجي',
      ),
      const PropertyFacility(
        id: 9,
        name: 'Balcony',
        nameAr: 'شرفة',
        icon: 'balcony',
        category: 'Outdoor',
        categoryAr: 'خارجي',
      ),

      // Safety & Security
      const PropertyFacility(
        id: 10,
        name: 'Security Camera',
        nameAr: 'كاميرا أمان',
        icon: 'security',
        category: 'Safety',
        categoryAr: 'أمان',
      ),
      const PropertyFacility(
        id: 11,
        name: 'Smoke Detector',
        nameAr: 'كاشف دخان',
        icon: 'smoke_detector',
        category: 'Safety',
        categoryAr: 'أمان',
      ),

      // Fitness & Wellness
      const PropertyFacility(
        id: 12,
        name: 'Gym',
        nameAr: 'صالة رياضية',
        icon: 'fitness_center',
        category: 'Fitness',
        categoryAr: 'لياقة',
      ),
      const PropertyFacility(
        id: 13,
        name: 'Spa',
        nameAr: 'سبا',
        icon: 'spa',
        category: 'Wellness',
        categoryAr: 'عافية',
      ),

      // Family Friendly
      const PropertyFacility(
        id: 14,
        name: 'Baby Crib',
        nameAr: 'سرير أطفال',
        icon: 'child_care',
        category: 'Family',
        categoryAr: 'عائلي',
      ),
      const PropertyFacility(
        id: 15,
        name: 'High Chair',
        nameAr: 'كرسي عالي',
        icon: 'chair',
        category: 'Family',
        categoryAr: 'عائلي',
      ),
    ];
  }

  static Map<String, List<PropertyFacility>> getFacilitiesByCategory() {
    final facilities = getDefaultFacilities();
    final Map<String, List<PropertyFacility>> categorized = {};

    for (final facility in facilities) {
      if (!categorized.containsKey(facility.category)) {
        categorized[facility.category] = [];
      }
      categorized[facility.category]!.add(facility);
    }

    return categorized;
  }
}
