import 'package:flutter/material.dart';
import 'package:gather_point/core/theme/theme_extensions.dart';

/// Enhanced card widget with theme-aware styling and animations
class EnhancedCard extends StatefulWidget {
  final Widget child;
  final VoidCallback? onTap;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double borderRadius;
  final bool withShadow;
  final bool withBorder;
  final Color? backgroundColor;
  final Color? borderColor;
  final double? elevation;
  final bool animateOnTap;
  final Duration animationDuration;

  const EnhancedCard({
    super.key,
    required this.child,
    this.onTap,
    this.padding = const EdgeInsets.all(16),
    this.margin,
    this.borderRadius = 16,
    this.withShadow = true,
    this.withBorder = false,
    this.backgroundColor,
    this.borderColor,
    this.elevation,
    this.animateOnTap = true,
    this.animationDuration = const Duration(milliseconds: 150),
  });

  @override
  State<EnhancedCard> createState() => _EnhancedCardState();
}

class _EnhancedCardState extends State<EnhancedCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    if (widget.onTap != null && widget.animateOnTap) {
      setState(() => _isPressed = true);
      _animationController.forward();
    }
  }

  void _handleTapUp(TapUpDetails details) {
    if (widget.onTap != null && widget.animateOnTap) {
      setState(() => _isPressed = false);
      _animationController.reverse();
    }
  }

  void _handleTapCancel() {
    if (widget.onTap != null && widget.animateOnTap) {
      setState(() => _isPressed = false);
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final backgroundColor = widget.backgroundColor ?? context.cardColor;
    final borderColor = widget.borderColor ?? context.borderColor;

    Widget cardContent = Container(
      margin: widget.margin,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border: widget.withBorder
            ? Border.all(color: borderColor, width: 1)
            : null,
        boxShadow: widget.withShadow ? context.cardShadow : null,
      ),
      child: widget.padding != null
          ? Padding(
              padding: widget.padding!,
              child: widget.child,
            )
          : widget.child,
    );

    if (widget.onTap != null) {
      cardContent = GestureDetector(
        onTap: widget.onTap,
        onTapDown: _handleTapDown,
        onTapUp: _handleTapUp,
        onTapCancel: _handleTapCancel,
        child: cardContent,
      );

      if (widget.animateOnTap) {
        cardContent = AnimatedBuilder(
          animation: _scaleAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimation.value,
              child: child,
            );
          },
          child: cardContent,
        );
      }
    }

    return cardContent;
  }
}

/// Enhanced card with loading state
class EnhancedLoadingCard extends StatelessWidget {
  final double height;
  final double borderRadius;
  final EdgeInsetsGeometry? margin;

  const EnhancedLoadingCard({
    super.key,
    this.height = 100,
    this.borderRadius = 16,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      margin: margin,
      decoration: BoxDecoration(
        color: context.cardColor,
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: context.cardShadow,
      ),
      child: Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(context.accentColor),
        ),
      ),
    );
  }
}

/// Enhanced card with error state
class EnhancedErrorCard extends StatelessWidget {
  final String message;
  final VoidCallback? onRetry;
  final double borderRadius;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;

  const EnhancedErrorCard({
    super.key,
    required this.message,
    this.onRetry,
    this.borderRadius = 16,
    this.margin,
    this.padding = const EdgeInsets.all(16),
  });

  @override
  Widget build(BuildContext context) {
    return EnhancedCard(
      margin: margin,
      padding: padding,
      borderRadius: borderRadius,
      backgroundColor: context.errorColor.withValues(alpha: 0.1),
      borderColor: context.errorColor.withValues(alpha: 0.3),
      withBorder: true,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.error_outline,
            color: context.errorColor,
            size: 32,
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: TextStyle(
              color: context.errorColor,
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
          if (onRetry != null) ...[
            const SizedBox(height: 12),
            TextButton(
              onPressed: onRetry,
              child: Text(
                'إعادة المحاولة',
                style: TextStyle(color: context.accentColor),
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// Enhanced card with empty state
class EnhancedEmptyCard extends StatelessWidget {
  final String message;
  final IconData? icon;
  final VoidCallback? onAction;
  final String? actionText;
  final double borderRadius;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;

  const EnhancedEmptyCard({
    super.key,
    required this.message,
    this.icon = Icons.inbox_outlined,
    this.onAction,
    this.actionText,
    this.borderRadius = 16,
    this.margin,
    this.padding = const EdgeInsets.all(24),
  });

  @override
  Widget build(BuildContext context) {
    return EnhancedCard(
      margin: margin,
      padding: padding,
      borderRadius: borderRadius,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (icon != null)
            Icon(
              icon,
              color: context.captionTextColor,
              size: 48,
            ),
          const SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(
              color: context.captionTextColor,
              fontSize: 16,
            ),
            textAlign: TextAlign.center,
          ),
          if (onAction != null && actionText != null) ...[
            const SizedBox(height: 16),
            TextButton(
              onPressed: onAction,
              child: Text(
                actionText!,
                style: TextStyle(color: context.accentColor),
              ),
            ),
          ],
        ],
      ),
    );
  }
}
