import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_colors.dart';

/// Extension on BuildContext for easy theme access
extension ThemeExtensions on BuildContext {
  /// Check if current theme is dark
  bool get isDarkMode => Theme.of(this).brightness == Brightness.dark;
  
  /// Get theme-aware primary text color
  Color get primaryTextColor {
    final theme = Theme.of(this);
    return theme.textTheme.bodyLarge?.color ?? 
           (theme.brightness == Brightness.dark ? AppColors.white : AppColors.black);
  }
  
  /// Get theme-aware secondary text color
  Color get secondaryTextColor {
    final isDark = Theme.of(this).brightness == Brightness.dark;
    return isDark ? AppColors.lightGrey : AppColors.darkGrey2;
  }
  
  /// Get theme-aware caption text color
  Color get captionTextColor {
    final isDark = Theme.of(this).brightness == Brightness.dark;
    return isDark ? AppColors.lightGrey3 : AppColors.darkGrey3;
  }
  
  /// Get theme-aware accent color (main yellow color)
  Color get accentColor => AppColors.yellow;
  
  /// Get theme-aware primary color (main yellow color)
  Color get primaryColor => AppColors.yellow;
  
  /// Get theme-aware card background color
  Color get cardColor => Theme.of(this).cardColor;
  
  /// Get theme-aware background color
  Color get backgroundColor {
    final isDark = Theme.of(this).brightness == Brightness.dark;
    return isDark ? AppColors.black : const Color(0xFFF5F6FA);
  }
  
  /// Get theme-aware surface color
  Color get surfaceColor => Theme.of(this).colorScheme.surface;
  
  /// Get theme-aware icon color
  Color get iconColor => Theme.of(this).iconTheme.color ?? primaryTextColor;
  
  /// Get theme-aware border color
  Color get borderColor {
    final isDark = Theme.of(this).brightness == Brightness.dark;
    return isDark ? AppColors.darkGrey : AppColors.lightGrey3;
  }
  
  /// Get theme-aware shadow color
  Color getShadowColor({double opacity = 0.1}) {
    final isDark = Theme.of(this).brightness == Brightness.dark;
    return isDark 
        ? Colors.black.withValues(alpha: opacity * 3)
        : Colors.black.withValues(alpha: opacity);
  }
  
  /// Get theme-aware success color
  Color get successColor => Colors.green;
  
  /// Get theme-aware warning color
  Color get warningColor => AppColors.yellow;
  
  /// Get theme-aware error color
  Color get errorColor => Colors.red;
  
  /// Get theme-aware info color
  Color get infoColor => Colors.blue;
  
  /// Get theme-aware divider color
  Color get dividerColor => Theme.of(this).dividerColor;
  
  /// Get theme-aware card shadow
  List<BoxShadow> get cardShadow {
    final isDark = Theme.of(this).brightness == Brightness.dark;
    return [
      BoxShadow(
        color: getShadowColor(opacity: isDark ? 0.3 : 0.08),
        blurRadius: 20,
        offset: const Offset(0, 8),
        spreadRadius: 0,
      ),
      BoxShadow(
        color: getShadowColor(opacity: isDark ? 0.2 : 0.04),
        blurRadius: 6,
        offset: const Offset(0, 2),
        spreadRadius: 0,
      ),
    ];
  }
  
  /// Get theme-aware overlay color
  Color getOverlayColor({double opacity = 0.1}) {
    final isDark = Theme.of(this).brightness == Brightness.dark;
    return isDark 
        ? Colors.white.withValues(alpha: opacity)
        : Colors.black.withValues(alpha: opacity);
  }
  
  /// Get adaptive color based on theme
  Color getAdaptiveColor({
    required Color lightColor,
    required Color darkColor,
  }) {
    final isDark = Theme.of(this).brightness == Brightness.dark;
    return isDark ? darkColor : lightColor;
  }
}
