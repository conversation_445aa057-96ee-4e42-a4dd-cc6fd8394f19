import 'package:shared_preferences/shared_preferences.dart';

class SharedPreferencesService {
  static const String _tokenKey = 'auth_token';
  static const String _userIdKey = 'user_id';
  static const String _userTypeKey = 'user_type';

  SharedPreferences? _prefs;

  Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  /// Get authentication token
  String? getToken() {
    return _prefs?.getString(_tokenKey);
  }

  /// Set authentication token
  Future<bool> setToken(String token) async {
    return await _prefs?.setString(_tokenKey, token) ?? false;
  }

  /// Remove authentication token
  Future<bool> removeToken() async {
    return await _prefs?.remove(_tokenKey) ?? false;
  }

  /// Get user ID
  int? getUserId() {
    return _prefs?.getInt(_userIdKey);
  }

  /// Set user ID
  Future<bool> setUserId(int userId) async {
    return await _prefs?.setInt(_userId<PERSON><PERSON>, userId) ?? false;
  }

  /// Get user type
  int? getUserType() {
    return _prefs?.getInt(_userTypeKey);
  }

  /// Set user type
  Future<bool> setUserType(int userType) async {
    return await _prefs?.setInt(_userTypeKey, userType) ?? false;
  }

  /// Clear all stored data
  Future<bool> clear() async {
    return await _prefs?.clear() ?? false;
  }

  /// Check if user is logged in
  bool get isLoggedIn => getToken() != null;
}
