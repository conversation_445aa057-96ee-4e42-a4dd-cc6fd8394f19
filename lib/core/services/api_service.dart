import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:gather_point/core/services/shared_preferences_service.dart';
import 'package:gather_point/core/services/service_locator.dart';

class ApiService {
  static const String baseUrl = 'https://backend.gatherpoint.sa/api';
  
  final SharedPreferencesService _sharedPreferencesService = getIt<SharedPreferencesService>();

  /// Get authorization headers
  Map<String, String> get _headers {
    final token = _sharedPreferencesService.getToken();
    return {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    };
  }

  /// GET request
  Future<http.Response> get(String endpoint) async {
    final url = Uri.parse('$baseUrl$endpoint');
    return await http.get(url, headers: _headers);
  }

  /// POST request
  Future<http.Response> post(String endpoint, Map<String, dynamic> data) async {
    final url = Uri.parse('$baseUrl$endpoint');
    return await http.post(
      url,
      headers: _headers,
      body: json.encode(data),
    );
  }

  /// PUT request
  Future<http.Response> put(String endpoint, Map<String, dynamic> data) async {
    final url = Uri.parse('$baseUrl$endpoint');
    return await http.put(
      url,
      headers: _headers,
      body: json.encode(data),
    );
  }

  /// DELETE request
  Future<http.Response> delete(String endpoint, [Map<String, dynamic>? data]) async {
    final url = Uri.parse('$baseUrl$endpoint');
    return await http.delete(
      url,
      headers: _headers,
      body: data != null ? json.encode(data) : null,
    );
  }

  /// PATCH request
  Future<http.Response> patch(String endpoint, Map<String, dynamic> data) async {
    final url = Uri.parse('$baseUrl$endpoint');
    return await http.patch(
      url,
      headers: _headers,
      body: json.encode(data),
    );
  }
}
