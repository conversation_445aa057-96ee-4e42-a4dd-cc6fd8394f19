class RoutesKeys {
  //*=======================Views=======================*//
  static const kSplashView = '/splashView';
  static const kNoInternetView = '/noInternetView';
  static const kOnBoardingView = '/onBoardingView';
  static const kVerifyOTPView = '/verifyOTPView';
  static const kLoginView = '/loginView';

  //*=======================Profile=======================*//
  static const kAccountSettings = '/account-settings';
  static const kHelp = '/help';
  static const kPrivacySettings = '/privacy-settings';
  static const kViewProfile = '/view-profile';
  static const kReferHost = '/refer-host';
  static const kLegal = '/legal';
  static const kNotificationSettings = '/notification-settings';

  //*=======================Tabs=======================*//
  static const kHomeViewTab = '/homeViewTab';
  static const kProfileViewTab = '/profileViewTab';
  static const kFavoritesViewTab = '/favoritesViewTab';
  static const kCartViewTab = '/cartViewTab';
  static const kSearchViewTab = '/searchViewTab';
  static const kListingViewTab = '/listingViewTab';
  static const kBookingsViewTab = '/bookingsViewTab';

  //*=======================Host=======================*//
  static const kMyListingsTab = '/myListingsTab';
  static const kCreateProperty = '/create-property';
  static const kEditProperty = '/edit-property';
  static const kPropertyAnalytics = '/property-analytics';
}
