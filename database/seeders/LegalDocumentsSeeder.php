<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\LegalDocument;

class LegalDocumentsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $documents = [
            [
                'type' => 'terms',
                'title_en' => 'Terms of Service',
                'title_ar' => 'شروط الخدمة',
                'content_en' => $this->getTermsContentEn(),
                'content_ar' => $this->getTermsContentAr(),
                'version' => '1.0',
                'effective_date' => now(),
                'is_active' => true,
            ],
            [
                'type' => 'privacy',
                'title_en' => 'Privacy Policy',
                'title_ar' => 'سياسة الخصوصية',
                'content_en' => $this->getPrivacyContentEn(),
                'content_ar' => $this->getPrivacyContentAr(),
                'version' => '1.0',
                'effective_date' => now(),
                'is_active' => true,
            ],
            [
                'type' => 'cookies',
                'title_en' => 'Cookie Policy',
                'title_ar' => 'سياسة ملفات تعريف الارتباط',
                'content_en' => $this->getCookiesContentEn(),
                'content_ar' => $this->getCookiesContentAr(),
                'version' => '1.0',
                'effective_date' => now(),
                'is_active' => true,
            ],
            [
                'type' => 'user_agreement',
                'title_en' => 'User Agreement',
                'title_ar' => 'اتفاقية المستخدم',
                'content_en' => $this->getUserAgreementContentEn(),
                'content_ar' => $this->getUserAgreementContentAr(),
                'version' => '1.0',
                'effective_date' => now(),
                'is_active' => true,
            ],
            [
                'type' => 'host_agreement',
                'title_en' => 'Host Agreement',
                'title_ar' => 'اتفاقية المضيف',
                'content_en' => $this->getHostAgreementContentEn(),
                'content_ar' => $this->getHostAgreementContentAr(),
                'version' => '1.0',
                'effective_date' => now(),
                'is_active' => true,
            ],
        ];

        foreach ($documents as $document) {
            LegalDocument::create($document);
        }
    }

    private function getTermsContentAr(): string
    {
        return "# شروط الخدمة

## 1. القبول بالشروط
بالوصول إلى منصة Gather Point واستخدامها، فإنك توافق على الالتزام بهذه الشروط والأحكام.

## 2. وصف الخدمة
Gather Point هي منصة تربط بين المضيفين والضيوف لحجز أماكن الإقامة قصيرة المدى.

## 3. التسجيل والحساب
- يجب أن تكون 18 عامًا أو أكثر للتسجيل
- يجب تقديم معلومات دقيقة وحديثة
- أنت مسؤول عن الحفاظ على سرية حسابك

## 4. الحجوزات والدفع
- جميع الحجوزات تخضع لتأكيد المضيف
- الدفع مطلوب وقت الحجز
- سياسات الإلغاء تختلف حسب العقار

## 5. مسؤوليات المضيف
- تقديم وصف دقيق للعقار
- الحفاظ على معايير النظافة والسلامة
- الاستجابة للضيوف في الوقت المناسب

## 6. مسؤوليات الضيف
- احترام العقار وقواعد المضيف
- الإبلاغ عن أي أضرار فورًا
- اتباع عدد الضيوف المتفق عليه

## 7. الرسوم
- رسوم الخدمة تطبق على جميع الحجوزات
- الرسوم غير قابلة للاسترداد

## 8. إنهاء الخدمة
نحتفظ بالحق في إنهاء أو تعليق حسابك في حالة انتهاك هذه الشروط.

## 9. تحديث الشروط
قد نحدث هذه الشروط من وقت لآخر. سيتم إشعارك بأي تغييرات مهمة.

## 10. الاتصال
للأسئلة حول هذه الشروط، يرجى الاتصال بنا على <EMAIL>";
    }

    private function getTermsContentEn(): string
    {
        return "# Terms of Service

## 1. Acceptance of Terms
By accessing and using the Gather Point platform, you agree to be bound by these terms and conditions.

## 2. Service Description
Gather Point is a platform that connects hosts and guests for short-term accommodation bookings.

## 3. Registration and Account
- You must be 18 years or older to register
- You must provide accurate and current information
- You are responsible for maintaining the confidentiality of your account

## 4. Bookings and Payment
- All bookings are subject to host confirmation
- Payment is required at the time of booking
- Cancellation policies vary by property

## 5. Host Responsibilities
- Provide accurate property descriptions
- Maintain cleanliness and safety standards
- Respond to guests in a timely manner

## 6. Guest Responsibilities
- Respect the property and host rules
- Report any damages immediately
- Follow agreed guest count

## 7. Fees
- Service fees apply to all bookings
- Fees are non-refundable

## 8. Termination
We reserve the right to terminate or suspend your account for violation of these terms.

## 9. Updates to Terms
We may update these terms from time to time. You will be notified of any material changes.

## 10. Contact
For questions about these terms, please contact <NAME_EMAIL>";
    }

    private function getPrivacyContentAr(): string
    {
        return "# سياسة الخصوصية

## 1. المعلومات التي نجمعها
نجمع المعلومات التي تقدمها لنا مباشرة، مثل:
- معلومات الحساب (الاسم، البريد الإلكتروني، رقم الهاتف)
- معلومات الملف الشخصي
- معلومات الحجز والدفع
- المراسلات والتقييمات

## 2. كيف نستخدم معلوماتك
نستخدم معلوماتك لـ:
- تقديم وتحسين خدماتنا
- معالجة الحجوزات والمدفوعات
- التواصل معك
- ضمان الأمان ومنع الاحتيال

## 3. مشاركة المعلومات
لا نبيع معلوماتك الشخصية. قد نشارك المعلومات مع:
- المضيفين والضيوف (حسب الضرورة للحجز)
- مقدمي الخدمات الخارجيين
- السلطات القانونية عند الضرورة

## 4. أمان البيانات
نتخذ تدابير أمنية مناسبة لحماية معلوماتك من الوصول غير المصرح به.

## 5. حقوقك
يحق لك:
- الوصول إلى معلوماتك الشخصية
- تصحيح المعلومات غير الدقيقة
- حذف حسابك
- الاعتراض على معالجة معينة

## 6. ملفات تعريف الارتباط
نستخدم ملفات تعريف الارتباط لتحسين تجربتك على المنصة.

## 7. التحديثات
قد نحدث هذه السياسة من وقت لآخر. سنخطرك بأي تغييرات مهمة.

## 8. الاتصال
للأسئلة حول الخصوصية، اتصل بنا على <EMAIL>";
    }

    private function getPrivacyContentEn(): string
    {
        return "# Privacy Policy

## 1. Information We Collect
We collect information you provide directly to us, such as:
- Account information (name, email, phone number)
- Profile information
- Booking and payment information
- Communications and reviews

## 2. How We Use Your Information
We use your information to:
- Provide and improve our services
- Process bookings and payments
- Communicate with you
- Ensure security and prevent fraud

## 3. Information Sharing
We do not sell your personal information. We may share information with:
- Hosts and guests (as necessary for bookings)
- Third-party service providers
- Legal authorities when required

## 4. Data Security
We implement appropriate security measures to protect your information from unauthorized access.

## 5. Your Rights
You have the right to:
- Access your personal information
- Correct inaccurate information
- Delete your account
- Object to certain processing

## 6. Cookies
We use cookies to improve your experience on our platform.

## 7. Updates
We may update this policy from time to time. We will notify you of any material changes.

## 8. Contact
For privacy questions, contact <NAME_EMAIL>";
    }

    private function getCookiesContentAr(): string
    {
        return "# سياسة ملفات تعريف الارتباط

## ما هي ملفات تعريف الارتباط؟
ملفات تعريف الارتباط هي ملفات نصية صغيرة يتم تخزينها على جهازك عند زيارة موقعنا.

## كيف نستخدم ملفات تعريف الارتباط
نستخدم ملفات تعريف الارتباط لـ:
- تذكر تفضيلاتك
- تحليل استخدام الموقع
- تحسين الأداء
- تخصيص المحتوى

## أنواع ملفات تعريف الارتباط
- **ضرورية**: مطلوبة لتشغيل الموقع
- **تحليلية**: تساعدنا في فهم كيفية استخدام الموقع
- **وظيفية**: تحسن تجربة المستخدم
- **إعلانية**: تخصص الإعلانات

## إدارة ملفات تعريف الارتباط
يمكنك التحكم في ملفات تعريف الارتباط من خلال إعدادات المتصفح.

## الاتصال
للأسئلة، اتصل بنا على <EMAIL>";
    }

    private function getCookiesContentEn(): string
    {
        return "# Cookie Policy

## What are Cookies?
Cookies are small text files stored on your device when you visit our website.

## How We Use Cookies
We use cookies to:
- Remember your preferences
- Analyze website usage
- Improve performance
- Personalize content

## Types of Cookies
- **Essential**: Required for website operation
- **Analytics**: Help us understand website usage
- **Functional**: Enhance user experience
- **Advertising**: Personalize advertisements

## Managing Cookies
You can control cookies through your browser settings.

## Contact
For questions, contact <NAME_EMAIL>";
    }

    private function getUserAgreementContentAr(): string
    {
        return "# اتفاقية المستخدم

## التزامات المستخدم
كمستخدم لمنصة Gather Point، تتعهد بـ:
- استخدام المنصة للأغراض المشروعة فقط
- عدم انتهاك حقوق الآخرين
- تقديم معلومات صحيحة ودقيقة
- احترام قوانين البلد المحلية

## الاستخدام المحظور
يُحظر استخدام المنصة لـ:
- الأنشطة غير القانونية
- التحايل على النظام
- إرسال محتوى ضار أو مسيء
- انتهاك حقوق الملكية الفكرية

## المسؤولية
أنت مسؤول عن جميع الأنشطة التي تتم من خلال حسابك.

## الإنهاء
يمكننا إنهاء حسابك في حالة انتهاك هذه الاتفاقية.";
    }

    private function getUserAgreementContentEn(): string
    {
        return "# User Agreement

## User Obligations
As a user of Gather Point platform, you agree to:
- Use the platform for lawful purposes only
- Not violate others' rights
- Provide accurate and truthful information
- Respect local laws and regulations

## Prohibited Use
You may not use the platform for:
- Illegal activities
- System manipulation
- Sending harmful or offensive content
- Violating intellectual property rights

## Responsibility
You are responsible for all activities conducted through your account.

## Termination
We may terminate your account for violation of this agreement.";
    }

    private function getHostAgreementContentAr(): string
    {
        return "# اتفاقية المضيف

## التزامات المضيف
كمضيف على منصة Gather Point، تتعهد بـ:
- تقديم وصف دقيق وصادق للعقار
- الحفاظ على معايير النظافة والسلامة
- الاستجابة للضيوف في الوقت المناسب
- احترام قوانين الضيافة المحلية

## معايير العقار
يجب أن يلبي عقارك:
- معايير السلامة الأساسية
- متطلبات النظافة
- الوصف المقدم في الإعلان
- التراخيص المطلوبة

## المدفوعات والرسوم
- سيتم دفع أرباحك وفقًا لجدولنا الزمني
- تطبق رسوم الخدمة على جميع الحجوزات
- أنت مسؤول عن الضرائب المطبقة

## الإلغاء والاسترداد
يجب اتباع سياسات الإلغاء المحددة في إعلانك.

## الإنهاء
يمكننا إنهاء حسابك في حالة انتهاك هذه الاتفاقية.";
    }

    private function getHostAgreementContentEn(): string
    {
        return "# Host Agreement

## Host Obligations
As a host on Gather Point platform, you agree to:
- Provide accurate and honest property descriptions
- Maintain cleanliness and safety standards
- Respond to guests in a timely manner
- Comply with local hospitality laws

## Property Standards
Your property must meet:
- Basic safety standards
- Cleanliness requirements
- Description provided in listing
- Required licenses

## Payments and Fees
- Your earnings will be paid according to our schedule
- Service fees apply to all bookings
- You are responsible for applicable taxes

## Cancellation and Refunds
You must follow the cancellation policies specified in your listing.

## Termination
We may terminate your account for violation of this agreement.";
    }
}
