<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\EarlyAccessFeature;

class EarlyAccessFeaturesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $features = [
            [
                'name_en' => 'AI-Powered Property Recommendations',
                'name_ar' => 'توصيات العقارات المدعومة بالذكاء الاصطناعي',
                'description_en' => 'Get personalized property recommendations based on your preferences and booking history using advanced AI algorithms.',
                'description_ar' => 'احصل على توصيات عقارات مخصصة بناءً على تفضيلاتك وتاريخ حجوزاتك باستخدام خوارزميات الذكاء الاصطناعي المتقدمة.',
                'icon' => 'ai',
                'status' => 'beta',
                'progress_percentage' => 85,
                'estimated_release_date' => '2025-03-15',
                'requirements' => json_encode([
                    ['type' => 'min_bookings', 'value' => 3]
                ]),
                'is_premium' => false,
                'order' => 1,
                'is_active' => true,
            ],
            [
                'name_en' => 'Advanced Analytics Dashboard',
                'name_ar' => 'لوحة التحليلات المتقدمة',
                'description_en' => 'Access detailed analytics about your property performance, guest behavior, and revenue optimization insights.',
                'description_ar' => 'احصل على تحليلات مفصلة حول أداء عقارك وسلوك الضيوف ورؤى تحسين الإيرادات.',
                'icon' => 'analytics',
                'status' => 'beta',
                'progress_percentage' => 70,
                'estimated_release_date' => '2025-04-01',
                'requirements' => json_encode([
                    ['type' => 'user_type', 'value' => 2],
                    ['type' => 'min_properties', 'value' => 1]
                ]),
                'is_premium' => true,
                'order' => 2,
                'is_active' => true,
            ],
            [
                'name_en' => 'Smart Pricing Automation',
                'name_ar' => 'أتمتة التسعير الذكي',
                'description_en' => 'Automatically adjust your property prices based on demand, seasonality, and market conditions to maximize revenue.',
                'description_ar' => 'اضبط أسعار عقارك تلقائياً بناءً على الطلب والموسمية وظروف السوق لتحقيق أقصى إيراد.',
                'icon' => 'automation',
                'status' => 'development',
                'progress_percentage' => 45,
                'estimated_release_date' => '2025-05-15',
                'requirements' => json_encode([
                    ['type' => 'user_type', 'value' => 2],
                    ['type' => 'min_properties', 'value' => 2]
                ]),
                'is_premium' => true,
                'order' => 3,
                'is_active' => true,
            ],
            [
                'name_en' => 'Third-Party Integrations',
                'name_ar' => 'التكامل مع الأطراف الثالثة',
                'description_en' => 'Connect your properties with popular booking platforms like Airbnb, Booking.com, and more for unified management.',
                'description_ar' => 'اربط عقاراتك مع منصات الحجز الشهيرة مثل Airbnb و Booking.com والمزيد للإدارة الموحدة.',
                'icon' => 'integration',
                'status' => 'coming_soon',
                'progress_percentage' => 25,
                'estimated_release_date' => '2025-06-30',
                'requirements' => json_encode([
                    ['type' => 'user_type', 'value' => 2]
                ]),
                'is_premium' => false,
                'order' => 4,
                'is_active' => true,
            ],
            [
                'name_en' => 'Enhanced Security Features',
                'name_ar' => 'ميزات الأمان المحسنة',
                'description_en' => 'Advanced security features including identity verification, background checks, and fraud detection.',
                'description_ar' => 'ميزات أمان متقدمة تشمل التحقق من الهوية وفحص الخلفية واكتشاف الاحتيال.',
                'icon' => 'security',
                'status' => 'development',
                'progress_percentage' => 60,
                'estimated_release_date' => '2025-07-15',
                'requirements' => json_encode([]),
                'is_premium' => true,
                'order' => 5,
                'is_active' => true,
            ],
            [
                'name_en' => 'Virtual Reality Property Tours',
                'name_ar' => 'جولات العقارات بالواقع الافتراضي',
                'description_en' => 'Create immersive VR tours of your properties to give guests a realistic preview before booking.',
                'description_ar' => 'أنشئ جولات واقع افتراضي غامرة لعقاراتك لمنح الضيوف معاينة واقعية قبل الحجز.',
                'icon' => 'ai',
                'status' => 'coming_soon',
                'progress_percentage' => 15,
                'estimated_release_date' => '2025-09-01',
                'requirements' => json_encode([
                    ['type' => 'user_type', 'value' => 2],
                    ['type' => 'min_properties', 'value' => 1]
                ]),
                'is_premium' => true,
                'order' => 6,
                'is_active' => true,
            ],
        ];

        foreach ($features as $feature) {
            EarlyAccessFeature::create($feature);
        }
    }
}
