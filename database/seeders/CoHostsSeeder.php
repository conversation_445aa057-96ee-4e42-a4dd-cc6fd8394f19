<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\CoHost;
use App\Models\User;

class CoHostsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Get hosts (user_type_id = 2)
        $hosts = User::where('user_type_id', 2)->limit(5)->get();
        
        if ($hosts->count() < 2) {
            $this->command->info('Not enough hosts found. Creating test hosts...');
            return;
        }

        // Create some co-host relationships
        $coHostRelationships = [
            [
                'host_id' => $hosts[0]->id,
                'co_host_id' => $hosts[1]->id,
                'property_id' => null, // All properties
                'status' => 'accepted',
                'permissions' => json_encode([
                    'manage_bookings',
                    'communicate_guests',
                    'update_calendar',
                    'view_earnings'
                ]),
                'commission_percentage' => 15.00,
                'message' => 'مرحباً، أود دعوتك للمساعدة في إدارة عقاراتي. لديك خبرة ممتازة في هذا المجال.',
                'accepted_at' => now()->subDays(10),
            ],
            [
                'host_id' => $hosts[1]->id,
                'co_host_id' => $hosts[2]->id,
                'property_id' => null,
                'status' => 'accepted',
                'permissions' => json_encode([
                    'manage_bookings',
                    'communicate_guests',
                    'update_calendar'
                ]),
                'commission_percentage' => 10.00,
                'message' => 'أحتاج مساعدة في إدارة الحجوزات والتواصل مع الضيوف.',
                'accepted_at' => now()->subDays(5),
            ],
            [
                'host_id' => $hosts[0]->id,
                'co_host_id' => $hosts[2]->id,
                'property_id' => null,
                'status' => 'pending',
                'permissions' => json_encode([
                    'manage_bookings',
                    'communicate_guests'
                ]),
                'commission_percentage' => 12.00,
                'message' => 'هل تود المساعدة في إدارة بعض عقاراتي؟ يمكننا مناقشة التفاصيل.',
                'accepted_at' => null,
            ],
            [
                'host_id' => $hosts[2]->id,
                'co_host_id' => $hosts[0]->id,
                'property_id' => null,
                'status' => 'pending',
                'permissions' => json_encode([
                    'manage_bookings',
                    'update_calendar',
                    'view_earnings'
                ]),
                'commission_percentage' => 20.00,
                'message' => 'أعجبني أسلوبك في إدارة العقارات، هل تود التعاون معي؟',
                'accepted_at' => null,
            ],
        ];

        foreach ($coHostRelationships as $relationship) {
            // Check if relationship already exists
            $exists = CoHost::where('host_id', $relationship['host_id'])
                           ->where('co_host_id', $relationship['co_host_id'])
                           ->where('property_id', $relationship['property_id'])
                           ->exists();

            if (!$exists) {
                CoHost::create($relationship);
            }
        }
    }
}
