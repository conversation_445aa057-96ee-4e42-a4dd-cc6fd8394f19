<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\HostingResource;

class HostingResourcesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $resources = [
            [
                'title_en' => 'Getting Started as a Host',
                'title_ar' => 'البدء كمضيف',
                'description_en' => 'Complete guide to start your hosting journey successfully',
                'description_ar' => 'دليل شامل لبدء رحلة الاستضافة بنجاح',
                'content_en' => $this->getGettingStartedContentEn(),
                'content_ar' => $this->getGettingStartedContentAr(),
                'category' => 'guide',
                'type' => 'article',
                'icon' => 'home',
                'order' => 1,
                'is_featured' => true,
                'is_active' => true,
            ],
            [
                'title_en' => 'Photography Tips for Your Listing',
                'title_ar' => 'نصائح التصوير لإعلانك',
                'description_en' => 'Professional photography tips to make your property stand out',
                'description_ar' => 'نصائح التصوير الاحترافي لجعل عقارك مميزاً',
                'content_en' => $this->getPhotographyTipsContentEn(),
                'content_ar' => $this->getPhotographyTipsContentAr(),
                'category' => 'tip',
                'type' => 'article',
                'icon' => 'camera_alt',
                'order' => 2,
                'is_featured' => true,
                'is_active' => true,
            ],
            [
                'title_en' => 'Legal Requirements for Hosts',
                'title_ar' => 'المتطلبات القانونية للمضيفين',
                'description_en' => 'Understanding legal obligations and requirements for hosting',
                'description_ar' => 'فهم الالتزامات والمتطلبات القانونية للاستضافة',
                'content_en' => $this->getLegalRequirementsContentEn(),
                'content_ar' => $this->getLegalRequirementsContentAr(),
                'category' => 'legal',
                'type' => 'guide',
                'icon' => 'gavel',
                'order' => 3,
                'is_featured' => false,
                'is_active' => true,
            ],
            [
                'title_en' => 'Marketing Your Property',
                'title_ar' => 'تسويق عقارك',
                'description_en' => 'Effective strategies to promote your property and increase bookings',
                'description_ar' => 'استراتيجيات فعالة للترويج لعقارك وزيادة الحجوزات',
                'content_en' => $this->getMarketingContentEn(),
                'content_ar' => $this->getMarketingContentAr(),
                'category' => 'marketing',
                'type' => 'article',
                'icon' => 'campaign',
                'order' => 4,
                'is_featured' => true,
                'is_active' => true,
            ],
            [
                'title_en' => 'Financial Management for Hosts',
                'title_ar' => 'الإدارة المالية للمضيفين',
                'description_en' => 'Managing your hosting income, expenses, and taxes',
                'description_ar' => 'إدارة دخل الاستضافة والمصروفات والضرائب',
                'content_en' => $this->getFinancialManagementContentEn(),
                'content_ar' => $this->getFinancialManagementContentAr(),
                'category' => 'finance',
                'type' => 'guide',
                'icon' => 'account_balance',
                'order' => 5,
                'is_featured' => false,
                'is_active' => true,
            ],
            [
                'title_en' => 'Safety and Security Guidelines',
                'title_ar' => 'إرشادات الأمان والحماية',
                'description_en' => 'Essential safety measures to protect your property and guests',
                'description_ar' => 'تدابير الأمان الأساسية لحماية عقارك وضيوفك',
                'content_en' => $this->getSafetyGuidelinesContentEn(),
                'content_ar' => $this->getSafetyGuidelinesContentAr(),
                'category' => 'safety',
                'type' => 'checklist',
                'icon' => 'security',
                'order' => 6,
                'is_featured' => true,
                'is_active' => true,
            ],
            [
                'title_en' => 'Property Maintenance Checklist',
                'title_ar' => 'قائمة صيانة العقار',
                'description_en' => 'Regular maintenance tasks to keep your property in top condition',
                'description_ar' => 'مهام الصيانة الدورية للحفاظ على عقارك في أفضل حالة',
                'content_en' => $this->getMaintenanceChecklistContentEn(),
                'content_ar' => $this->getMaintenanceChecklistContentAr(),
                'category' => 'maintenance',
                'type' => 'checklist',
                'icon' => 'build',
                'order' => 7,
                'is_featured' => false,
                'is_active' => true,
            ],
        ];

        foreach ($resources as $resource) {
            HostingResource::create($resource);
        }
    }

    private function getGettingStartedContentAr(): string
    {
        return "# البدء كمضيف

## مرحباً بك في عالم الاستضافة!

### الخطوة الأولى: إعداد ملفك الشخصي
- أضف صورة شخصية واضحة
- اكتب نبذة تعريفية جذابة
- أكمل جميع المعلومات المطلوبة

### الخطوة الثانية: إنشاء إعلان عقارك
- التقط صوراً عالية الجودة
- اكتب وصفاً مفصلاً ودقيقاً
- حدد الأسعار بشكل تنافسي

### الخطوة الثالثة: تحضير العقار
- تأكد من النظافة التامة
- وفر المرافق الأساسية
- أضف لمسات ترحيبية

### نصائح للنجاح
- كن متجاوباً مع الضيوف
- حافظ على معايير عالية
- اطلب التقييمات من الضيوف

### الدعم والمساعدة
فريقنا متاح دائماً لمساعدتك في رحلة الاستضافة.";
    }

    private function getGettingStartedContentEn(): string
    {
        return "# Getting Started as a Host

## Welcome to the world of hosting!

### Step 1: Set up your profile
- Add a clear profile picture
- Write an engaging bio
- Complete all required information

### Step 2: Create your property listing
- Take high-quality photos
- Write detailed and accurate descriptions
- Set competitive pricing

### Step 3: Prepare your property
- Ensure complete cleanliness
- Provide essential amenities
- Add welcoming touches

### Tips for success
- Be responsive to guests
- Maintain high standards
- Ask guests for reviews

### Support and help
Our team is always available to help you on your hosting journey.";
    }

    private function getPhotographyTipsContentAr(): string
    {
        return "# نصائح التصوير لإعلانك

## أهمية الصور في جذب الضيوف

### نصائح الإضاءة
- استخدم الضوء الطبيعي قدر الإمكان
- تجنب الظلال القاسية
- صور في أوقات النهار المشرقة

### زوايا التصوير
- صور من زوايا متعددة
- اعرض المساحات الواسعة
- ركز على التفاصيل المميزة

### ترتيب المكان
- نظف وأزل الفوضى
- رتب الأثاث بشكل جذاب
- أضف لمسات ديكورية بسيطة

### الصور المطلوبة
- صورة رئيسية جذابة
- جميع الغرف والمرافق
- المنطقة المحيطة

### أخطاء يجب تجنبها
- الصور المظلمة أو الضبابية
- الفوضى في الخلفية
- عدم إظهار المساحة الحقيقية";
    }

    private function getPhotographyTipsContentEn(): string
    {
        return "# Photography Tips for Your Listing

## Importance of photos in attracting guests

### Lighting tips
- Use natural light whenever possible
- Avoid harsh shadows
- Shoot during bright daylight hours

### Camera angles
- Shoot from multiple angles
- Show spacious areas
- Focus on unique features

### Staging the space
- Clean and declutter
- Arrange furniture attractively
- Add simple decorative touches

### Required photos
- Attractive main photo
- All rooms and amenities
- Surrounding area

### Mistakes to avoid
- Dark or blurry photos
- Clutter in the background
- Not showing the real space";
    }

    private function getLegalRequirementsContentAr(): string
    {
        return "# المتطلبات القانونية للمضيفين

## التراخيص والتصاريح
- ترخيص الاستضافة قصيرة المدى
- تصريح البلدية
- شهادة السلامة

## الالتزامات الضريبية
- تسجيل النشاط التجاري
- دفع ضريبة القيمة المضافة
- الإقرار الضريبي السنوي

## التأمين
- تأمين العقار
- تأمين المسؤولية المدنية
- تأمين محتويات العقار

## حقوق وواجبات المضيف
- حق رفض الحجز
- واجب توفير بيئة آمنة
- احترام خصوصية الضيوف

## القوانين المحلية
- قوانين الضوضاء
- قوانين الإشغال
- لوائح السلامة

تأكد من مراجعة القوانين المحلية في منطقتك.";
    }

    private function getLegalRequirementsContentEn(): string
    {
        return "# Legal Requirements for Hosts

## Licenses and permits
- Short-term rental license
- Municipal permit
- Safety certificate

## Tax obligations
- Business registration
- VAT payment
- Annual tax return

## Insurance
- Property insurance
- Liability insurance
- Contents insurance

## Host rights and duties
- Right to refuse bookings
- Duty to provide safe environment
- Respect guest privacy

## Local laws
- Noise regulations
- Occupancy laws
- Safety regulations

Make sure to check local laws in your area.";
    }

    private function getMarketingContentAr(): string
    {
        return "# تسويق عقارك

## كتابة وصف جذاب
- استخدم كلمات مفتاحية مهمة
- اذكر المميزات الفريدة
- كن صادقاً ودقيقاً

## تحسين الأسعار
- ادرس أسعار المنافسين
- استخدم التسعير الديناميكي
- قدم خصومات للإقامات الطويلة

## التفاعل مع الضيوف
- رد سريع على الاستفسارات
- كن ودوداً ومفيداً
- قدم توصيات محلية

## استخدام وسائل التواصل
- شارك صور عقارك
- انشر تجارب الضيوف
- تفاعل مع المجتمع المحلي

## طلب التقييمات
- اطلب من الضيوف كتابة تقييم
- رد على جميع التقييمات
- استخدم التقييمات للتحسين";
    }

    private function getMarketingContentEn(): string
    {
        return "# Marketing Your Property

## Writing attractive descriptions
- Use important keywords
- Mention unique features
- Be honest and accurate

## Pricing optimization
- Study competitor prices
- Use dynamic pricing
- Offer discounts for long stays

## Guest interaction
- Quick response to inquiries
- Be friendly and helpful
- Provide local recommendations

## Using social media
- Share photos of your property
- Post guest experiences
- Engage with local community

## Requesting reviews
- Ask guests to write reviews
- Respond to all reviews
- Use reviews for improvement";
    }

    private function getFinancialManagementContentEn(): string
    {
        return "# Financial Management for Hosts

## Income tracking
- Record all booking income
- Track seasonal patterns
- Monitor occupancy rates

## Expense management
- Property maintenance costs
- Cleaning and supplies
- Marketing expenses

## Tax considerations
- Keep detailed records
- Understand deductible expenses
- Consult with tax professional

## Pricing strategies
- Research market rates
- Consider seasonal adjustments
- Factor in all costs";
    }

    private function getFinancialManagementContentAr(): string
    {
        return "# الإدارة المالية للمضيفين

## تتبع الدخل
- سجل جميع إيرادات الحجوزات
- تتبع الأنماط الموسمية
- راقب معدلات الإشغال

## إدارة المصروفات
- تكاليف صيانة العقار
- التنظيف واللوازم
- مصاريف التسويق

## الاعتبارات الضريبية
- احتفظ بسجلات مفصلة
- افهم المصروفات المعفاة
- استشر محاسب ضرائب

## استراتيجيات التسعير
- ابحث في أسعار السوق
- فكر في التعديلات الموسمية
- احسب جميع التكاليف";
    }

    private function getSafetyGuidelinesContentEn(): string
    {
        return "# Safety and Security Guidelines

## Property security
- Install quality locks
- Provide emergency contacts
- Ensure proper lighting

## Guest safety
- Check smoke detectors
- Provide first aid kit
- Clear emergency exits

## Documentation
- Keep guest records
- Document property condition
- Report incidents promptly

## Emergency procedures
- Post emergency numbers
- Provide evacuation plan
- Train cleaning staff";
    }

    private function getSafetyGuidelinesContentAr(): string
    {
        return "# إرشادات الأمان والحماية

## أمان العقار
- ركب أقفال عالية الجودة
- وفر أرقام الطوارئ
- تأكد من الإضاءة المناسبة

## سلامة الضيوف
- افحص أجهزة إنذار الحريق
- وفر حقيبة إسعافات أولية
- اجعل مخارج الطوارئ واضحة

## التوثيق
- احتفظ بسجلات الضيوف
- وثق حالة العقار
- أبلغ عن الحوادث فوراً

## إجراءات الطوارئ
- اعرض أرقام الطوارئ
- وفر خطة الإخلاء
- درب طاقم التنظيف";
    }

    private function getMaintenanceChecklistContentEn(): string
    {
        return "# Property Maintenance Checklist

## Daily checks
- Clean common areas
- Check for damages
- Restock supplies

## Weekly maintenance
- Deep clean all rooms
- Check appliances
- Inspect safety equipment

## Monthly tasks
- HVAC system check
- Plumbing inspection
- Electrical safety check

## Seasonal maintenance
- Exterior cleaning
- Garden maintenance
- Weather preparation

## Annual inspections
- Professional safety check
- Insurance review
- Equipment replacement";
    }

    private function getMaintenanceChecklistContentAr(): string
    {
        return "# قائمة صيانة العقار

## الفحص اليومي
- تنظيف المناطق المشتركة
- فحص الأضرار
- تجديد اللوازم

## الصيانة الأسبوعية
- تنظيف عميق لجميع الغرف
- فحص الأجهزة
- تفقد معدات السلامة

## المهام الشهرية
- فحص نظام التكييف
- تفقد السباكة
- فحص السلامة الكهربائية

## الصيانة الموسمية
- تنظيف الواجهة الخارجية
- صيانة الحديقة
- التحضير للطقس

## الفحوصات السنوية
- فحص السلامة المهني
- مراجعة التأمين
- استبدال المعدات";
    }
}
