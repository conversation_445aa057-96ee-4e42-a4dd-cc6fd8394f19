<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\UserEarlyAccess;
use App\Models\EarlyAccessFeature;
use App\Models\User;

class UserEarlyAccessSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $users = User::limit(5)->get();
        $features = EarlyAccessFeature::all();
        
        if ($users->isEmpty() || $features->isEmpty()) {
            $this->command->info('No users or features found for early access enrollment.');
            return;
        }

        // Create some enrollments
        $enrollments = [
            [
                'user_id' => $users[0]->id,
                'feature_id' => $features[0]->id, // AI Recommendations
                'status' => 'enrolled',
                'feedback' => 'ميزة رائعة! التوصيات دقيقة جداً وساعدتني في العثور على عقارات مناسبة.',
                'rating' => 5,
                'enrolled_at' => now()->subDays(20),
                'last_used_at' => now()->subDays(2),
            ],
            [
                'user_id' => $users[0]->id,
                'feature_id' => $features[1]->id, // Analytics Dashboard
                'status' => 'enrolled',
                'feedback' => 'لوحة التحليلات مفيدة جداً لفهم أداء عقاراتي.',
                'rating' => 4,
                'enrolled_at' => now()->subDays(15),
                'last_used_at' => now()->subDays(1),
            ],
            [
                'user_id' => $users[1]->id,
                'feature_id' => $features[0]->id, // AI Recommendations
                'status' => 'enrolled',
                'feedback' => null,
                'rating' => null,
                'enrolled_at' => now()->subDays(10),
                'last_used_at' => now()->subDays(3),
            ],
            [
                'user_id' => $users[1]->id,
                'feature_id' => $features[3]->id, // Third-Party Integrations
                'status' => 'waitlist',
                'feedback' => null,
                'rating' => null,
                'enrolled_at' => null,
                'last_used_at' => null,
            ],
            [
                'user_id' => $users[2]->id,
                'feature_id' => $features[1]->id, // Analytics Dashboard
                'status' => 'enrolled',
                'feedback' => 'التحليلات مفصلة ومفيدة، لكن أتمنى لو كانت أسرع في التحديث.',
                'rating' => 4,
                'enrolled_at' => now()->subDays(8),
                'last_used_at' => now()->subHours(6),
            ],
            [
                'user_id' => $users[2]->id,
                'feature_id' => $features[4]->id, // Security Features
                'status' => 'enrolled',
                'feedback' => 'ميزات الأمان ممتازة وتعطي ثقة أكبر في التعامل.',
                'rating' => 5,
                'enrolled_at' => now()->subDays(5),
                'last_used_at' => now()->subHours(12),
            ],
        ];

        foreach ($enrollments as $enrollment) {
            // Check if enrollment already exists
            $exists = UserEarlyAccess::where('user_id', $enrollment['user_id'])
                                   ->where('feature_id', $enrollment['feature_id'])
                                   ->exists();

            if (!$exists) {
                UserEarlyAccess::create($enrollment);
            }
        }
    }
}
