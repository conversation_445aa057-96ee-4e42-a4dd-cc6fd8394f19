<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Friendship;
use App\Models\User;

class FriendshipsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Get some users to create friendships
        $users = User::limit(10)->get();
        
        if ($users->count() < 2) {
            // Create some test users if not enough exist
            $testUsers = [
                [
                    'full_name' => 'أحمد محمد',
                    'email' => '<EMAIL>',
                    'phone' => '+966501234567',
                    'user_type_id' => 1, // Client
                    'password' => bcrypt('password'),
                ],
                [
                    'full_name' => 'فاطمة علي',
                    'email' => '<EMAIL>',
                    'phone' => '+966501234568',
                    'user_type_id' => 2, // Host
                    'password' => bcrypt('password'),
                ],
                [
                    'full_name' => 'محمد السعد',
                    'email' => '<EMAIL>',
                    'phone' => '+966501234569',
                    'user_type_id' => 2, // Host
                    'password' => bcrypt('password'),
                ],
                [
                    'full_name' => 'نورا أحمد',
                    'email' => '<EMAIL>',
                    'phone' => '+966501234570',
                    'user_type_id' => 1, // Client
                    'password' => bcrypt('password'),
                ],
                [
                    'full_name' => 'خالد عبدالله',
                    'email' => '<EMAIL>',
                    'phone' => '+966501234571',
                    'user_type_id' => 2, // Host
                    'password' => bcrypt('password'),
                ],
            ];

            foreach ($testUsers as $userData) {
                User::firstOrCreate(
                    ['email' => $userData['email']],
                    $userData
                );
            }

            $users = User::limit(10)->get();
        }

        // Create some accepted friendships
        $friendships = [
            // Accepted friendships
            [
                'user_id' => $users[0]->id,
                'friend_id' => $users[1]->id,
                'status' => 'accepted',
                'accepted_at' => now()->subDays(30),
            ],
            [
                'user_id' => $users[0]->id,
                'friend_id' => $users[2]->id,
                'status' => 'accepted',
                'accepted_at' => now()->subDays(15),
            ],
            [
                'user_id' => $users[1]->id,
                'friend_id' => $users[3]->id,
                'status' => 'accepted',
                'accepted_at' => now()->subDays(7),
            ],
            
            // Pending friendships
            [
                'user_id' => $users[0]->id,
                'friend_id' => $users[4]->id,
                'status' => 'pending',
                'accepted_at' => null,
            ],
            [
                'user_id' => $users[2]->id,
                'friend_id' => $users[0]->id,
                'status' => 'pending',
                'accepted_at' => null,
            ],
        ];

        foreach ($friendships as $friendship) {
            // Check if friendship already exists
            $exists = Friendship::where(function ($query) use ($friendship) {
                $query->where('user_id', $friendship['user_id'])
                      ->where('friend_id', $friendship['friend_id']);
            })->orWhere(function ($query) use ($friendship) {
                $query->where('user_id', $friendship['friend_id'])
                      ->where('friend_id', $friendship['user_id']);
            })->exists();

            if (!$exists) {
                Friendship::create($friendship);
            }
        }
    }
}
