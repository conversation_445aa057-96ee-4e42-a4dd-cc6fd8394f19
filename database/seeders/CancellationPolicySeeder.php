<?php

namespace Database\Seeders;

use App\Models\Admin\CancellationPolicy;
use Illuminate\Database\Seeder;

class CancellationPolicySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $policies = [
            // Short-term policies (≤28 days)
            [
                'name_en' => 'Flexible - Short Term',
                'name_ar' => 'مرنة - قصيرة المدى',
                'description_en' => 'Full refund 1 day prior to arrival.',
                'description_ar' => 'استرداد كامل قبل يوم واحد من الوصول.',
                'policy_type' => 'flexible_short',
                'duration_type' => 'short',
                'cancellation_window_hours' => 24, // 1 day
                'refund_percentage' => 100.00,
                'booking_window_hours' => null,
                'minimum_notice_hours' => null,
                'service_fee_refundable' => false,
                'cleaning_fee_refundable' => true,
                'is_active' => true,
                'order' => 1,
            ],
            [
                'name_en' => 'Moderate - Short Term',
                'name_ar' => 'معتدلة - قصيرة المدى',
                'description_en' => 'Full refund 2 days prior to arrival.',
                'description_ar' => 'استرداد كامل قبل يومين من الوصول.',
                'policy_type' => 'moderate_short',
                'duration_type' => 'short',
                'cancellation_window_hours' => 48, // 2 days
                'refund_percentage' => 100.00,
                'booking_window_hours' => null,
                'minimum_notice_hours' => null,
                'service_fee_refundable' => false,
                'cleaning_fee_refundable' => true,
                'is_active' => true,
                'order' => 2,
            ],
            [
                'name_en' => 'Strict - Short Term',
                'name_ar' => 'مشددة - قصيرة المدى',
                'description_en' => 'Full refund 5 days prior to arrival.',
                'description_ar' => 'استرداد كامل قبل 5 أيام من الوصول.',
                'policy_type' => 'strict_short',
                'duration_type' => 'short',
                'cancellation_window_hours' => 120, // 5 days
                'refund_percentage' => 100.00,
                'booking_window_hours' => null,
                'minimum_notice_hours' => null,
                'service_fee_refundable' => false,
                'cleaning_fee_refundable' => false,
                'is_active' => true,
                'order' => 3,
            ],
            // Long-term policies (>28 days)
            [
                'name_en' => 'Moderate - Long Term',
                'name_ar' => 'معتدلة - طويلة المدى',
                'description_en' => '50% refund within 7 days of arrival date.',
                'description_ar' => 'استرداد 50% من المبلغ المدفوع في خلال 7 أيام من تاريخ الوصول.',
                'policy_type' => 'moderate_long',
                'duration_type' => 'long',
                'cancellation_window_hours' => 168, // 7 days
                'refund_percentage' => 50.00,
                'booking_window_hours' => null,
                'minimum_notice_hours' => null,
                'service_fee_refundable' => false,
                'cleaning_fee_refundable' => true,
                'is_active' => true,
                'order' => 4,
            ],
            [
                'name_en' => 'Strict - Long Term',
                'name_ar' => 'مشددة - طويلة المدى',
                'description_en' => 'Full refund if cancelled within 3 days of booking and at least 14 days before arrival.',
                'description_ar' => 'المبلغ المدفوع يتم رده في حالة إلغاء الحجز في خلال ثلاثة أيام من تاريخ الحجز على أن يكون الإلغاء قبل موعد الحجز بمدة لا تقل عن 14 يوم.',
                'policy_type' => 'strict_long',
                'duration_type' => 'long',
                'cancellation_window_hours' => 0, // Not used for this policy type
                'refund_percentage' => 100.00,
                'booking_window_hours' => 72, // 3 days after booking
                'minimum_notice_hours' => 336, // 14 days before arrival
                'service_fee_refundable' => false,
                'cleaning_fee_refundable' => false,
                'is_active' => true,
                'order' => 5,
            ],
        ];

        foreach ($policies as $policy) {
            CancellationPolicy::create($policy);
        }
    }
}
