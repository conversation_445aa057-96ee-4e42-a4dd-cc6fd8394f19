<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_early_access', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('feature_id');
            $table->enum('status', ['enrolled', 'waitlist', 'declined', 'removed'])->default('enrolled');
            $table->text('feedback')->nullable(); // User feedback on the feature
            $table->integer('rating')->nullable(); // 1-5 rating
            $table->timestamp('enrolled_at')->nullable();
            $table->timestamp('last_used_at')->nullable();
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('feature_id')->references('id')->on('early_access_features')->onDelete('cascade');

            // Indexes
            $table->index(['user_id', 'status']);
            $table->index(['feature_id', 'status']);
            
            // Ensure unique enrollment per user per feature
            $table->unique(['user_id', 'feature_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_early_access');
    }
};
