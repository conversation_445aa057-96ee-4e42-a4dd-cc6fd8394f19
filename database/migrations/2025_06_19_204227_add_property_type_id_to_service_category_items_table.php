<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('service_category_items', function (Blueprint $table) {
            $table->foreignId('property_type_id')->nullable()->after('id')->constrained('property_types', 'id')->onDelete('no action')->onUpdate('no action');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('service_category_items', function (Blueprint $table) {
            $table->dropColumn('property_type_id');
        });
    }
};
