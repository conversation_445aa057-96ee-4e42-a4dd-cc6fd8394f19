<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('co_hosts', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('host_id'); // The main host
            $table->unsignedBigInteger('co_host_id'); // The co-host
            $table->unsignedBigInteger('property_id')->nullable(); // Specific property (null = all properties)
            $table->enum('status', ['pending', 'accepted', 'declined', 'removed'])->default('pending');
            $table->json('permissions')->nullable(); // JSON array of permissions
            $table->decimal('commission_percentage', 5, 2)->default(0); // Co-host commission percentage
            $table->text('message')->nullable(); // Message from host when inviting
            $table->timestamp('accepted_at')->nullable();
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('host_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('co_host_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('property_id')->references('id')->on('service_category_items')->onDelete('cascade');

            // Indexes for better performance
            $table->index(['host_id', 'status']);
            $table->index(['co_host_id', 'status']);
            $table->index(['property_id', 'status']);
            
            // Ensure unique co-host relationships per property
            $table->unique(['host_id', 'co_host_id', 'property_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('co_hosts');
    }
};
