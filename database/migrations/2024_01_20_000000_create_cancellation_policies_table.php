<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cancellation_policies', function (Blueprint $table) {
            $table->id();
            $table->string('name_en');
            $table->string('name_ar');
            $table->text('description_en');
            $table->text('description_ar');
            $table->enum('policy_type', ['flexible_short', 'moderate_short', 'strict_short', 'moderate_long', 'strict_long', 'custom'])->default('moderate_short');
            $table->enum('duration_type', ['short', 'long', 'both'])->default('short'); // short ≤28 days, long >28 days
            $table->integer('cancellation_window_hours')->default(24); // Hours before check-in
            $table->decimal('refund_percentage', 5, 2)->default(100.00); // 0-100%
            $table->integer('booking_window_hours')->nullable(); // Hours after booking for special rules
            $table->integer('minimum_notice_hours')->nullable(); // Minimum hours before arrival
            $table->boolean('service_fee_refundable')->default(true);
            $table->boolean('cleaning_fee_refundable')->default(true);
            $table->boolean('is_active')->default(true);
            $table->integer('order')->default(0);
            $table->timestamps();
            $table->softDeletes();

            $table->index(['is_active', 'order']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cancellation_policies');
    }
};
