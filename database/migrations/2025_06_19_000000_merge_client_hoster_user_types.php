<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Update all users with user_type_id = 3 (Service Provider/Hoster) 
        // to user_type_id = 2 (Client) and set is_hoster_mode = true
        DB::table('users')
            ->where('user_type_id', 3)
            ->update([
                'user_type_id' => 2,
                'is_hoster_mode' => true,
                'updated_at' => now()
            ]);

        // Ensure all existing users with user_type_id = 2 have is_hoster_mode = false
        // (in case some don't have this field set properly)
        DB::table('users')
            ->where('user_type_id', 2)
            ->whereNull('is_hoster_mode')
            ->orWhere('is_hoster_mode', false)
            ->update([
                'is_hoster_mode' => false,
                'updated_at' => now()
            ]);

        // Log the migration results
        $updatedHosters = DB::table('users')->where('user_type_id', 2)->where('is_hoster_mode', true)->count();
        $regularUsers = DB::table('users')->where('user_type_id', 2)->where('is_hoster_mode', false)->count();
        
        \Log::info("User type migration completed:", [
            'hosters_converted' => $updatedHosters,
            'regular_users' => $regularUsers,
            'total_users_type_2' => $updatedHosters + $regularUsers
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Revert users with is_hoster_mode = true back to user_type_id = 3
        DB::table('users')
            ->where('user_type_id', 2)
            ->where('is_hoster_mode', true)
            ->update([
                'user_type_id' => 3,
                'is_hoster_mode' => false, // Reset hoster mode since we're using user_type_id again
                'updated_at' => now()
            ]);

        \Log::info("User type migration reverted: Hosters moved back to user_type_id = 3");
    }
};
