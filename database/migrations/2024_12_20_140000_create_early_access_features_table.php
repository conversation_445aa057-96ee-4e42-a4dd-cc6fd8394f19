<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('early_access_features', function (Blueprint $table) {
            $table->id();
            $table->string('name_en');
            $table->string('name_ar');
            $table->text('description_en');
            $table->text('description_ar');
            $table->string('icon')->nullable(); // Icon name or URL
            $table->enum('status', ['development', 'beta', 'coming_soon', 'released'])->default('development');
            $table->integer('progress_percentage')->default(0); // 0-100
            $table->date('estimated_release_date')->nullable();
            $table->json('requirements')->nullable(); // JSON array of requirements
            $table->boolean('is_premium')->default(false); // Premium feature flag
            $table->integer('order')->default(0); // Display order
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            // Indexes
            $table->index(['status', 'is_active']);
            $table->index(['order', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('early_access_features');
    }
};
